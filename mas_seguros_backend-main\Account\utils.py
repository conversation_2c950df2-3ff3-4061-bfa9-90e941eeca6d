from random import randint, choice
import string
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.template.loader import render_to_string
from rest_framework.response import Response
from rest_framework_jwt.settings import api_settings
from rest_framework import pagination, status

from mas_seguros_backend import prefixes as backend_prefixes

from . import urls as view_urls
from threading import Thread
from .models import UserProfile
from . import models as account_models
from .models import User, UserProfile
from mas_seguros_backend import utils as backend_utils


def get_user_profile(username=None, code=None, email=None):
    try:
        if username and code:
            return UserProfile.objects.filter(user__username=username, verification_code=code).last()
        if email and code:
            return UserProfile.objects.filter(user__email=email, verification_code=code).last()
        if username:
            return UserProfile.objects.filter(user__username=username).last()
        if email:
            print("userrrr==========")
            return UserProfile.objects.filter(user__email=email).last()
        if code:
            return UserProfile.objects.filter(verification_code=code).last()

        backend_utils.logger("Username or email not provided!")
        return None
    except Exception as e:
        print("er0rrrrrrrrrrrrrrrrrrr")
        return None


def get_company_from_user(user):
    """
    get company object from authenticated user
    param: user
    """
    try:
        return user.userprofile.employeeprofilemodel.company
    except:
        return user.userprofile.personalclientprofilemodel.company

    # finally:
    #     return user.userprofile.personalclientprofilemodel.company


def get_token(user):
    jwt_payload_handler = api_settings.JWT_PAYLOAD_HANDLER
    jwt_encode_handler = api_settings.JWT_ENCODE_HANDLER
    payload = jwt_payload_handler(user)
    return jwt_encode_handler(payload)


def random_digits():
    range_start = 10 ** (4 - 1)
    range_end = (10 ** 4) - 1
    return randint(range_start, range_end)


def generate_random_password(length=8):
    """
    Generate a random password with the specified length
    containing lowercase, uppercase, digits, and special characters
    """
    # Define character sets
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*"

    # Ensure at least one character from each set
    password = [
        choice(lowercase),
        choice(uppercase),
        choice(digits),
        choice(special_chars)
    ]

    # Fill the rest of the password
    remaining_length = length - 4
    all_chars = lowercase + uppercase + digits + special_chars
    password.extend(choice(all_chars) for _ in range(remaining_length))

    # Shuffle the password characters
    import random
    random.shuffle(password)

    # Convert list to string
    return ''.join(password)


def get_full_url(request, path):
    return "{}://{}{}".format(request.scheme, request.get_host(), path)


def thread_making(target, arguments: list):
    t = Thread(target=target,
               args=arguments)
    t.setDaemon(True)
    t.start()


def get_user(username: str = None, email: str = None):
    try:
        if username:
            return User.objects.get(username=username.lower())
        if email:
            return User.objects.get(email=email.lower())
        backend_utils.logger("Username or email not provided!")
        return None
    except User.DoesNotExist as exep:
        backend_utils.logger(str(exep))
        return None


def create_user_profile(user: User, email_verified=False):
    return UserProfile.objects.create(user=user, verification_code=random_digits(), email_verified=email_verified)


def generate_prefix_number():
    new_ui_id = backend_prefixes.get_new_prefix_number_for_user_profile(
        _model=account_models.UserProfile,
        field_name='ui_id',
        prefix=backend_prefixes.USER_UI)
    return new_ui_id


def validate_email_phone(email, phone):
    lower_email = email.lower()
    if User.objects.filter(email__iexact=lower_email).exists():
        return False, "Este correo electrónico ya está registrado en otra cuenta!"

    if account_models.UserProfile.objects.filter(phone__exact=phone).exists():
        return False, "Este número de teléfono ya está registrado en otra cuenta! "
    return True, 'OK'


def get_phone_numbers(phone_numbers):
    phone_numbers_list = list()
    for i in phone_numbers:
        phone_numbers_list.append(i['phone'])
    return phone_numbers_list


def replace_scheme(url: str):
    if url and not (url.__contains__('localhost') or url.__contains__('127.0.0.1')):
        url = url.replace('http', 'https')
    return url


class CustomPagination(pagination.PageNumberPagination):
    def get_paginated_response(self, data):
        data = backend_utils.success_response(data={
            'links': {
                'next': replace_scheme(self.get_next_link()),
                'previous': replace_scheme(self.get_previous_link())
            },
            'count': self.page.paginator.count,
            'results': data
        }, status_code=status.HTTP_200_OK)
        return Response(data)


def get_paginated_response_without_serializer(query_set=None, request=None, page_size=10):
    paginator = CustomPagination()
    paginator.page_size = page_size
    result_page = paginator.paginate_queryset(query_set, request)
    return paginator.get_paginated_response(result_page)
