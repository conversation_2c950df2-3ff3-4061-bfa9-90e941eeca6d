from django.urls import path
from . import views as roles_views

urlpatterns = [
    # Legacy endpoints
    path('userprofile/', roles_views.GetUserProfile.as_view(), name='get-user-profile'),
    path('editprofile/', roles_views.EditUserProfile.as_view(), name='edit-user-profile'),

    # New admin management endpoints
    path('admins/', roles_views.GetAllAdmins.as_view(), name='get-all-admins'),
    path('admin/create/', roles_views.CreateAdmin.as_view(), name='create-admin'),
    path('admin/update/', roles_views.UpdateAdmin.as_view(), name='update-admin'),
    path('admin/suspend/', roles_views.SuspendAdmin.as_view(), name='suspend-admin'),
    path('admin/delete/', roles_views.DeleteAdmin.as_view(), name='delete-admin'),
    path('admin/change-password/', roles_views.ChangeAdminPassword.as_view(), name='change-admin-password'),
    path('admin/statistics/', roles_views.GetAdminStatistics.as_view(), name='get-admin-statistics'),
]
