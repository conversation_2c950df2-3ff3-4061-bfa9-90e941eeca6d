from rest_framework import serializers
from AdminSide.AdminAPi import models as admin_model
from Account import models as account_models, serializers as account_serializers
from AdminSide.CompanyDashboard import serializers as company_serializer, models as company_models
from Shield import models as shield_models
from django.contrib.auth.models import User


class AminUserProfileSerializer(serializers.ModelSerializer):
    user = account_serializers.UserSerializer()

    class Meta:
        model = account_models.UserProfile
        # fields = '__all__'
        exclude = ['ui_id']


class AdminLoginSerializer(serializers.Serializer):
    email_address = serializers.EmailField(required=True, error_messages={
        'required': 'Campo requerido',
        'invalid': 'Formato de correo electrónico incorrecto'
    })
    password = serializers.CharField(required=True, style={'input_type': 'password'}, error_messages={
        'required': 'Campo requerido'
    })

    def validate_email_address(self, value):
        if not value:
            raise serializers.ValidationError("Campo requerido")

        lower_email = value.lower().strip()

        # Check email format more strictly
        import re
        email_pattern = r'^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$'
        if not re.match(email_pattern, lower_email, re.IGNORECASE):
            raise serializers.ValidationError("Formato de correo electrónico incorrecto")

        if not User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Correo electrónico no encontrado")
        return lower_email

    def validate_password(self, value):
        if not value:
            raise serializers.ValidationError("Campo requerido")
        if len(value) < 6:
            raise serializers.ValidationError("La contraseña debe tener al menos 6 caracteres")
        return value

    def validate(self, data):
        email = data.get('email_address')
        password = data.get('password')

        # If email exists and password is valid length, check if credentials are correct
        if email and password and len(password) >= 6:
            try:
                user = User.objects.get(email__iexact=email)
                if not user.check_password(password):
                    raise serializers.ValidationError("Correo electrónico o contraseña incorrectos")
            except User.DoesNotExist:
                # This should be caught by validate_email_address, but just in case
                raise serializers.ValidationError("Correo electrónico no encontrado")

        return data


class AdminPasswordRecoverySerializer(serializers.Serializer):
    email = serializers.EmailField(
        required=True, 
        allow_blank=False,
        error_messages={
            'required': 'Este campo es requerido.',
            'invalid': 'Ingrese una dirección de correo electrónico válida.',
            'blank': 'Este campo no puede estar vacío.'
        }
    )

    def validate_email(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Este campo no puede estar vacío.")

        lower_email = value.lower().strip()

        # Check email format
        import re
        email_pattern = r'^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$'
        if not re.match(email_pattern, lower_email, re.IGNORECASE):
            raise serializers.ValidationError("Ingrese una dirección de correo electrónico válida.")

        # Check if user exists and is an admin
        try:
            user = User.objects.get(email__iexact=lower_email)
            user_profile = user.userprofile
            if not user_profile.role == account_models.web_admin and not user.is_superuser:
                raise serializers.ValidationError("Correo electrónico no encontrado")
        except User.DoesNotExist:
            raise serializers.ValidationError("Correo electrónico no encontrado")
        except:
            raise serializers.ValidationError("Correo electrónico no encontrado")

        return lower_email


class PromoCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = admin_model.PromoCode
        fields = "__all__"

    def validate_email(self, value):
        lower_email = value.lower()
        if admin_model.PromoCode.objects.filter(promo_code__iexact=value).exists():
            raise serializers.ValidationError("This promo code is already in use!")
        return value


class UseradminProfileSerializerForDashboard(serializers.ModelSerializer):
    user = company_serializer.UserSerializer()
    number_of_shields = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField(required=False)

    def get_number_of_shields(self, record):
        shield_count = shield_models.ShieldModel.objects.filter(members__user=record.user).count()
        return shield_count

    def get_user(self, record):
        record: account_models.UserProfile
        return record.user.all().count()

    class Meta:
        model = account_models.UserProfile
        fields = "__all__"


# edit password

class ChangePasswordSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True)
    password2 = serializers.CharField(write_only=True, required=True)
    old_password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ('old_password', 'password', 'password2')

    def validate(self, attrs):
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})

        return attrs

    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError({"old_password": "Old password is not correct"})
        return value

    def update(self, instance, validated_data):

        instance.set_password(validated_data['password'])
        instance.save()

        return instance


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'phone', 'full_name', 'identification_card', 'birth_date', 'role', 'lat',
                  'long', 'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend',
                  'created_at', 'updated_at', 'image_url'] + ['image_url', ]


class AdminSerializer(serializers.ModelSerializer):
    admin = UserProfileSerializer(many=True)

    # users = UserProfileSerializer(many=True)
    # promocode = PromoCodeSerializer(many=True)

    class Meta:
        model = company_models.CompanyProfileModel
        fields = ["admin", ]


class UserProfileSerializerForRoles(serializers.ModelSerializer):
    class Meta:
        model = account_models.UserProfile
        exclude = ['verification_code', 'user']
        # fields = "__all__"
