{"data_mtime": 1753275809, "dep_lines": [3, 1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 5, 30, 30, 30], "dependencies": ["Shield.models", "string", "random", "Shield", "math", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "4499923f37636b6f2ea9abe8e618fec735d4361b", "id": "Shield.utils", "ignore_all": true, "interface_hash": "d8c8175158c253d40a2bfdb95c87013fb275fa4b", "mtime": 1752062659, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\utils.py", "plugin_data": null, "size": 8597, "suppressed": [], "version_id": "1.15.0"}