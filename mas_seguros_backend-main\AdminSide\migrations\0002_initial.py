# Generated by Django 5.2.3 on 2025-06-14 11:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
        ('AdminSide', '0001_initial'),
        ('Shield', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='companyprofilemodel',
            name='shields',
            field=models.ManyToManyField(blank=True, to='Shield.shieldmodel'),
        ),
        migrations.AddField(
            model_name='companyprofilemodel',
            name='users',
            field=models.ManyToManyField(blank=True, related_name='users_to_users', to='Account.userprofile'),
        ),
        migrations.AddField(
            model_name='promocode',
            name='company',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='AdminSide.companyprofilemodel'),
        ),
        migrations.AddField(
            model_name='superadmin',
            name='userprofile',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile'),
        ),
        migrations.AddField(
            model_name='companyprofilemodel',
            name='super_admin',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='AdminSide.superadmin'),
        ),
    ]
