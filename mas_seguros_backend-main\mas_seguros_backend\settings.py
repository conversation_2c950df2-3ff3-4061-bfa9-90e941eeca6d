"""
Django settings for mas_seguros_backend project.

Generated by 'django-admin startproject' using Django 4.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""
import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-1-@j^(#&*uuzmn7_vwfx(bhy$$z4qd=+l*^za#hzvdnzb&qhul'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', False)

ALLOWED_HOSTS = ['*', 'https://masseguros.limacreativa.com', "http://************:3000", "http://************:3000" ]

# Application definition
other_apps = [
    'Account',
    'Sos',
    'Alert',
    # 'AdminAPi',
    'Membership',
    'Shield',
    'Faq',
    'About',
    'Ticket',
    'AdminSide',
    'rest_framework.pagination',
    # 'fcm_django'
    # 'push_notifications',
    # 'ControlPanelDashboard',
    # 'CompanyDashboard'

]
INSTALLED_APPS = [
                     'django.contrib.admin',
                     'django.contrib.auth',
                     'django.contrib.contenttypes',
                     'django.contrib.sessions',
                     'django.contrib.messages',
                     'django.contrib.staticfiles',
                     'rest_framework_simplejwt',
                     'rest_framework.authtoken',
                     'corsheaders',
                     'rest_framework',
                     'django_filters',
                 ] + other_apps

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # CorsMiddleware should be placed before CommonMiddleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]


ROOT_URLCONF = 'mas_seguros_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    # "DATE_INPUT_FORMATS": ["%d/%m/%Y"],
}

WSGI_APPLICATION = 'mas_seguros_backend.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME', 'masbackend'),
        'USER': os.environ.get('DB_USER', ''),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', ''),
        'PORT': os.environ.get('DB_PORT', ''),
    }
}
# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Karachi'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

# Base_url_path = 'https://masseguros.limacreativa.com{url}'
Base_url_path = 'http://************:8000{url}'
Base_url_path_local = 'http://127.0.0.1{url}'
# Base_url_path = 'http://***************{url}'
# Base_url_path_local = 'http://127.0.0.1{url}'
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
MEDIA_DIR = os.path.join(BASE_DIR, 'media')

STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

MEDIA_ROOT = MEDIA_DIR
MEDIA_URL = '/media/'
# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'



EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_PORT = 587
# EMAIL_HOST_USER = '<EMAIL>'
# EMAIL_HOST_PASSWORD = 'nlmevmcpwkfqguxy'
# Email settings
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
SENDGRID_API_KEY = os.environ.get('SENDGRID_API_KEY', '')
from_email = EMAIL_HOST_USER

# allow localhost 3000 to acced apis
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3000',
    "http://127.0.0.1:8000",
    "https://seguros.netlify.app",
    "http://************:3000",
    "http://************",
    "http://************:3000",
    "http://************"
]
CORS_ALLOWED_ORIGIN_REGEXES = [
    'http://localhost:3000',
    "https://seguros.netlify.app",
    "http://************:3000",
    "http://************",
    "http://************:3000",
    "http://************"

]
# CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = True
CORS_ORIGIN_ALLOW_ALL = True

# Additional CORS headers for file downloads
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'content-disposition',
]

CORS_EXPOSE_HEADERS = [
    'content-disposition',
    'content-type',
    'content-length',
]

CORS_ORIGIN_WHITELIST = (
    'http://localhost:3000',  # for localhost (REACT Default)
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "https://seguros.netlify.app",
    "http://************:3000",
    "http://************",
    "http://************:3000",
    "http://************"
    # "https://masseguros.limacreativa.com",
)

#
# FCM_DJANGO_SETTINGS = {
#     "APP_VERBOSE_NAME": "Mas Seguros",
#     "FCM_SERVER_KEY": "",
# }
CSRF_TRUSTED_ORIGINS = ["https://masseguros.limacreativa.com", 'https://*.127.0.0.1', "http://************",  'http://************:3000', "http://************:3000", "http://************"]



# ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

AUTHENTICATION_BACKENDS = [
    'Account.authentication.MultiAppAuthenticationBackend',
    'django.contrib.auth.backends.ModelBackend',
]
