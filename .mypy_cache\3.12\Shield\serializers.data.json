{".class": "MypyFile", "_fullname": "Shield.serializers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddMemberToShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.AddMemberToShieldSerializer", "name": "AddMemberToShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.AddMemberToShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.AddMemberToShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddMemberToShieldSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "users_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddMemberToShieldSerializer.users_id", "name": "users_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.AddMemberToShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.AddMemberToShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AddPointOfInterestShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer", "name": "AddPointOfInterestShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.AddPointOfInterestShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.point_of_interest_lat", "name": "point_of_interest_lat", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.point_of_interest_location", "name": "point_of_interest_location", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.point_of_interest_long", "name": "point_of_interest_long", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.point_of_interest_tag", "name": "point_of_interest_tag", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.AddPointOfInterestShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.AddPointOfInterestShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlertCategorySerialzier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.AlertCategorySerialzier", "name": "AlertCategorySerialzier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.AlertCategorySerialzier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.AlertCategorySerialzier", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.AlertCategorySerialzier.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.AlertCategorySerialzier.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.AlertCategorySerialzier.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AlertCategorySerialzier.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AlertCategorySerialzier.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Alert.models.AlertCategories", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.AlertCategorySerialzier.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.AlertCategorySerialzier.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.AlertCategorySerialzier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.AlertCategorySerialzier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AssignAdminShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.AssignAdminShieldSerializer", "name": "AssignAdminShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.AssignAdminShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.AssignAdminShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AssignAdminShieldSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "single_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AssignAdminShieldSerializer.single_user", "name": "single_user", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "users_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.AssignAdminShieldSerializer.users_id", "name": "users_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.AssignAdminShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.AssignAdminShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BiometricSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.BiometricSerializer", "name": "BiometricSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.BiometricSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.BiometricSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.BiometricSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.BiometricSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.BiometricSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Biometric", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.BiometricSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.BiometricSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.BiometricSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.BiometricSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BiometricUserShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.BiometricUserShieldSerializer", "name": "BiometricUserShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.BiometricUserShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.BiometricUserShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.address", "name": "address", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.lat", "name": "lat", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.long", "name": "long", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.type", "name": "type", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "user_pic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.BiometricUserShieldSerializer.user_pic", "name": "user_pic", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.BiometricUserShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.BiometricUserShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeShieldNameSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ChangeShieldNameSerializer", "name": "ChangeShieldNameSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ChangeShieldNameSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ChangeShieldNameSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ChangeShieldNameSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ChangeShieldNameSerializer.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ChangeShieldNameSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ChangeShieldNameSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.CreateShieldSerializer", "name": "CreateShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.CreateShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.CreateShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.CreateShieldSerializer.locations", "name": "locations", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.CreateShieldSerializer.shield_name", "name": "shield_name", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "tag_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.CreateShieldSerializer.tag_name", "name": "tag_name", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "users_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.CreateShieldSerializer.users_id", "name": "users_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.CreateShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.CreateShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EditPointOfInterestShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer", "name": "EditPointOfInterestShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.EditPointOfInterestShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.poi_id", "name": "poi_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.point_of_interest_lat", "name": "point_of_interest_lat", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.point_of_interest_location", "name": "point_of_interest_location", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.point_of_interest_long", "name": "point_of_interest_long", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.point_of_interest_tag", "name": "point_of_interest_tag", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.EditPointOfInterestShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.EditPointOfInterestShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetMemberIDSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetMemberIDSerializer", "name": "GetMemberIDSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetMemberIDSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetMemberIDSerializer", "builtins.object"], "names": {".class": "SymbolTable", "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetMemberIDSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetMemberIDSerializer.month", "name": "month", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetMemberIDSerializer.year", "name": "year", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetMemberIDSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetMemberIDSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetMonthlySerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetMonthlySerializer", "name": "GetMonthlySerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetMonthlySerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetMonthlySerializer", "builtins.object"], "names": {".class": "SymbolTable", "month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetMonthlySerializer.month", "name": "month", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetMonthlySerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetMonthlySerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetPointOfInterestIdSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetPointOfInterestIdSerializer", "name": "GetPointOfInterestIdSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetPointOfInterestIdSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetPointOfInterestIdSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetPointOfInterestIdSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetPointOfInterestIdSerializer.poi_id", "name": "poi_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetPointOfInterestIdSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetPointOfInterestIdSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetRouteDetailSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetRouteDetailSerializer", "name": "GetRouteDetailSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetRouteDetailSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetRouteDetailSerializer", "builtins.object"], "names": {".class": "SymbolTable", "route_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetRouteDetailSerializer.route_id", "name": "route_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetRouteDetailSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetRouteDetailSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldIdAndMemberIDHierarchySerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer", "name": "GetShieldIdAndMemberIDHierarchySerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer", "builtins.object"], "names": {".class": "SymbolTable", "hierarchy_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer.hierarchy_name", "name": "hierarchy_name", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldIdAndMemberIDSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetShieldIdAndMemberIDSerializer", "name": "GetShieldIdAndMemberIDSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetShieldIdAndMemberIDSerializer", "builtins.object"], "names": {".class": "SymbolTable", "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdAndMemberIDSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetShieldIdAndMemberIDSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetShieldIdAndMemberIDSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldIdSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetShieldIdSerializer", "name": "GetShieldIdSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetShieldIdSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetShieldIdSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldIdSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetShieldIdSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetShieldIdSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldJoiningCodeSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetShieldJoiningCodeSerializer", "name": "GetShieldJoiningCodeSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetShieldJoiningCodeSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetShieldJoiningCodeSerializer", "builtins.object"], "names": {".class": "SymbolTable", "shield_joining_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetShieldJoiningCodeSerializer.shield_joining_code", "name": "shield_joining_code", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetShieldJoiningCodeSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetShieldJoiningCodeSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetUserIDSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.GetUserIDSerializer", "name": "GetUserIDSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.GetUserIDSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.GetUserIDSerializer", "builtins.object"], "names": {".class": "SymbolTable", "user_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.GetUserIDSerializer.user_id", "name": "user_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.GetUserIDSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.GetUserIDSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HierarchySerializerShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.HierarchySerializerShield", "name": "HierarchySerializerShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.HierarchySerializerShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.HierarchySerializerShield", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.HierarchySerializerShield.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.HierarchySerializerShield.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.HierarchySerializerShield.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.HierarchySerializerShield.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.HierarchySerializerShield.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Hierarchie", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.HierarchySerializerShield.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.HierarchySerializerShield.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.HierarchySerializerShield.admin", "name": "admin", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.HierarchySerializerShield.get_admin", "name": "get_admin", "type": null}}, "get_shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.HierarchySerializerShield.get_shield_id", "name": "get_shield_id", "type": null}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.HierarchySerializerShield.member", "name": "member", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.HierarchySerializerShield.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.HierarchySerializerShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.HierarchySerializerShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemberAudioStatusSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.MemberAudioStatusSerializer", "name": "MemberAudioStatusSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.MemberAudioStatusSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.MemberAudioStatusSerializer", "builtins.object"], "names": {".class": "SymbolTable", "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberAudioStatusSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberAudioStatusSerializer.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.MemberAudioStatusSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.MemberAudioStatusSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemberLocationSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.MemberLocationSerializer", "name": "MemberLocationSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.MemberLocationSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.MemberLocationSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.MemberLocationSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.MemberLocationSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.MemberLocationSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberLocationSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberLocationSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Location", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.MemberLocationSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.MemberLocationSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.MemberLocationSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.MemberLocationSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemberRouteSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.MemberRouteSerializer", "name": "MemberRouteSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.MemberRouteSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.MemberRouteSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.MemberRouteSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.MemberRouteSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.MemberRouteSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberRouteSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberRouteSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Route", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.MemberRouteSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.MemberRouteSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ending_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberRouteSerializer.ending_poi", "name": "ending_poi", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_ending_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.MemberRouteSerializer.get_ending_poi", "name": "get_ending_poi", "type": null}}, "get_starting_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.MemberRouteSerializer.get_starting_poi", "name": "get_starting_poi", "type": null}}, "starting_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.MemberRouteSerializer.starting_poi", "name": "starting_poi", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.MemberRouteSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.MemberRouteSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointOfInterestSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.PointOfInterestSerializer", "name": "PointOfInterestSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.PointOfInterestSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.PointOfInterestSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.PointOfInterestSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.PointOfInterestSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.PointOfInterestSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.PointOfInterestSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.PointOfInterestSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.PointsOfInterest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.PointOfInterestSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.PointOfInterestSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.PointOfInterestSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.PointOfInterestSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointOfInterestSerializerInShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.PointOfInterestSerializerInShield", "name": "PointOfInterestSerializerInShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.PointOfInterestSerializerInShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.PointOfInterestSerializerInShield", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.PointOfInterestSerializerInShield.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.PointsOfInterest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.PointOfInterestSerializerInShield.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.PointOfInterestSerializerInShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.PointOfInterestSerializerInShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemoveMemberToShieldSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.RemoveMemberToShieldSerializer", "name": "RemoveMemberToShieldSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.RemoveMemberToShieldSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.RemoveMemberToShieldSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RemoveMemberToShieldSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "user_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RemoveMemberToShieldSerializer.user_id", "name": "user_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.RemoveMemberToShieldSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.RemoveMemberToShieldSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestShieldSerialzier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.RequestShieldSerialzier", "name": "RequestShieldSerialzier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.RequestShieldSerialzier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.RequestShieldSerialzier", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.RequestShieldSerialzier.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.RequestShieldSerialzier.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.RequestShieldSerialzier.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RequestShieldSerialzier.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RequestShieldSerialzier.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldJoinRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.RequestShieldSerialzier.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.RequestShieldSerialzier.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.RequestShieldSerialzier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.RequestShieldSerialzier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestShieldSerialzierForOneTimeAPi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi", "name": "RequestShieldSerialzierForOneTimeAPi", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.RequestShieldSerialzierForOneTimeAPi", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldJoinRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.RequestShieldSerialzierForOneTimeAPi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldAdminSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldAdminSerializer", "name": "ShieldAdminSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldAdminSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldAdminSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldAdminSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldAdminSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldAdminSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAdminSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAdminSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldAdminSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldAdminSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAdminSerializer.admin", "name": "admin", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldAdminSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldAdminSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldAlertSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldAlertSerializer", "name": "ShieldAlertSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldAlertSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldAlertSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldAlertSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldAlertSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldAlertSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAlertSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAlertSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Alert.models.AlertModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldAlertSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldAlertSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAlertSerializer.category", "name": "category", "type": "Shield.serializers.AlertCategorySerialzier"}}, "userprofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldAlertSerializer.userprofile", "name": "userprofile", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldAlertSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldAlertSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldBiometricsReportSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldBiometricsReportSerializer", "name": "ShieldBiometricsReportSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldBiometricsReportSerializer", "builtins.object"], "names": {".class": "SymbolTable", "end_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.end_date", "name": "end_date", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.member_ids", "name": "member_ids", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "report_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.report_type", "name": "report_type", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "start_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.start_date", "name": "start_date", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldBiometricsReportSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldBiometricsReportSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldBiometricsSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldBiometricsSerializer", "name": "ShieldBiometricsSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldBiometricsSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldBiometricsSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldBiometricsSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldBiometricsSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldBiometricsSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Biometric", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldBiometricsSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldBiometricsSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "get_image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldBiometricsSerializer.get_image_url", "name": "get_image_url", "type": null}}, "image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldBiometricsSerializer.image_url", "name": "image_url", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldBiometricsSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldBiometricsSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldImageChangeSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldImageChangeSerializer", "name": "ShieldImageChangeSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldImageChangeSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldImageChangeSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldImageChangeSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldImageChangeSerializer.image", "name": "image", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldImageChangeSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldImageChangeSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldJoiningRequestSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldJoiningRequestSerializer", "name": "ShieldJoiningRequestSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldJoiningRequestSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldJoiningRequestSerializer", "builtins.object"], "names": {".class": "SymbolTable", "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldJoiningRequestSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldJoiningRequestSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldJoiningRequestSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocationSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldMemberLocationSerializer", "name": "ShieldMemberLocationSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldMemberLocationSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldMemberLocationSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldMemberLocationSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Location", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldMemberLocationSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldMemberLocationSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldMemberLocationSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberRouteSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldMemberRouteSerializer", "name": "ShieldMemberRouteSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldMemberRouteSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldMemberRouteSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldMemberRouteSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Route", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldMemberRouteSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldMemberRouteSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldMemberRouteSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldUpdateJoiningStatusSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer", "name": "ShieldUpdateJoiningStatusSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldUpdateJoiningStatusSerializer", "builtins.object"], "names": {".class": "SymbolTable", "requester_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer.requester_id", "name": "requester_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldUpdateJoiningStatusSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldUpdateJoiningStatusSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldUserRouteProfileSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldUserRouteProfileSerializer", "name": "ShieldUserRouteProfileSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldUserRouteProfileSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldUserRouteProfileSerializer", "builtins.object"], "names": {".class": "SymbolTable", "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldUserRouteProfileSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "user_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldUserRouteProfileSerializer.user_id", "name": "user_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldUserRouteProfileSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldUserRouteProfileSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldsSerializerForDashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldsSerializerForDashboard", "name": "ShieldsSerializerForDashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldsSerializerForDashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldsSerializerForDashboard", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldsSerializerForDashboard.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldsSerializerForDashboard.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerForDashboard.admin", "name": "admin", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}, "shield_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerForDashboard.shield_type", "name": "shield_type", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldsSerializerForDashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldsSerializerForDashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldsSerializerInCreateShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldsSerializerInCreateShield", "name": "ShieldsSerializerInCreateShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldsSerializerInCreateShield", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.ShieldsSerializerInCreateShield.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldsSerializerInCreateShield.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.admin", "name": "admin", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}, "get_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_locations", "name": "get_locations", "type": null}}, "get_logo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_logo_url", "name": "get_logo_url", "type": null}}, "get_participent_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_participent_count", "name": "get_participent_count", "type": null}}, "get_shield_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_shield_code", "name": "get_shield_code", "type": null}}, "get_shield_joining_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_shield_joining_code", "name": "get_shield_joining_code", "type": null}}, "get_shield_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_shield_name", "name": "get_shield_name", "type": null}}, "get_shield_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_shield_type", "name": "get_shield_type", "type": null}}, "get_walkie_talkie_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.get_walkie_talkie_status", "name": "get_walkie_talkie_status", "type": null}}, "locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.locations", "name": "locations", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.logo_url", "name": "logo_url", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "members": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.members", "name": "members", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}, "participent_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.participent_count", "name": "participent_count", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.shield_code", "name": "shield_code", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_joining_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.shield_joining_code", "name": "shield_joining_code", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.shield_name", "name": "shield_name", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.shield_type", "name": "shield_type", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "walkie_talkie_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.walkie_talkie_status", "name": "walkie_talkie_status", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.ShieldsSerializerInCreateShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.ShieldsSerializerInCreateShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "User": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.serializers.User", "name": "User", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.User", "source_any": null, "type_of_any": 3}}}, "UserLocationSerialzier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserLocationSerialzier", "name": "UserLocationSerialzier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.UserLocationSerialzier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserLocationSerialzier", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserLocationSerialzier.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.UserLocationSerialzier.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserLocationSerialzier.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserLocationSerialzier.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserLocationSerialzier.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Location", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserLocationSerialzier.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserLocationSerialzier.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserLocationSerialzier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserLocationSerialzier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserProfileRouteSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserProfileRouteSerializer", "name": "UserProfileRouteSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.UserProfileRouteSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserProfileRouteSerializer", "builtins.object"], "names": {".class": "SymbolTable", "battery_percentage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.battery_percentage", "name": "battery_percentage", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.current_address", "name": "current_address", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.current_location", "name": "current_location", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_location_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.current_location_since", "name": "current_location_since", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_speed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.current_speed", "name": "current_speed", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.current_status", "name": "current_status", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "last_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.last_poi", "name": "last_poi", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "last_poi_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.last_poi_time", "name": "last_poi_time", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "max_speed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.max_speed", "name": "max_speed", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.member", "name": "member", "type": "Shield.serializers.UserProfileSerializerForDashboard"}}, "min_battery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.min_battery", "name": "min_battery", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "on_route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.on_route", "name": "on_route", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "on_route_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileRouteSerializer.on_route_since", "name": "on_route_since", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserProfileRouteSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserProfileRouteSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserProfileSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserProfileSerializer", "name": "UserProfileSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.UserProfileSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserProfileSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserProfileSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.UserProfileSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserProfileSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Account.models.UserProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserProfileSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserProfileSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializer.user", "name": "user", "type": "Shield.serializers.UserSerializer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserProfileSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserProfileSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserProfileSerializerForDashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserProfileSerializerForDashboard", "name": "UserProfileSerializerForDashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.UserProfileSerializerForDashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserProfileSerializerForDashboard", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserProfileSerializerForDashboard.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Account.models.UserProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserProfileSerializerForDashboard.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserProfileSerializerForDashboard.user", "name": "user", "type": "Shield.serializers.UserSerializer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserProfileSerializerForDashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserProfileSerializerForDashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserSerializer", "name": "UserSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.UserSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.UserSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.UserSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.UserSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserSerializer.Meta.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserSerializer.Meta.model", "name": "model", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.User", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.UserSerializer.password", "name": "password", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "validate_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.serializers.UserSerializer.validate_email", "name": "validate_email", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.UserSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.UserSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WalkieTalkieSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.WalkieTalkieSerializer", "name": "WalkieTalkieSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.WalkieTalkieSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.WalkieTalkieSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.WalkieTalkieSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.WalkieTalkieSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.WalkieTalkieSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.WalkieTalkieSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.WalkieTalkieSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.WalkieTalkieSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.WalkieTalkieSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.WalkieTalkieSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.WalkieTalkieSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.serializers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "account_models": {".class": "SymbolTableNode", "cross_ref": "Account.models", "kind": "Gdef"}, "alert_models": {".class": "SymbolTableNode", "cross_ref": "Alert.models", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "getMemberIdSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.getMemberIdSerializer", "name": "getMemberIdSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.getMemberIdSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.getMemberIdSerializer", "builtins.object"], "names": {".class": "SymbolTable", "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.getMemberIdSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.getMemberIdSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.getMemberIdSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "membership_models": {".class": "SymbolTableNode", "cross_ref": "Membership.models", "kind": "Gdef"}, "serializers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.serializers.serializers", "name": "serializers", "type": {".class": "AnyType", "missing_import_name": "Shield.serializers.serializers", "source_any": null, "type_of_any": 3}}}, "sheildMembershipSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.sheildMembershipSerializer", "name": "sheildMembershipSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.serializers.sheildMembershipSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.sheildMembershipSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.serializers.sheildMembershipSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.serializers.sheildMembershipSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.serializers", "mro": ["Shield.serializers.sheildMembershipSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.sheildMembershipSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.serializers.sheildMembershipSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Membership.models.MembershipModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.sheildMembershipSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.sheildMembershipSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.serializers.sheildMembershipSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.serializers.sheildMembershipSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "shield_models": {".class": "SymbolTableNode", "cross_ref": "Shield.models", "kind": "Gdef"}, "ticket_models": {".class": "SymbolTableNode", "cross_ref": "Ticket.models", "kind": "Gdef"}}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\serializers.py"}