import React from "react";
import InputGroup from "@/components/utility/InputGroup";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import FilterDropDownBtn from "../utility/FilterDropDownBtn";

const TopBar = ({ search, setSearch, resetPage, tempFilters, setTempFilters, applyFilters, allData, currentData }) => {
  return (
    <div className="bg-neutral">
      <div className="container-padding items-center gap-3 space-y-2 py-2.5 lg:flex lg:space-y-0">
        <div className="w-full flex-shrink-0 sm:w-auto">
          <InputGroup className=" relative">
            <div className="absolute inset-y-0 left-0 flex w-9 items-center justify-center p-1 px-1.5 pl-3 text-secondary">
              <MagnifyingGlassIcon className="aspect-square w-full" />
            </div>
            <InputGroup.Input
              value={search}
              onChange={e => {
                setSearch(e.target.value);
                resetPage();
              }}
              id="search"
              type="search"
              name="search"
              className="pl-9"
              placeholder="Buscar"
            />
          </InputGroup>
        </div>

        <div className="flex flex-grow items-center gap-3">
          <FilterDropDownBtn.Primary
            filters={tempFilters}
            setFilters={setTempFilters}
            onApply={applyFilters}
            groups={[
              {
                id: 1,
                title: "Type of Shield",
                options: [
                  {
                    id: 1,
                    label: "Corporate",
                    name: "shield_type",
                    value: "Corporate",
                  },
                  {
                    id: 2,
                    label: "Individual",
                    name: "shield_type",
                    value: "Individual",
                  },
                ],
              },
              {
                id: 2,
                title: "Status",
                options: [
                  {
                    id: 1,
                    label: "Activo",
                    name: "suspend",
                    value: false,
                  },
                  {
                    id: 2,
                    label: "Suspendido",
                    name: "suspend",
                    value: true,
                  },
                ],
              },
            ]}
          />

          <div className="ml-auto text-right text-sm text-gray-900">
            {allData && allData.length ? `${allData.length} Escudos` : 'No hay escudos'}
            {currentData && currentData.length !== allData?.length && (
              <span className="text-primary"> ({currentData.length} mostrados)</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
