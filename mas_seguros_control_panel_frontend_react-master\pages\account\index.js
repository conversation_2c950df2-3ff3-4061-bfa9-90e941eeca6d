import React, { useEffect, useState } from "react";
import Admin from "@/components/layouts/Admin";
import { useSelector } from "react-redux";
import useAxios from "@/hooks/useAxios";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import ProfilePicture from "@/components/ProfilePicture";
import InputGroup from "@/components/utility/InputGroup";
import { PencilIcon } from "@heroicons/react/24/outline";

const Account = () => {
  const user = useSelector((state) => state.user);
  const { axios } = useAxios();
  const [isLoading, setIsLoading] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const { register, handleSubmit, setValue, formState: { errors } } = useForm({
    mode: "onBlur"
  });

  // Function to fetch user profile data
  const fetchUserProfile = async () => {
    console.log("User data from Redux:", user);

    // Create a basic profile from Redux state data
    const createBasicProfile = () => {
      // Use the full_name from Redux if available, otherwise construct it
      let displayName = user.full_name || "";

      if (!displayName) {
        // If no full_name, try to construct it from first_name and last_name
        displayName = `${user.first_name || ''} ${user.last_name || ''}`.trim();

        // If still no name, use email username
        if (!displayName && user.email) {
          displayName = user.email.split('@')[0];
        }

        // Final fallback
        if (!displayName) {
          displayName = "Usuario";
        }
      }

      // Create a profile object using all available data from Redux
      const basicProfile = {
        full_name: displayName,
        user: {
          id: user.id,
          email: user.email || "",
          first_name: user.first_name || "",
          last_name: user.last_name || ""
        },
        phone: user.phone || "",
        image: user.image,
        role: user.role || "Admin",
        identification_card: user.identification_card || "",
        birth_date: user.birth_date || ""
      };

      console.log("Created basic profile:", basicProfile);

      setUserProfile(basicProfile);
      setValue("full_name", basicProfile.full_name);
      setValue("email", basicProfile.user.email);
      setValue("phone", basicProfile.phone);
      setValue("identification_card", basicProfile.identification_card);

      // Format birth date if available
      if (basicProfile.birth_date) {
        try {
          const date = new Date(basicProfile.birth_date);
          const day = String(date.getDate()).padStart(2, '0');
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const year = date.getFullYear();
          setValue("birth_date", `${day}/${month}/${year}`);
        } catch (e) {
          console.error("Error formatting date:", e);
        }
      }

      return basicProfile;
    };

    // Always start with data from Redux
    createBasicProfile();

    // Log the user ID to see if we have it
    console.log("User ID from Redux:", user.id);

    // If we don't have a user ID, we can't fetch more data from the API
    if (!user.id) {
      console.log("No user ID available in Redux, using basic profile only");
      return;
    }

    setIsLoading(true);
    try {
      // Try to get more detailed profile data if we have a user ID
      console.log(`Fetching profile data from API with ID: ${user.id}`);
      const response = await axios.get(`/adminside/api/roles/userprofile/?id=${user.id}`);
      console.log("API response:", response.data);

      if (response.data && response.data.data) {
        const data = response.data.data;
        console.log("Profile data from API:", data);

        // Merge the API data with our basic profile
        const mergedProfile = {
          ...data,
          // Ensure we have these fields even if API doesn't return them
          full_name: data.full_name || userProfile.full_name,
          user: {
            ...data.user,
            email: data.user?.email || userProfile.user.email
          },
          phone: data.phone || userProfile.phone,
          role: data.role || userProfile.role
        };

        console.log("Merged profile:", mergedProfile);
        setUserProfile(mergedProfile);

        // Pre-fill form with user data
        setValue("full_name", mergedProfile.full_name);
        setValue("email", mergedProfile.user?.email || user.email);
        setValue("identification_card", mergedProfile.identification_card || "");
        setValue("phone", mergedProfile.phone || "");

        // Format birth date if available
        if (mergedProfile.birth_date) {
          try {
            let dateStr = mergedProfile.birth_date;
            let dd, mm, yyyy;
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
              // YYYY-MM-DD
              [yyyy, mm, dd] = dateStr.split('-');
            } else if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
              // DD/MM/YYYY
              [dd, mm, yyyy] = dateStr.split('/');
            } else {
              // Try to parse as Date
              const d = new Date(dateStr);
              if (!isNaN(d.getTime())) {
                dd = String(d.getDate()).padStart(2, '0');
                mm = String(d.getMonth() + 1).padStart(2, '0');
                yyyy = d.getFullYear();
              }
            }
            if (dd && mm && yyyy) {
              setValue("birth_date", `${dd}/${mm}/${yyyy}`);
            }
          } catch (e) {
            // fallback: do not set
          }
        }
      } else {
        console.log("No data returned from API or invalid response format");
      }
    } catch (error) {
      console.error("Error fetching profile from API:", error);
      console.error("Error details:", error.response?.data || error.message);
      // We already have the basic profile from Redux, so no need to do anything here
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, [user]);

  const onSubmit = async (data) => {
    setIsLoading(true);
    try {
      // Split full name into first and last name
      const nameParts = data.full_name.split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(' ');

      // Check if we have a user ID
      if (!user.id) {
        toast.error("No se puede actualizar el perfil sin ID de usuario");
        return;
      }

      // Use the adminside roles API to update the profile
      await axios.post("/adminside/api/roles/editprofile/", {
        ...data,
        id: user.id,
        first_name: firstName,
        last_name: lastName,
        birth_date: data.birth_date // API expects DD/MM/YYYY format
      });

      toast.success("Perfil actualizado con éxito");

      // Exit edit mode after successful update
      setIsEditMode(false);

      // Refresh profile data
      fetchUserProfile();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error?.response?.data?.message || "Error al actualizar el perfil");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to format date for display
  const formatDateForDisplay = (dateString) => {
    if (!dateString) return "No especificado";
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      return dateString;
    }
  };

  return (
    <Admin pageTitle="Mi Perfil" headerTitle="Mi Perfil">
      <div className="container-padding py-6">
        <div className="mx-auto max-w-3xl bg-white p-6 shadow-sm">
          <div className="mb-6 flex items-center gap-4">
            <ProfilePicture
              src={user.image}
              className="h-20 w-20 rounded-full"
              alt={userProfile?.full_name || "Usuario"}
            />
            <div>
              <h2 className="text-xl font-semibold">
                {userProfile?.full_name || "Usuario"}
              </h2>
              <p className="text-gray-600">{user.email || "<EMAIL>"}</p>
              {(userProfile?.role || user.role) && (
                <p className="text-sm text-gray-500">{userProfile?.role || user.role}</p>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="py-10 text-center">
              {isEditMode ? "Guardando cambios..." : "Cargando información del perfil..."}
            </div>
          ) : isEditMode ? (
            <div>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <label className="mb-1 block text-sm font-medium">Nombre completo</label>
                    <InputGroup>
                      <InputGroup.Input
                        {...register("full_name", { required: "El nombre es requerido" })}
                        placeholder="Nombre completo"
                      />
                    </InputGroup>
                    {errors.full_name && (
                      <p className="mt-1 text-sm text-red-600">{errors.full_name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium">Correo electrónico</label>
                    <InputGroup>
                      <InputGroup.Input
                        {...register("email", {
                          required: "El correo es requerido",
                          pattern: {
                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                            message: "Correo electrónico inválido"
                          }
                        })}
                        placeholder="Correo electrónico"
                      />
                    </InputGroup>
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium">Identificación</label>
                    <InputGroup>
                      <InputGroup.Input
                        {...register("identification_card")}
                        placeholder="Número de identificación"
                      />
                    </InputGroup>
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium">Teléfono</label>
                    <InputGroup>
                      <InputGroup.Input
                        {...register("phone")}
                        placeholder="Número de teléfono"
                      />
                    </InputGroup>
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium">Fecha de nacimiento</label>
                    <InputGroup>
                      <InputGroup.Input
                        {...register("birth_date")}
                        placeholder="DD/MM/YYYY"
                      />
                    </InputGroup>
                    <p className="mt-1 text-xs text-gray-500">Formato: DD/MM/YYYY</p>
                  </div>
                </div>

                <div className="mt-6 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => setIsEditMode(false)}
                    className="rounded border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="rounded bg-primary px-4 py-2 font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-70"
                  >
                    {isLoading ? "Guardando..." : "Guardar cambios"}
                  </button>
                </div>
              </form>
            </div>
          ) : (
            <div>
              <div className="mb-6 flex justify-end">
                <button
                  onClick={() => setIsEditMode(true)}
                  className="flex items-center gap-2 rounded bg-primary px-4 py-2 font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                >
                  <PencilIcon className="h-5 w-5" />
                  Editar perfil
                </button>
              </div>

              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Nombre completo</h3>
                    <p className="text-base">{userProfile?.full_name || "No especificado"}</p>
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Correo electrónico</h3>
                    <p className="text-base">{userProfile?.user?.email || user.email || "No especificado"}</p>
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Identificación</h3>
                    <p className="text-base">{userProfile?.identification_card || "No especificado"}</p>
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Teléfono</h3>
                    <p className="text-base">{userProfile?.phone || user.phone || "No especificado"}</p>
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Fecha de nacimiento</h3>
                    <p className="text-base">
                      {userProfile?.birth_date ? formatDateForDisplay(userProfile.birth_date) : "No especificado"}
                    </p>
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Rol</h3>
                    <p className="text-base">{userProfile?.role || user.role || "Admin"}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Admin>
  );
};

export default Account;
