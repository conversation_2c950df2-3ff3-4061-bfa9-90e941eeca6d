import useAxios from "@/hooks/useAxios";
import React, { forwardRef, useImperativeHandle } from "react";
import { useQuery } from "react-query";
import SectionHeading from "../SectionHeading";
import MembershipsCountCard from "./MembershipsCountCard";
import RegisteredUsersCountCard from "./RegisteredUsersCountCard";
import SmallAnalyticsCard from "./SmallAnalyticsCard";

const TopCardsSection = forwardRef(({ selectedMonth }, ref) => {
  const { axios } = useAxios();

  const now = new Date();

  function padWithZero(num, targetLength) {
    return String(num).padStart(targetLength, "0");
  }

  const selectedDate = new Date(
    `${now.getFullYear()}-${padWithZero(
      selectedMonth.value,
      2
    )}-15T14:10:28.570073Z`
  );

  const fetchData = ({ url = "", month, year }) => {
    return axios.get(url, {
      params: {
        month,
        year,
      },
    });
  };

  const alertsQuery = useQuery(
    [`alerts-count-data-${selectedMonth.value}`],
    () =>
      fetchData({
        url: "/api/dashboard/generated-alerts-with-sos-according-months/",
        month: selectedDate.getMonth() + 1,
        year: selectedDate.getFullYear(),
      }),
    {
      refetchOnWindowFocus: false,
      // Add error handling for alerts query
      onError: (error) => {
        console.error("Error fetching alerts data:", error);
      }
    }
  );

  const ticketsQuery = useQuery(
    [`tickets-count-data-${selectedMonth.value}`],
    () =>
      fetchData({
        url: "/api/dashboard/created-tickets-according-months/",
        month: selectedDate.getMonth() + 1,
        year: selectedDate.getFullYear(),
      }),
    {
      refetchOnWindowFocus: false,
      // Add error handling for tickets query
      onError: (error) => {
        console.error("Error fetching tickets data:", error);
      }
    }
  );

  const shieldsQuery = useQuery(
    [`shields-count-data-${selectedMonth.value}`],
    () =>
      fetchData({
        url: "/api/dashboard/created-shields-according-months/",
        month: selectedDate.getMonth() + 1,
        year: selectedDate.getFullYear(),
      }),
    {
      refetchOnWindowFocus: false,
      // Add error handling for shields query
      onError: (error) => {
        console.error("Error fetching shields data:", error);
        // We don't show a toast here to avoid disrupting the user experience
        // since this is a dashboard component that should gracefully handle errors
      }
    }
  );

  const promocodesQuery = useQuery(
    [`promocodes-count-data-${selectedMonth.value}`],
    () =>
      fetchData({
        url: "/api/dashboard/created-promo-code-according-months/",
        month: selectedDate.getMonth() + 1,
        year: selectedDate.getFullYear(),
      }),
    {
      refetchOnWindowFocus: false,
      // Add error handling for promocodes query
      onError: (error) => {
        console.error("Error fetching promocodes data:", error);
      }
    }
  );

  // Safely access data with fallbacks to empty objects/arrays
  const alertsData = alertsQuery.data?.data?.data ?? {}
  const ticketsData = ticketsQuery.data?.data?.data ?? {}
  const shieldsData = shieldsQuery.data?.data?.data ?? {}
  const promocodesData = promocodesQuery.data?.data?.data ?? {}

  // Create a ref to expose data for Excel export
  useImperativeHandle(ref, () => ({
    getExportData: () => {
      try {
        // Get data from RegisteredUsersCountCard
        const usersCardRef = document.querySelector('[data-users]');
        const usersDataStr = usersCardRef?.getAttribute('data-users') || '{}';
        const usersData = JSON.parse(usersDataStr);

        // Get data from MembershipsCountCard
        const membershipsCardRef = document.querySelector('[data-memberships]');
        const membershipsDataStr = membershipsCardRef?.getAttribute('data-memberships') || '{}';
        const membershipsData = JSON.parse(membershipsDataStr)?.items || [];

        // Check if we have any data
        const hasData =
          (usersData && Object.keys(usersData).length > 0) ||
          (membershipsData && membershipsData.length > 0) ||
          (shieldsData && Object.keys(shieldsData).length > 0) ||
          (ticketsData && Object.keys(ticketsData).length > 0) ||
          (alertsData && Object.keys(alertsData).length > 0) ||
          (promocodesData && Object.keys(promocodesData).length > 0);

        if (!hasData) {
          return null;
        }

        return {
          users: usersData,
          memberships: membershipsData,
          shields: shieldsData,
          tickets: ticketsData,
          alerts: alertsData,
          promocodes: promocodesData
        };
      } catch (error) {
        console.error("Error getting export data:", error);
        return null;
      }
    }
  }));


  return (
    <div className="container-padding">
      <SectionHeading className="py-5">Métricas del mes</SectionHeading>
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 2xl:grid-cols-4">
        <div data-export-id="registered-users-card">
          <RegisteredUsersCountCard selectedMonth={selectedMonth} />
        </div>
        <div data-export-id="memberships-card">
          <MembershipsCountCard selectedMonth={selectedMonth} />
        </div>
        <div className="flex flex-col gap-4">
          <SmallAnalyticsCard
            title="Escudos creados"
            items={[
              {
                title: "Particulares",
                count: shieldsData.individual_count,
              },
              {
                title: "Empresas",
                count: shieldsData.companies_count,
              },
            ]}
            footer={{
              title: "Ver escudos",
              href: "/shields",
            }}
            isLoading={shieldsQuery.isLoading}
            isError={shieldsQuery.isError}
          />
          <SmallAnalyticsCard
            title="Tickets de soporte"
            items={[
              {
                title: "Total",
                count: ticketsData.support_tickets,
              },
              {
                title: "Resueltos",
                count: ticketsData.resolved_tickets,
              },
            ]}
            footer={{
              title: "Ver soporte",
              href: "/support",
            }}
            isLoading={ticketsQuery.isLoading}
            isError={ticketsQuery.isError}
          />
        </div>
        <div className="flex flex-col gap-4">
          <SmallAnalyticsCard
            title="Alertas generadas"
            items={[
              {
                title: "Alertas",
                count: alertsData.generated_alerts_count,
              },
              {
                title: "SOS",
                count: alertsData.sos_count,
              },
            ]}
            footer={{
              title: "Ver Alertas",
              href: "/alerts-and-sos",
            }}
            isLoading={alertsQuery.isLoading}
            isError={alertsQuery.isError}
          />
          <SmallAnalyticsCard
            title="Código de promoción"
            items={[
              {
                title: "Total",
                count: promocodesData.total,
              },
              {
                title: "Vencidos",
                count: promocodesData.due,
              },
            ]}
            footer={{
              title: "Ver códigos",
              href: "/promo-codes",
            }}
            isLoading={promocodesQuery.isLoading}
            isError={promocodesQuery.isError}
          />
        </div>
      </div>
    </div>
  );
});

export default TopCardsSection;
