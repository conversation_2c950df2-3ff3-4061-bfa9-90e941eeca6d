from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from Ticket import models as ticket_models


# class SeTicketDescriptionrializer(serializers.Serializer):
#     category_id = serializers.IntegerField(required=True)


class TicketSubjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = ticket_models.TicketSubject
        fields = ['title', 'id', 'description', 'resolved']


class TicketDescriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ticket_models.Ticket
        fields = ['description', 'title']


class TicketMessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.SerializerMethodField()

    def get_sender_name(self, obj):
        if obj.sender:
            return obj.sender.full_name
        return "Sistema"

    class Meta:
        model = ticket_models.TicketMessage
        fields = ['id', 'ticket', 'sender', 'sender_name', 'message', 'is_admin', 'created_at']
        read_only_fields = ['id', 'created_at']


class TicketSerializer(serializers.ModelSerializer):
    messages = TicketMessageSerializer(many=True, read_only=True)

    class Meta:
        model = ticket_models.Ticket
        fields = ['id', 'ticket_num', 'ticket_description', 'title', 'user', 'resolved', 'created_at', 'messages']
