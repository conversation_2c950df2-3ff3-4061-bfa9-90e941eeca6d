import React, { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';

const GoogleMap = ({
  lat,
  lng,
  zoom = 15,
  className = "",
  markers = [],
  showMarker = true,
  mapType = 'roadmap',
  onMapLoad = null,
  style = { height: '100%', width: '100%' },
  showRoute = false,
  routePoints = [],
  userImage = null
}) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  const polylinesRef = useRef([]);
  const svgUrlsRef = useRef([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!lat || !lng || !process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
      setIsLoading(false);
      if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
        setError('Google Maps API key not found');
      }
      return;
    }

    const loader = new Loader({
      apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
      version: 'weekly',
      libraries: ['places']
    });

    loader.load().then(() => {
      if (mapRef.current) {
        // Create map
        const map = new google.maps.Map(mapRef.current, {
          center: { lat: parseFloat(lat), lng: parseFloat(lng) },
          zoom: zoom,
          mapTypeId: mapType,
          mapTypeControl: true,
          streetViewControl: true,
          fullscreenControl: true,
          zoomControl: true
        });

        mapInstanceRef.current = map;

        // Clear existing markers and polylines
        markersRef.current.forEach(marker => marker.setMap(null));
        markersRef.current = [];
        polylinesRef.current.forEach(polyline => polyline.setMap(null));
        polylinesRef.current = [];
        // Clean up SVG blob URLs
        svgUrlsRef.current.forEach(url => URL.revokeObjectURL(url));
        svgUrlsRef.current = [];

        // Add main marker if showMarker is true
        if (showMarker) {
          // Create light blue outer circle
          const lightBlueCircle = new google.maps.Marker({
            position: { lat: parseFloat(lat), lng: parseFloat(lng) },
            map: map,
            icon: {
              path: google.maps.SymbolPath.CIRCLE,
              fillColor: '#1555ED',
              fillOpacity: 0.2,
              strokeColor: '#1555ED',
              strokeOpacity: 0.3,
              strokeWeight: 1,
              scale: 20
            },
            zIndex: 1
          });

          // Create main blue circle icon
          const mainMarker = new google.maps.Marker({
            position: { lat: parseFloat(lat), lng: parseFloat(lng) },
            map: map,
            title: 'Ubicación',
            icon: {
              path: google.maps.SymbolPath.CIRCLE,
              fillColor: '#1555ED',
              fillOpacity: 1,
              strokeColor: '#ffffff',
              strokeWeight: 2,
              scale: 6
            },
            zIndex: 2
          });

          markersRef.current.push(lightBlueCircle);

          const infoWindow = new google.maps.InfoWindow({
            content: `<div><strong>Ubicación</strong><br>Lat: ${lat}<br>Lng: ${lng}</div>`
          });

          mainMarker.addListener('click', () => {
            infoWindow.open(map, mainMarker);
          });

          markersRef.current.push(mainMarker);
        }

        // Handle route visualization
        if (showRoute && routePoints && routePoints.length > 1) {
          // Create polyline for the route
          const routePath = routePoints.map(point => ({
            lat: parseFloat(point.lat),
            lng: parseFloat(point.lng)
          }));

          const polyline = new google.maps.Polyline({
            path: routePath,
            geodesic: true,
            strokeColor: '#000000',
            strokeOpacity: 1.0,
            strokeWeight: 3
          });

          polyline.setMap(map);
          polylinesRef.current.push(polyline);

          // Add start marker (black dot)
          if (routePoints[0]) {
            const startMarker = new google.maps.Marker({
              position: { lat: parseFloat(routePoints[0].lat), lng: parseFloat(routePoints[0].lng) },
              map: map,
              title: 'Inicio de ruta',
              icon: {
                path: google.maps.SymbolPath.CIRCLE,
                fillColor: '#000000',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2,
                scale: 8
              },
              zIndex: 3
            });

            const startInfoWindow = new google.maps.InfoWindow({
              content: `<div><strong>Inicio de ruta</strong><br>${routePoints[0].location || 'Ubicación de inicio'}</div>`
            });

            startMarker.addListener('click', () => {
              startInfoWindow.open(map, startMarker);
            });

            markersRef.current.push(startMarker);
          }

          // Add end marker with user image in blue pear-shaped marker
          if (routePoints[routePoints.length - 1]) {
            const endPoint = routePoints[routePoints.length - 1];

            // Create custom marker with user image using Canvas
            const createCustomMarkerCanvas = (imageUrl) => {
              // Always use default image if no user image provided
              const defaultImage = '/assets/img/default-profile-pic-1.jpg';
              const finalImageUrl = imageUrl || defaultImage;

              return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const size = 50;
                canvas.width = size;
                canvas.height = size + 10; // Extra height for the pear shape point

                // Load the user image first
                const img = new Image();
                img.crossOrigin = 'anonymous';

                img.onload = () => {
                  // Clear canvas
                  ctx.clearRect(0, 0, canvas.width, canvas.height);

                  // Draw blue pear-shaped background (Google Maps style)
                  ctx.fillStyle = '#1555ED';
                  ctx.strokeStyle = '#ffffff';
                  ctx.lineWidth = 3;

                  // Create the pear shape path
                  ctx.beginPath();
                  // Top circle part
                  ctx.arc(size/2, 18, 18, 0, 2 * Math.PI);
                  ctx.fill();

                  // Draw the pointing triangle at bottom
                  ctx.beginPath();
                  ctx.moveTo(size/2, size + 8); // Bottom point
                  ctx.lineTo(size/2 - 10, 32); // Left point
                  ctx.lineTo(size/2 + 10, 32); // Right point
                  ctx.closePath();
                  ctx.fill();

                  // Draw white border around the whole shape
                  ctx.beginPath();
                  ctx.arc(size/2, 18, 18, 0, 2 * Math.PI);
                  ctx.stroke();

                  ctx.beginPath();
                  ctx.moveTo(size/2, size + 8);
                  ctx.lineTo(size/2 - 10, 32);
                  ctx.lineTo(size/2 + 10, 32);
                  ctx.closePath();
                  ctx.stroke();

                  // Create circular clipping path for the image
                  ctx.save();
                  ctx.beginPath();
                  ctx.arc(size/2, 18, 14, 0, 2 * Math.PI); // Smaller circle for image
                  ctx.clip();

                  // Draw the user image
                  ctx.drawImage(img, size/2 - 14, 4, 28, 28);
                  ctx.restore();

                  // Add inner circle border for image
                  ctx.strokeStyle = '#ffffff';
                  ctx.lineWidth = 2;
                  ctx.beginPath();
                  ctx.arc(size/2, 18, 14, 0, 2 * Math.PI);
                  ctx.stroke();

                  const dataUrl = canvas.toDataURL();
                  resolve({
                    url: dataUrl,
                    scaledSize: new google.maps.Size(size, size + 10),
                    anchor: new google.maps.Point(size/2, size + 10)
                  });
                };

                img.onerror = () => {
                  // If image fails to load, create pear-shaped marker without image
                  ctx.clearRect(0, 0, canvas.width, canvas.height);

                  ctx.fillStyle = '#1555ED';
                  ctx.strokeStyle = '#ffffff';
                  ctx.lineWidth = 3;

                  // Create the pear shape path
                  ctx.beginPath();
                  // Top circle part
                  ctx.arc(size/2, 18, 18, 0, 2 * Math.PI);
                  ctx.fill();

                  // Draw the pointing triangle at bottom
                  ctx.beginPath();
                  ctx.moveTo(size/2, size + 8); // Bottom point
                  ctx.lineTo(size/2 - 10, 32); // Left point
                  ctx.lineTo(size/2 + 10, 32); // Right point
                  ctx.closePath();
                  ctx.fill();

                  // Draw white border around the whole shape
                  ctx.beginPath();
                  ctx.arc(size/2, 18, 18, 0, 2 * Math.PI);
                  ctx.stroke();

                  ctx.beginPath();
                  ctx.moveTo(size/2, size + 8);
                  ctx.lineTo(size/2 - 10, 32);
                  ctx.lineTo(size/2 + 10, 32);
                  ctx.closePath();
                  ctx.stroke();

                  const dataUrl = canvas.toDataURL();
                  resolve({
                    url: dataUrl,
                    scaledSize: new google.maps.Size(size, size + 10),
                    anchor: new google.maps.Point(size/2, size + 10)
                  });
                };

                img.src = finalImageUrl;
              });
            };

            // Create marker with async image loading
            createCustomMarkerCanvas(userImage).then((markerIcon) => {
              const endMarker = new google.maps.Marker({
                position: { lat: parseFloat(endPoint.lat), lng: parseFloat(endPoint.lng) },
                map: map,
                title: 'Fin de ruta',
                icon: markerIcon,
                zIndex: 3
              });

              const endInfoWindow = new google.maps.InfoWindow({
                content: `<div><strong>Fin de ruta</strong><br>${endPoint.location || 'Ubicación final'}</div>`
              });

              endMarker.addListener('click', () => {
                endInfoWindow.open(map, endMarker);
              });

              markersRef.current.push(endMarker);
            });
          }

          // Fit bounds to show entire route
          const bounds = new google.maps.LatLngBounds();
          routePoints.forEach(point => {
            bounds.extend({ lat: parseFloat(point.lat), lng: parseFloat(point.lng) });
          });
          map.fitBounds(bounds);

          // Ensure minimum zoom level
          google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
            if (map.getZoom() > 18) {
              map.setZoom(18);
            }
          });
        } else {
          // Add additional markers (original behavior)
          if (markers && markers.length > 0) {
            markers.forEach((markerData, index) => {
              if (markerData.lat && markerData.lng) {
                // Create main blue circle for additional marker
                const marker = new google.maps.Marker({
                  position: { lat: parseFloat(markerData.lat), lng: parseFloat(markerData.lng) },
                  map: map,
                  title: markerData.title || `Marker ${index + 1}`,
                  icon: markerData.icon || {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: '#1555ED',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2,
                    scale: 6
                  },
                  zIndex: 2
                });

                if (markerData.infoContent) {
                  const infoWindow = new google.maps.InfoWindow({
                    content: markerData.infoContent
                  });

                  marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                  });
                }

                markersRef.current.push(marker);
              }
            });

            // Adjust map bounds to fit all markers if there are multiple
            if (markers.length > 1 || (markers.length === 1 && showMarker)) {
              const bounds = new google.maps.LatLngBounds();

              if (showMarker) {
                bounds.extend({ lat: parseFloat(lat), lng: parseFloat(lng) });
              }

              markers.forEach(markerData => {
                if (markerData.lat && markerData.lng) {
                  bounds.extend({ lat: parseFloat(markerData.lat), lng: parseFloat(markerData.lng) });
                }
              });

              map.fitBounds(bounds);

              // Ensure minimum zoom level
              google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
                if (map.getZoom() > 18) {
                  map.setZoom(18);
                }
              });
            }
          }
        }

        setIsLoading(false);
        
        // Call onMapLoad callback if provided
        if (onMapLoad) {
          onMapLoad(map);
        }
      }
    }).catch((error) => {
      console.error('Error loading Google Maps:', error);
      setError('Error loading Google Maps');
      setIsLoading(false);
    });

    // Cleanup function
    return () => {
      markersRef.current.forEach(marker => marker.setMap(null));
      markersRef.current = [];
      polylinesRef.current.forEach(polyline => polyline.setMap(null));
      polylinesRef.current = [];
      // Clean up SVG blob URLs
      svgUrlsRef.current.forEach(url => URL.revokeObjectURL(url));
      svgUrlsRef.current = [];
    };
  }, [lat, lng, zoom, markers, showMarker, mapType, onMapLoad, showRoute, routePoints, userImage]);

  if (!lat || !lng) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 text-gray-500 ${className}`} style={style}>
        <div className="text-center">
          <div className="text-4xl mb-2">📍</div>
          <p className="text-sm">No hay datos de ubicación disponibles</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 text-gray-500 ${className}`} style={style}>
        <div className="text-center">
          <div className="text-4xl mb-2">⚠️</div>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className} style={style}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Cargando mapa...</p>
          </div>
        </div>
      )}
      <div ref={mapRef} style={{ height: '100%', width: '100%' }} />
    </div>
  );
};

export default GoogleMap;
