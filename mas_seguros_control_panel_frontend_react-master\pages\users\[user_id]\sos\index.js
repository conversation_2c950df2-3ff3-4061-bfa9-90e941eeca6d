import React from "react";
import { useRouter } from "next/router";
import { useQuery } from "react-query";
import UserLayout from "@/components/layouts/UserLayout";
import Table from "@/components/Table";
import EvidenceModalBtn from "@/components/users/user/SOS/EvidenceModalBtn";
import useAxios from "@/hooks/useAxios";
import { format } from "date-fns";

const SOS = () => {
  const router = useRouter();
  const { user_id } = router.query;
  const { axios } = useAxios();

  const fetchUserAlertsAndSOS = async () => {
    if (!user_id) return [];
    try {
      // Use the new combined API endpoint for better performance
      const response = await axios.get(`adminside/api/alert/user-alerts-and-sos/`, {
        params: { user_id }
      });

      const alerts = Array.isArray(response.data?.data?.alerts) ? response.data.data.alerts : [];
      const sosData = Array.isArray(response.data?.data?.sos) ? response.data.data.sos : [];

      // Transform and combine data
      const transformedAlerts = alerts.map(alert => ({
        ...alert,
        type: 'Alert',
        category_name: alert.category?.name,
        date: alert.alert_date || (alert.created_at ? format(new Date(alert.created_at), 'dd/MM/yyyy') : null),
        time: alert.alert_time || (alert.created_at ? format(new Date(alert.created_at), 'HH:mm') : null),
        evidence_url: alert.evidence_url,
        shield: alert.shield
      }));

      const transformedSos = sosData.map(sos => ({
        ...sos,
        type: 'SOS',
        date: sos.alert_date || (sos.created_at ? format(new Date(sos.created_at), 'dd/MM/yyyy') : null),
        time: sos.alert_time || (sos.created_at ? format(new Date(sos.created_at), 'HH:mm') : null),
        evidence_url: sos.evidence_url || sos.evidence,
        shield: sos.shield
      }));

      const combined = [...transformedAlerts, ...transformedSos]
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      return combined;
    } catch (error) {
      console.error("Error fetching alerts and SOS:", error);
      return [];
    }
  };

  const { data: alertsAndSOS = [], isLoading } = useQuery(
    [`user-${user_id}-alerts-sos`],
    fetchUserAlertsAndSOS,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  return (
    <>
      <UserLayout pageTitle="Usuarios" headerTitle="Usuarios">
        <div className="mt-5">
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Tipo</Table.Th>
                <Table.Th>Ubicación</Table.Th>
                <Table.Th>Fecha</Table.Th>
                <Table.Th>Hora</Table.Th>
                <Table.Th>Evidencia</Table.Th>
                <Table.Th>Escudo</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {isLoading ? (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center">
                    Cargando...
                  </Table.Td>
                </Table.Tr>
              ) : alertsAndSOS.length > 0 ? (
                alertsAndSOS.map((item, index) => (
                  <Table.Tr key={item.id || index}>
                    <Table.Td>
                      <p className={`font-semibold ${item.type === 'SOS' ? 'text-danger' : ''}`}>
                        {item.type === 'SOS' ? 'SOS' : `Alerta - ${item.category_name || 'General'}`}
                      </p>
                      {/* Only show # and number, not type again */}
                      <p>#{item.num || item.id}</p>
                    </Table.Td>
                    <Table.Td>
                      {item.lat && item.long ? `${item.lat}, ${item.long}` : 'No disponible'}
                    </Table.Td>
                    <Table.Td>
                      {item.date || 'No disponible'}
                    </Table.Td>
                    <Table.Td>
                      {item.time || 'No disponible'}
                    </Table.Td>
                    <Table.Td>
                      {(() => {
                        const evidenceNumber = item.evidence_number || item.num || item.id;

                        // Always show as clickable - modal will handle missing evidence as per CA0104
                        return (
                          <EvidenceModalBtn alert={item} className="text-primary">
                            {evidenceNumber}
                          </EvidenceModalBtn>
                        );
                      })()}
                    </Table.Td>
                    <Table.Td className="font-semibold">
                      {item.shield?.shield_code || 'No asignado'}
                    </Table.Td>
                  </Table.Tr>
                ))
              ) : (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center">
                    No hay alertas o SOS registrados para este usuario
                  </Table.Td>
                </Table.Tr>
              )}
            </Table.Tbody>
          </Table>
        </div>
      </UserLayout>
    </>
  );
};

export default SOS;
