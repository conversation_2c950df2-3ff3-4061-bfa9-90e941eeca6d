#!/usr/bin/env python3
"""
Validation script for Ma<PERSON> Backend Postman Collection
This script validates the structure and completeness of the Postman collection.
"""

import json
import sys
from pathlib import Path

def validate_collection():
    """Validate the Postman collection structure and content."""
    
    collection_file = Path("Mas_Seguros_Backend_API.postman_collection.json")
    environment_file = Path("Mas_Seguros_Environments.postman_environment.json")
    
    # Check if files exist
    if not collection_file.exists():
        print("❌ Collection file not found!")
        return False
    
    if not environment_file.exists():
        print("❌ Environment file not found!")
        return False
    
    try:
        # Load and validate collection
        with open(collection_file, 'r', encoding='utf-8') as f:
            collection = json.load(f)
        
        # Load and validate environment
        with open(environment_file, 'r', encoding='utf-8') as f:
            environment = json.load(f)
        
        print("✅ JSON files are valid")
        
        # Validate collection structure
        required_collection_fields = ['info', 'item', 'variable']
        for field in required_collection_fields:
            if field not in collection:
                print(f"❌ Missing required field in collection: {field}")
                return False
        
        # Count endpoints by category
        categories = {}
        total_endpoints = 0
        
        def count_endpoints(items, category_name=""):
            nonlocal total_endpoints
            count = 0
            for item in items:
                if 'item' in item:  # This is a folder
                    folder_name = item.get('name', 'Unknown')
                    folder_count = count_endpoints(item['item'], folder_name)
                    categories[folder_name] = folder_count
                    count += folder_count
                else:  # This is an endpoint
                    count += 1
                    total_endpoints += 1
            return count
        
        count_endpoints(collection['item'])
        
        print(f"\n📊 Collection Statistics:")
        print(f"Total Endpoints: {total_endpoints}")
        print(f"Total Categories: {len(categories)}")
        
        print(f"\n📁 Endpoints by Category:")
        for category, count in categories.items():
            print(f"  • {category}: {count} endpoints")
        
        # Validate environment variables
        env_vars = {var['key']: var['value'] for var in environment['values']}
        required_env_vars = ['base_url', 'auth_token', 'user_id']
        
        print(f"\n🔧 Environment Variables:")
        for var in required_env_vars:
            if var in env_vars:
                print(f"  ✅ {var}")
            else:
                print(f"  ❌ Missing: {var}")
        
        # Check for authentication setup
        auth_endpoints = []
        
        def find_auth_endpoints(items):
            for item in items:
                if 'item' in item:
                    if item.get('name') == 'Authentication':
                        for auth_item in item['item']:
                            auth_endpoints.append(auth_item.get('name'))
                    else:
                        find_auth_endpoints(item['item'])
        
        find_auth_endpoints(collection['item'])
        
        print(f"\n🔐 Authentication Endpoints:")
        for endpoint in auth_endpoints:
            print(f"  • {endpoint}")
        
        # Validate that login endpoint has test script for token extraction
        login_has_test = False
        
        def check_login_test(items):
            nonlocal login_has_test
            for item in items:
                if 'item' in item:
                    check_login_test(item['item'])
                elif item.get('name') == 'Login User':
                    if 'event' in item and any(event.get('listen') == 'test' for event in item['event']):
                        login_has_test = True
        
        check_login_test(collection['item'])
        
        if login_has_test:
            print("✅ Login endpoint has test script for token extraction")
        else:
            print("⚠️  Login endpoint missing test script for token extraction")
        
        print(f"\n✅ Collection validation completed successfully!")
        print(f"📄 Collection Name: {collection['info']['name']}")
        print(f"📝 Description: {collection['info']['description']}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return False

def main():
    """Main function to run validation."""
    print("🔍 Validating Mas Seguros Backend Postman Collection...")
    print("=" * 60)
    
    if validate_collection():
        print("\n🎉 All validations passed! The collection is ready to use.")
        sys.exit(0)
    else:
        print("\n💥 Validation failed! Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
