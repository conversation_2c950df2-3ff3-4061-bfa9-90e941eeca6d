from django.urls import path, reverse
# from Account.views_api import *
from Account import views as account_views
from AdminSide.Roles import views as roles_views

urlpatterns = [
    path('register/', account_views.RegisterApi.as_view(), name='register'),
    path('login/', account_views.LoginApi.as_view(), name='login'),
    path('logout/', account_views.LogoutApi.as_view(), name='logout'),
    path('sendverificationcode/', account_views.SendVerificationCode.as_view(), name='send-verification-code'),
    path('forgetpasswordverifycode/', account_views.ForgetPasswordVerifyCodeApi.as_view(),
         name='forget-password-verification-code'),
    path('setnewpassword/', account_views.SetNewPasswordApi.as_view(), name='set-new-password'),
    path('confirmcurrentpasschangepassword/', account_views.ConfirmCurrentPassChangePassword.as_view(),
         name='confirm-current-pass-change_password'),
    path('changepassword/', account_views.ChangePassword.as_view(), name='change_password'),
    path('enablelocation/', account_views.EnableLocation.as_view(), name='enable-location'),
    path('realtimelocation/', account_views.RealTimeLocation.as_view(), name='real-time-location'),
    path('editprofile/', account_views.EditProfile.as_view(), name='edit-profile'),
    path('changephone/', account_views.ChangePhone.as_view(), name='change-phone'),
    # path('changeprofileimage/', account_views.ChangeProfileImage.as_view(), name='change-profile-image'),
    path('deleteaccount/', account_views.DeleteAccount.as_view(), name='delete-account'),
    path('accounts/', account_views.Account.as_view(), name='get-accounts'),
    path('fcm-device-token/', account_views.FcmDeviceToken.as_view(), name='fcm-device-token'),

    # Admin registration endpoint for frontend compatibility
    path('adminregister/', roles_views.CreateAdmin.as_view(), name='admin-register'),

    # get all admins
    # path('getadmin/',account_views.GetadminUsers.as_view()),

]


def get_login_url():
    return reverse("user-login")


def get_register_url():
    return reverse("register-user")


def get_verify_email_url(username, code):
    return reverse("email-verify", kwargs={"username": username, "code": code})


def get_reset_password_url():
    return reverse("reset-password")


def send_verification_code(username):
    return reverse("send-verification-code", kwargs={"username": username})


def set_user_password(username, code):
    return reverse("set-user-password", kwargs={"username": username, "code": code})


def confirm_reset_password_code(username, code=0):
    return reverse("confirm-reset-password-code", kwargs={"username": username, "code": code})
