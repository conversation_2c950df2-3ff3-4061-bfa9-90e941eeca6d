import React from "react";
import Link from "next/link";
import { ChevronRightIcon } from "@heroicons/react/20/solid";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

const SmallAnalyticsCard = ({
  title = "",
  items = [],
  footer = {},
  isLoading = false,
  isError = false
}) => {
  // Function to render the appropriate content based on loading/error state
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="animate-pulse text-center">
            <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
            <div className="h-6 w-12 bg-gray-200 rounded"></div>
          </div>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="flex items-center justify-center h-full text-center text-warning">
          <div>
            <ExclamationTriangleIcon className="mx-auto h-5 w-5" />
            <p className="mt-1 text-xs"><PERSON>rror al cargar datos</p>
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2">
        {items.map((item) => {
          return (
            <div key={item.title} className="space-y-1">
              <dd className="text-xs text-secondary">{item.title}</dd>
              <dd className="text-2xl">{item.count !== undefined ? item.count : 0}</dd>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="flex h-1/2 flex-col divide-y bg-white">
      <div className="flex-grow p-4">
        <h3 className="mb-2 text-sm font-semibold">{title}</h3>
        {renderContent()}
      </div>
      <Link
        href={footer.href}
        className="flex items-center justify-between px-4 py-2.5 text-sm text-primary"
      >
        <span>{footer.title}</span>
        <ChevronRightIcon className="h-5 w-5" />
      </Link>
    </div>
  );
};

export default SmallAnalyticsCard;
