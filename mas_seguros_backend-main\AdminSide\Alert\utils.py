from mas_seguros_backend import prefixes as backend_prefixes
from Alert import models as alert_models


def generate_prefix_number(userprofile):
    new_alert_num = backend_prefixes.get_new_prefix_number(userprofile=userprofile,
                                                           _model=alert_models.AlertModel,
                                                           field_name='num',
                                                           prefix=backend_prefixes.ALERT)
    return new_alert_num


def check_if_unsolved_alert_exist(user):
    alert_status = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SOLVED).last()
    all_alerts = alert_models.AlertModel.objects.filter(userprofile=user.userprofile).count()
    if all_alerts:
        completed_alerts = alert_models.AlertModel.objects.filter(userprofile=user.userprofile,
                                                                  status=alert_status).count()
        if all_alerts == completed_alerts:
            return True
        return False
    return True
