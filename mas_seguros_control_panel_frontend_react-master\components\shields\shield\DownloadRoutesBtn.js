import InputGroup from "@/components/utility/InputGroup";
import Modal from "@/components/utility/Modal";
import React, { createElement, useState, useEffect } from "react";
import useAxios from "@/hooks/useAxios";
import { useRouter } from "next/router";
import { toast } from "react-hot-toast";
import { useQuery } from "react-query";
import { utils, writeFile } from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from "date-fns";

const DownloadRoutesBtn = ({ as = "button", defaultMemberId = "", members: propMembers = [], ...props }) => {
  const [open, setOpen] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedMemberId, setSelectedMemberId] = useState("");
  const [fileFormat, setFileFormat] = useState("xlsx");
  const [isExporting, setIsExporting] = useState(false);
  const { axios } = useAxios();
  const router = useRouter();
  const { shield_id } = router.query;

  // Fetch shield members
  const fetchMembers = () => {
    return axios.get(`adminside/api/shield/shield-members/?id=${shield_id}`);
  };

  const { data: membersResponse, isLoading: membersLoading } = useQuery(
    `shield-${shield_id}-members-download`,
    fetchMembers,
    {
      refetchOnWindowFocus: false,
      enabled: !!shield_id && open && propMembers.length === 0 // Only fetch if members not provided as props
    }
  );

  // Use provided members or fetch from API
  const members = propMembers.length > 0 ? propMembers : (Array.isArray(membersResponse?.data?.data) ? membersResponse?.data?.data : []);

  // Reset form when modal opens and set default member
  useEffect(() => {
    if (open) {
      setStartDate("");
      setEndDate("");
      setSelectedMemberId(defaultMemberId || "");
      setFileFormat("xlsx");
    }
  }, [open, defaultMemberId]);

  const close = () => {
    setOpen(false);
  };

  const handleDownload = async () => {
    // Validation
    if (!selectedMemberId) {
      toast.error("Por favor selecciona un miembro");
      return;
    }

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (start > end) {
        toast.error("La fecha de inicio debe ser anterior a la fecha de fin");
        return;
      }
    }

    setIsExporting(true);
    try {
      // Fetch route data from the existing API
      console.log('Fetching routes for:', { shield_id, selectedMemberId });
      const response = await axios.get(`adminside/api/shield/shield-members-routes/?shield_id=${shield_id}&member_id=${selectedMemberId}`);

      console.log('API Response:', response);
      console.log('Route data:', response?.data);

      if (!response?.data || !Array.isArray(response.data) || response.data.length === 0) {
        toast.error("¡No hay rutas disponibles para este miembro!");
        return;
      }

      // Filter data based on date range if provided
      let filteredData = response.data;
      if (startDate || endDate) {
        filteredData = filteredData.filter(route => {
          if (!route.route_date) return false;
          const routeDate = new Date(route.route_date);

          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            return routeDate >= start && routeDate <= end;
          } else if (startDate) {
            const start = new Date(startDate);
            return routeDate >= start;
          } else if (endDate) {
            const end = new Date(endDate);
            return routeDate <= end;
          }
          return true;
        });
      }

      if (filteredData.length === 0) {
        toast.error("¡No hay rutas en el período de tiempo seleccionado!");
        return;
      }

      // Get member info for filename
      const selectedMember = members.find(m => (m.member || m)?.user?.id == selectedMemberId);
      const memberName = (selectedMember?.member || selectedMember)?.full_name || 'miembro';

      console.log('Starting file generation:', { fileFormat, memberName, routeCount: filteredData.length });

      if (fileFormat === 'pdf') {
        await generatePDF(filteredData, memberName);
      } else {
        await generateExcel(filteredData, memberName);
      }

      toast.success(`${fileFormat.toUpperCase()} descargado correctamente`);
      close();

    } catch (error) {
      console.error("Error downloading routes:", error);

      if (error.response) {
        const status = error.response.status;
        if (status === 404) {
          toast.error("No se encontraron rutas para descargar.");
        } else if (status === 500) {
          toast.error("Error del servidor. Intenta más tarde.");
        } else {
          toast.error(`Error ${status}: ${error.response.data?.msg || "Error al descargar"}`);
        }
      } else if (error.request) {
        toast.error("Error de conexión. Verifica tu conexión a internet.");
      } else if (error.message) {
        // This will catch our custom errors from generatePDF/generateExcel
        toast.error(error.message);
      } else {
        toast.error("Error inesperado al generar el archivo. Revisa la consola para más detalles.");
      }
    } finally {
      setIsExporting(false);
    }
  };

  const generateExcel = async (routes, memberName) => {
    try {
      console.log('Generating Excel with routes:', routes);
      console.log('Member name:', memberName);

      // Create workbook
      const wb = utils.book_new();

      // Helper function to safely format dates
      const safeFormatDate = (dateValue, formatString) => {
        if (!dateValue) return 'N/A';
        try {
          const date = new Date(dateValue);
          if (isNaN(date.getTime())) return 'N/A';

          // Simple date formatting without date-fns as fallback
          if (formatString === 'dd/MM/yyyy') {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
          }

          return format(date, formatString);
        } catch (error) {
          console.warn('Date formatting error:', error, 'for value:', dateValue);
          // Fallback: try to extract date parts manually
          if (typeof dateValue === 'string' && dateValue.includes('-')) {
            const parts = dateValue.split('T')[0].split('-');
            if (parts.length === 3) {
              return `${parts[2]}/${parts[1]}/${parts[0]}`;
            }
          }
          return 'N/A';
        }
      };

      // Helper function to safely format time
      const safeFormatTime = (timeValue) => {
        if (!timeValue) return 'N/A';
        try {
          // Handle different time formats
          let timeString = timeValue;
          if (typeof timeValue === 'string' && !timeValue.includes('T')) {
            timeString = `2000-01-01T${timeValue}`;
          }
          const date = new Date(timeString);
          if (isNaN(date.getTime())) {
            // Fallback: if it's already in HH:mm format, return as is
            if (typeof timeValue === 'string' && timeValue.match(/^\d{2}:\d{2}/)) {
              return timeValue.substring(0, 5);
            }
            return timeValue || 'N/A';
          }

          // Simple time formatting without date-fns as fallback
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          return `${hours}:${minutes}`;
        } catch (error) {
          console.warn('Time formatting error:', error, 'for value:', timeValue);
          return timeValue || 'N/A';
        }
      };

      // Create data for Excel
      const excelData = [
        ["ID Ruta", "Fecha", "Hora Inicio", "Hora Fin", "Velocidad Máxima (km/h)", "Batería Mínima (%)", "Ruta Completada", "Origen", "Destino"],
        ...routes.map(route => {
          console.log('Processing route:', route);
          return [
            `Ruta #${route.route_id || route.id || 'N/A'}`,
            safeFormatDate(route.route_date, 'dd/MM/yyyy'),
            safeFormatTime(route.route_starting_time),
            safeFormatTime(route.route_ending_time),
            route.max_speed || 'N/A',
            route.minimum_phone_battery || 'N/A',
            route.route_completed ? 'Sí' : 'No',
            route.starting_poi || 'N/A',
            route.ending_poi || 'N/A'
          ];
        })
      ];

      console.log('Excel data prepared:', excelData);

      // Create worksheet and add to workbook
      const ws = utils.aoa_to_sheet(excelData);
      utils.book_append_sheet(wb, ws, "Historial de Rutas");

      // Generate filename
      const now = new Date();
      const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
      const safeMemberName = memberName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const fileName = `Rutas_${safeMemberName}_${timestamp}.xlsx`;

      console.log('Downloading file:', fileName);

      // Write and download the file
      writeFile(wb, fileName);
    } catch (error) {
      console.error("Excel generation error:", error);
      throw new Error(`Error al generar el archivo Excel: ${error.message}`);
    }
  };

  const generatePDF = async (routes, memberName) => {
    try {
      console.log('Generating PDF with routes:', routes);
      console.log('Member name:', memberName);

      const doc = new jsPDF();

      // Title
      doc.setFontSize(16);
      doc.text('Historial de Rutas', 20, 20);

      // Member info
      doc.setFontSize(12);
      doc.text(`Miembro: ${memberName}`, 20, 35);

      // Helper function to safely format dates
      const safeFormatDate = (dateValue, formatString) => {
        if (!dateValue) return 'N/A';
        try {
          const date = new Date(dateValue);
          if (isNaN(date.getTime())) return 'N/A';

          // Simple date formatting without date-fns as fallback
          if (formatString === 'dd/MM/yyyy') {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
          }

          return format(date, formatString);
        } catch (error) {
          console.warn('Date formatting error:', error, 'for value:', dateValue);
          // Fallback: try to extract date parts manually
          if (typeof dateValue === 'string' && dateValue.includes('-')) {
            const parts = dateValue.split('T')[0].split('-');
            if (parts.length === 3) {
              return `${parts[2]}/${parts[1]}/${parts[0]}`;
            }
          }
          return 'N/A';
        }
      };

      // Helper function to safely format time
      const safeFormatTime = (timeValue) => {
        if (!timeValue) return 'N/A';
        try {
          let timeString = timeValue;
          if (typeof timeValue === 'string' && !timeValue.includes('T')) {
            timeString = `2000-01-01T${timeValue}`;
          }
          const date = new Date(timeString);
          if (isNaN(date.getTime())) {
            // Fallback: if it's already in HH:mm format, return as is
            if (typeof timeValue === 'string' && timeValue.match(/^\d{2}:\d{2}/)) {
              return timeValue.substring(0, 5);
            }
            return timeValue || 'N/A';
          }

          // Simple time formatting without date-fns as fallback
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          return `${hours}:${minutes}`;
        } catch (error) {
          console.warn('Time formatting error:', error, 'for value:', timeValue);
          return timeValue || 'N/A';
        }
      };

      // Date range info
      let dateInfo = '';
      try {
        if (startDate && endDate) {
          dateInfo = `Período: ${safeFormatDate(startDate, 'dd/MM/yyyy')} - ${safeFormatDate(endDate, 'dd/MM/yyyy')}`;
        } else if (startDate) {
          dateInfo = `Desde: ${safeFormatDate(startDate, 'dd/MM/yyyy')}`;
        } else if (endDate) {
          dateInfo = `Hasta: ${safeFormatDate(endDate, 'dd/MM/yyyy')}`;
        } else {
          dateInfo = 'Período: Todas las rutas';
        }
      } catch (error) {
        console.warn('Date range formatting error:', error);
        dateInfo = 'Período: Todas las rutas';
      }

      doc.text(dateInfo, 20, 45);

      // Table data
      const tableData = routes.map(route => {
        console.log('Processing route for PDF:', route);
        return [
          `Ruta #${route.route_id || route.id || 'N/A'}`,
          safeFormatDate(route.route_date, 'dd/MM/yyyy'),
          safeFormatTime(route.route_starting_time),
          route.max_speed || 'N/A',
          route.minimum_phone_battery || 'N/A',
          route.route_completed ? 'Sí' : 'No'
        ];
      });

      console.log('PDF table data prepared:', tableData);

      // Add table using autoTable
      autoTable(doc, {
        head: [['ID Ruta', 'Fecha', 'Hora Inicio', 'Velocidad (km/h)', 'Batería (%)', 'Completada']],
        body: tableData,
        startY: 55,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [66, 139, 202] }
      });

      // Generate filename
      const now = new Date();
      const timestamp = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
      const safeMemberName = memberName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
      const fileName = `Rutas_${safeMemberName}_${timestamp}.pdf`;

      console.log('Downloading PDF file:', fileName);

      // Save the PDF
      doc.save(fileName);
    } catch (error) {
      console.error("PDF generation error:", error);
      throw new Error(`Error al generar el archivo PDF: ${error.message}`);
    }
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-2xl overflow-hidden rounded bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Descargar Rutas</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="space-y-7 !py-10">
            <div className="space-y-6">
              {/* Member Selection */}
              <div>
                <InputGroup.Label>Miembro</InputGroup.Label>
                <InputGroup>
                  <select
                    value={selectedMemberId}
                    onChange={(e) => setSelectedMemberId(e.target.value)}
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                  >
                    <option value="">Seleccionar miembro...</option>
                    {(membersLoading && propMembers.length === 0) ? (
                      <option disabled>Cargando miembros...</option>
                    ) : (
                      members.map((memberData) => {
                        const member = memberData.member || memberData;
                        return (
                          <option key={member?.user?.id} value={member?.user?.id}>
                            {member.full_name} (ID: {member?.user?.id})
                          </option>
                        );
                      })
                    )}
                  </select>
                </InputGroup>
              </div>

              {/* Date Range */}
              <div>
                <InputGroup.Label>Rango de Fechas</InputGroup.Label>
                <div className="flex items-center gap-3">
                  <div className="flex-1">
                    <InputGroup>
                      <InputGroup.Input
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        placeholder="Fecha inicio"
                      />
                    </InputGroup>
                  </div>
                  <div className="flex flex-shrink-0 items-center justify-center px-2 text-gray-500">
                    hasta
                  </div>
                  <div className="flex-1">
                    <InputGroup>
                      <InputGroup.Input
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        placeholder="Fecha fin"
                      />
                    </InputGroup>
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Opcional: Deja vacío para descargar todas las rutas
                </p>
              </div>
            </div>
            <div>
              <InputGroup.Label>Formato</InputGroup.Label>
              <div className="flex gap-7 text-sm font-medium">
                <label className="inline-flex items-center gap-1.5">
                  <input
                    checked={fileFormat === "pdf"}
                    onChange={() => setFileFormat("pdf")}
                    name="format"
                    type="radio"
                    className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                  />
                  .PDF
                </label>
                <label className="inline-flex items-center gap-1.5">
                  <input
                    checked={fileFormat === "xlsx"}
                    onChange={() => setFileFormat("xlsx")}
                    name="format"
                    type="radio"
                    className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                  />
                  .XLSX
                </label>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-gray-300 text-gray-700">
              Cancelar
            </Modal.FooterBtn>
            <Modal.FooterBtn
              onClick={handleDownload}
              className="bg-black text-white"
              disabled={isExporting || !selectedMemberId}
            >
              {isExporting ? "Descargando..." : "Descargar"}
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        ...props,
        onClick: () => setOpen(true),
      })}
    </>
  );
};

export default DownloadRoutesBtn;
