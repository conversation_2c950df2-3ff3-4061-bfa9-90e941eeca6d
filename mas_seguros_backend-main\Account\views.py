from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.core.mail import send_mail
from rest_framework.generics import get_object_or_404
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status, generics
from rest_framework.response import Response
from Account import models as account_models, utils as account_utils, serializers as account_user_auth_serializers
from mas_seguros_backend import utils as backend_utils
from rest_framework.authtoken.models import Token
from datetime import datetime
from django.contrib.auth.hashers import check_password

from mas_seguros_backend.settings import from_email
from rest_framework.pagination import PageNumberPagination


class RegisterApi(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.RegistrationSerializer

    def post(self, request):
        full_name = request.data.get('full_name')
        full_name = full_name.split(' ')
        first_name = full_name[0]
        last_name = full_name[1]
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        check_email_and_phone = account_utils.validate_email_phone(request.data.get('email'), request.data.get('phone'))
        if check_email_and_phone[0]:
            new_birthdate = datetime.strptime(request.data.get('birth_date'), '%d/%m/%Y').strftime('%Y-%m-%d')
            user = User.objects.create_user(email=(request.data.get('email').lower()),
                                            username=request.data.get('email'),
                                            first_name=first_name,
                                            last_name=last_name,
                                            )
            user.is_active = True
            user.set_password(request.data.get('password'))
            user.save()
            ui_id = account_utils.generate_prefix_number()

            user_profile = account_models.UserProfile.objects.create(user=user, identification_card=request.data.get(
                'identification_card'), phone=request.data.get('phone'),
                                                                     full_name=request.data.get('full_name'),
                                                                     birth_date=new_birthdate,
                                                                     verification_code=account_utils.random_digits(),
                                                                     role=account_models.normal_user,
                                                                     email_verified=True)
            user_profile.ui_id = ui_id

            user_profile.save()

            token = Token.objects.get_or_create(user=user)[0].key
            get_user = get_object_or_404(User, email=user.username)
            # context = {'first_name': get_user.first_name, 'code': user_profile.full_name,
            #            'username': get_user.username,
            #            'message': 'enter the code in your app in order to'
            #                       ' verify your email.'
            #            }
            # account_utils.thread_making(backend_utils.send_email, ["Fittech Password Reset", context, get_user])
            user_profile_serializer = account_user_auth_serializers.UserProfileSerializer(user_profile)
            user_profile_serializer = user_profile_serializer.data
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=user_profile_serializer,
                                               msg='User Registered Successfully'), status=status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=check_email_and_phone[1]), status=status.HTTP_400_BAD_REQUEST)


class LoginApi(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.LoginSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            return self.on_valid_request_data(serializer.validated_data, request)
        else:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    errors=serializer.errors,
                    msg="Validation failed"
                ),
                status.HTTP_400_BAD_REQUEST
            )

    def on_valid_request_data(self, data, request):
        phone = data.get('phone')
        password = data.get('password')

        # Use our custom authentication backend
        user = authenticate(request=request, phone=phone, password=password)

        if user is not None:
            user_profile = account_models.UserProfile.objects.get_or_create(user=user)[0]
            user_profile_serializer = account_user_auth_serializers.UserProfileSerializerForLogin(user_profile)
            user_profile_serializer = user_profile_serializer.data
            token, created = Token.objects.get_or_create(user=user)

            admin_obj = account_models.UserProfile.objects.filter(user__is_staff=True).last()
            response = {
                'token': token.key,
                'user_profile': user_profile_serializer,
                'admin_details': {
                    'id': admin_obj.user.id,
                    'email': admin_obj.user.email,
                    'username': "MasSeguros"
                }
            }
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=response,
                                               msg='User Login Successfully'))

        return Response(
            backend_utils.failure_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                msg="La contraseña que ha ingresado es incorrecta."
            ),
            status.HTTP_400_BAD_REQUEST
        )


class LogoutApi(APIView):
    # permission_classes = (IsAuthenticated,)

    def post(self, request):
        # logout(request)
        request.user.auth_token.delete()
        response = {
        }
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=response,
                                           msg='User Logged out Successfully'))


class SendVerificationCode(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.SendVerificationCode

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            requested_data = request.data
            email = requested_data.get('email')
            try:
                get_user = User.objects.get(email=email.lower())
                get_userprofile = get_user.userprofile

                # Check if user is an admin
                if get_userprofile.role == account_models.web_admin:
                    # Check if admin is suspended
                    if get_userprofile.suspend:
                        return Response(backend_utils.failure_response(
                            status_code=status.HTTP_403_FORBIDDEN,
                            errors={"email": ["Esta cuenta de administrador está suspendida."]},
                            msg="Esta cuenta de administrador está suspendida."
                        ), status.HTTP_403_FORBIDDEN)

                # Generate a random password
                new_password = account_utils.generate_random_password()

                # Set the new password for the user
                get_user.set_password(new_password)
                get_user.save()

                # Update verification code (for compatibility with existing code)
                get_userprofile.verification_code = account_utils.random_digits()
                get_userprofile.save()

                context = {
                    'first_name': get_user.first_name,
                    'username': get_user.username,
                    'message': 'Tu nueva contraseña para acceder al panel de administración.'
                }

                # Try to send email with the new password
                email_sent = backend_utils.send_email(
                    "Nueva contraseña para Mas Seguros",
                    context,
                    get_user,
                    new_password=new_password
                )

                if email_sent:
                    return Response(backend_utils.success_response(
                        msg='La nueva contraseña ha sido enviada a tu correo electrónico'
                    ))
                else:
                    # For development/testing, return success even if email fails
                    # In production, you might want to return an error instead
                    backend_utils.logger(f"Email sending failed")
                    return Response(backend_utils.success_response(
                        msg='Email sending failed.'
                    ))

            except User.DoesNotExist:
                return Response(backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    errors={"email": ["El correo electrónico ingresado no está registrado."]},
                    msg="El correo electrónico ingresado no está registrado."
                ), status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                backend_utils.logger(f"Error in SendVerificationCode: {str(e)}")
                return Response(backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    errors={"general": ["Error interno del servidor."]},
                    msg="Error interno del servidor."
                ), status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            # Ensure consistent error format
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    errors=serializer.errors,
                    msg="Validation failed"
                ),
                status.HTTP_400_BAD_REQUEST
            )


class ForgetPasswordVerifyCodeApi(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.PhoneVerifySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            backend_utils.logger(f"Validation failed: {serializer.errors}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    errors=serializer.errors,
                    msg="Validation failed"
                ),
                status.HTTP_400_BAD_REQUEST
            )

        requested_data = request.data
        code = requested_data.get('code')
        email = requested_data.get('email').lower()

        # Log the verification attempt for debugging
        backend_utils.logger(f"Verifying code: {code} for email: {email}")

        user_profile = account_utils.get_user_profile(email=email, code=code)
        if user_profile:
            user_profile.verification_code = account_utils.random_digits()
            # to change code immediately to avoid future attacks
            user_profile.email_verified = True
            user_profile.save()
            return Response(backend_utils.success_response(msg="Email verified successfully"))
        else:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg="El código que ingresaste es incorrecto."
                ),
                status.HTTP_400_BAD_REQUEST
            )


class SetNewPasswordApi(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.SetNewPasswordSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        password = request.data.get('new_password')
        email = request.data.get('email').lower()
        print("this is the password coming ====", password)
        print("this is the email coming ====", email)
        # code = request.data.get('code')
        try:
            user_profile = account_utils.get_user_profile(email)
            print("this is userprofile==", user_profile)
            matchcheck = check_password(password, user_profile.user.password)
            print("========1===========")
            if not matchcheck:
                print("========2===========")
                user_profile.user.set_password(password)
                user_profile.user.save()
                user_profile.verification_code = account_utils.random_digits()
                user_profile.save()
                print("========3===========")
                message = "Password Reset Successfully!"
                print("========4===========")
                return Response(backend_utils.success_response(status_code=status.HTTP_200_OK, data=None,
                                                               msg=message))
            else:
                print("========5===========")
                return Response(backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                               msg='La nueva contraseña no puede ser la contraseña anterior'),
                                status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print("=====this is the error==", e)
            return Response(backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                           msg='Something went wrong'), status.HTTP_400_BAD_REQUEST)


class ConfirmCurrentPassChangePassword(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.ConfirmCurrentPasswordSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        requested_data = request.data
        current_password = requested_data.get('current_password')
        matchcheck = check_password(current_password, request.user.password)
        if matchcheck:
            return Response(backend_utils.success_response(msg="password matched Successfully"))
        return Response(backend_utils.failure_response(msg="La contraseña no coincide."), status.HTTP_400_BAD_REQUEST)


class ChangePassword(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.PasswordCreationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        get_username = request.user.username
        user_profile = account_utils.get_user_profile(username=get_username)
        requested_data = request.data
        new_password = requested_data.get('new_password')
        if user_profile:
            matchcheck = check_password(new_password, request.user.password)
            if not matchcheck:
                user_profile.user.set_password(new_password)
                user_profile.user.save()
                return Response(backend_utils.success_response(msg="password changed Successfully"))
            return Response(backend_utils.failure_response(msg="Contraseña No puede ser una contraseña anterior"),
                            status.HTTP_400_BAD_REQUEST)
        return Response(backend_utils.failure_response(msg="no user with this verification code found"),
                        status.HTTP_400_BAD_REQUEST)


class EnableLocation(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.EnableLocationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        enable_location = request.data.get('enable_location')
        userprofile = request.user.userprofile
        userprofile.enable_location = enable_location
        userprofile.save()
        return Response(backend_utils.success_response(msg="Location Bool Updated Successfully"))


class RealTimeLocation(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.RealTimeLocationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        lat = request.data.get('lat')
        long = request.data.get('long')
        userprofile = request.user.userprofile
        userprofile.lat = lat
        userprofile.long = long
        userprofile.save()
        return Response(backend_utils.success_response(msg="Location Updated Successfully"))

    def get(self, request):
        userprofile = request.user.userprofile
        serializer = account_user_auth_serializers.RealTimeLocationSerializer(userprofile)
        return Response(backend_utils.success_response(msg="Location Parameters", data=serializer.data))


# class ChangeProfileImage(APIView):
#     permission_classes = (IsAuthenticated,)
#     serializer_class = account_user_auth_serializers.ChangeProfileImageSerializer
#
#     def post(self, request):
#         serializer = self.serializer_class(data=request.data)
#         response = serializer.is_valid(raise_exception=False)
#         if response:
#             pass
#         else:
#             print(serializer.error_messages)
#             return Response(
#                 backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
#                                                msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
#
#         image = request.data.get('image')
#         user = request.user
#         user.userprofile.image = image
#         user.userprofile.save()
#         return Response(
#             backend_utils.success_response(status_code=status.HTTP_200_OK, data=user.userprofile.image.url,
#                                            msg='Image Updated Successfully'))


class EditProfile(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.EditProfileSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        full_name = request.data.get('full_name')
        image = request.data.get('image', None)
        splited_full_name = full_name.split(' ')
        first_name = splited_full_name[0]
        last_name = splited_full_name[1]
        email = request.data.get('email')
        new_birthdate = datetime.strptime(request.data.get('birth_date'), '%d/%m/%Y').strftime('%Y-%m-%d')
        identification_card = request.data.get('identification_card')
        user = request.user
        user.userprofile.full_name = full_name
        user.userprofile.identification_card = identification_card
        user.userprofile.birth_date = new_birthdate
        if image:
            user.userprofile.image = image
        user.userprofile.save()
        user.first_name = first_name
        user.last_name = last_name
        user.email = email
        user.save()
        user_profile_serializer = account_user_auth_serializers.UserProfileSerializer(user.userprofile)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=user_profile_serializer.data,
                                           msg='Profile Updated Successfully'))


class ChangePhone(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.ChangePhoneSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)

        phone = request.data.get('phone')
        user = request.user
        user.userprofile.phone = phone
        user.userprofile.save()
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='Phone Number Updated Successfully'))


class DeleteAccount(APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        user = request.user
        User.objects.get(id=user.id).delete()
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           msg='Eliminación de cuenta con éxito'))


class FcmDeviceToken(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = account_user_auth_serializers.FcmTokenSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        account_models.FcmDeviceRegistration.objects.create(userprofile=user.userprofile,
                                                            device_id=request.data.get('device_id'))
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           msg='device id saved successfully'))


class Account(APIView):
    # permission_classes = (IsAuthenticated,)
    def post(self, request):
        final_response = list()
        # page_size = int(request.GET.get("page_size", 10))
        # page_size = 10 if page_size < 1 else page_size
        if 'phone_numbers' in request.data:
            phone_numbers = request.data.get('phone_numbers')
            for obj in phone_numbers:
                object = account_models.UserProfile.objects.filter(phone=obj['phone']).last()
                if object:
                    response = dict()
                    response["status"] = True
                    response["user_id"] = object.user.id
                    response["phone_number"] = object.phone
                    response["name"] = obj['name']
                    response["image_url"] = object.image_url
                    final_response.append(response)
                else:
                    response = dict()
                    response["status"] = False
                    response["user_id"] = None
                    response["phone_number"] = obj['phone']
                    response["name"] = obj['name']
                    response["image_url"] = None
                    final_response.append(response)
        else:
            return Response(
                backend_utils.success_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='please enter valid response'), status.HTTP_400_BAD_REQUEST)

        # return account_utils.get_paginated_response_without_serializer(final_response, request, page_size)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=final_response,
                                           msg='Users'), status.HTTP_200_OK)
