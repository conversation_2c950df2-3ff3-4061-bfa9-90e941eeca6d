PROJECT CODE PROJECT NAME DATE 1116 SAFER 19/03/2025 User Stories - Administrator Panel TOC \h \u \z \t "Heading 1,1,Heading 2,2,Heading 3,3,Heading 4,4,Heading 5,5,Heading 6,6," User Stories - Administrator Panel 1 HU001. Administrator access to the panel (Login and password recovery) 2 HU002. Main dashboard display with general metrics and quick access to modules 5 HU003. Viewing and managing the user list 8 HU004. View detailed user profile and location history 11 HU005. Viewing shields to which the user belongs 14 HU006. Viewing the history of alerts and SOS events associated with the user 16 HU007. Viewing user membership history 18 HU008. Viewing the user's biometric history and downloading reports 20 HU009. Viewing and managing the list of registered companies 22 HU010. Viewing shields associated with a company 25 HU011. Viewing members associated with a company 27 HU012. Viewing promotion codes associated with a company 29 HU013. Viewing the history of memberships purchased by a company 31 HU014. Viewing and managing the list of shields 33 HU015. Viewing points of interest on a shield and its visit history 35 HU016. Viewing members of a shield 38 HU017. Viewing a shield's route history 40 HU018. Viewing Alerts and SOS from the shield 43 HU019. Viewing Shield Membership History 45 HU020 Viewing and searching shield chat history 47 HU021 Viewing and managing biometric records of Shield members 49 HU022 Promotional code management 51 HU023 Viewing details of promotional codes and stock consumption 53 HU024 Management of alerts and evidence of the Alerts and SOS module 55 HU025 Payments and Memberships 57 HU026 Membership Details 59 HU027 Documentation 61 HU028 Tickets and Frequently Asked Questions 63 HU029 Support 65 HU030 Administrator Role Management 67 HU001. Administrator access to the panel (Login and password recovery) ID History HU001 Name Administrator access to the panel (Login and password recovery) Weight 3 HISTORY ROLE System administrator Function Sign in or regain access to the administrative panel. Result The administrator successfully accesses the panel or starts the password recovery process. Normal Flow The administrator accesses the URL of the administrative panel. Display the welcome screen with the following elements: “SAFER” logo. Message: "Welcome administrator. Enter the information to continue." Input: Email (type: email). Input: Password (type: password, with show/hide icon). Button: “Login” (active only if both fields have content). Link: “I forgot my password”. The system validates: that he email have the correct format. that the password be at least 6 characters. If the credentials are correct → redirection to the Dashboard. If the credentials are invalid → messages under the corresponding fields. If you press "I forgot my password" → the recovery flow starts. Password Recovery Flow: When you click “I forgot my password” , the user is redirected to a screen with: Input: Email . Button: "To send" . The system validates the email format When valid, it displays a confirmation screen: “We send the password to your email” , along with a warning to check spam. Actions available on that screen: Log in again. Resend password (modal sample with ✔ “Password forwarded” + button Continue ). System Notifications ✔ Successful start: “Welcome to the Administrative Panel”. ❌ Invalid email: “Incorrect email format ”. ❌ Invalid password: “The password must be at least 6 characters.” ❌ Incorrect credentials: “Incorrect email or password”. ✔ Password sent: “We send the password to your email.” ✔ Successful resend: Modal with ✔ “Password forwarded”. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0001 Validation of empty fields Empty email or password Click on “Login” Show message "Required field" CA0002 Email format validation Invalid email Click on “Login” "Incorrect email format" message CA0003 Password length Less than 6 characters Click on “Login” Message “Minimum 6 characters” CA0004 Incorrect credentials Invalid data Click on “Login” “Incorrect email or password” message CA0005 Successful start Valid data Click on “Login” Redirection to Dashboard CA0006 Recovery access Click on "I forgot my password" Click Redirection to recovery screen CA0007 Recovery validation - empty email empty field Click on “Send” “This field is required” message CA0008 Recovery validation - invalid email Incorrect format Click on “Send” “Incorrect email format” message CA0009 Successful shipment Valid email Click on “Send” “We send the password to your email” screen CA0010 Password forwarding Click on “Resend password” Click Modal ✔ “Password forwarded” CA0011 Return to login Click on “Login again” Click Redirection to login screen HU002. Main dashboard display with general metrics and quick access to modules ID History HU002 Name Main dashboard display with general metrics and quick access to modules Weight 3 HISTORY ROLE System administrator Function View the statistical summary of the system and quickly access key modules. Result The administrator views the metrics of the month, growth metrics and can navigate directly to the related modules. Normal Flow After logging in, the administrator is automatically redirected to the Dashboard principal . At the top you see the message: “Welcome, [User Name]” . The Dashboard shows sections organized with data and quick access: Metrics of the month: Registered users (total number and last month) with details by type (Individuals, Companies, Suspended). The user selects See users , redirects us to the module Users Purchased memberships by type (Free, Basic, Professional, Business) with comparison of last month. The user selects View memberships , redirects us to the module Membership payments. Shields created with details by type (Individuals, Companies). The user selects See shields , redirects us to the module Shields. Alerts generated with details by type (Alerts, SOS). The user selects View alerts , redirects us to the module Alerts and SOS. Support Tickets with details by type (Total, Resolved). The user selects See support , redirects us to the module Medium. Promotion codes with details by type (Total, Overdue). The user selects See codes , redirects us to the module Promotion codes. with subtotals and access to each module. Each block has an action button: “View [module name]” for direct navigation. Growth Metrics: Bar chart: Number of registered users per month with accumulated total. Bar chart: No. of Shields created per month with accumulated total. In the upper right header you will find: Notifications icon with counter and drop-down menu containing: Notification list with: Notification name Notification or user image Description Time and date User name and avatar with dropdown menu containing: See my profile Sign out Month selector: allows you to view the data according to the selected month. Buttons: Download Excel and Download PDF to export the monthly report. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0012 Viewing general metrics Successful access to the panel Successful login All blocks with updated data are displayed CA0013 Viewing growth metrics Data available per month Change of month Updated graphs and totals CA0014 Quick navigation to modules Click on the “View [module]” button Click Redirection to the corresponding module CA0015 Metric export Click on the download Excel or PDF button Click Downloaded file with visible metrics CA0016 Profile access Click on user name/image Click Dropdown menu with visible options CA0017 Sign out Click on “Log out” Click User is redirected to login screen HU003. Viewing and managing the user list ID History HU003 Name Main dashboard display with general metrics and quick access to modules Weight 3 HISTORY ROLE System administrator Function Consult the list of registered users, apply filters and perform actions on each record. Result The administrator can easily find users, apply filters, and perform actions such as viewing details or suspending accounts. Normal Flow The administrator accesses the module Users from the side menu Display a table with the following columns: User ID Name Telephone Mail Creation Date Type (Ex: Corporate, Individual) Status (Active, Suspended) Action At the top it has: Free search engine (by name, email, telephone). Filters button: allows you to filter by status, user type and creation date. The table supports pagination and shows the total number of records displayed. in the column Action , each row contains a dropdown with options: See details Suspend account Validations and Expected Behaviors If the search does not find matches → “No results found”. The status change (Suspend account) must be updated in real time. The button See details redirects to the user's file (HU next). Table must maintain consistency between pages navigated. System Notifications ✔ User suspended → “User's account has been successfully suspended.” ❌ Loading error → “It was not possible to load the users, try again.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0018 Correct display of the list Access to the module Click on “Users” Show table with updated data CA0019 Effective search Text entry Write in search engine Show filtered results CA0020 Filter application Filter selection Apply filter Update results in table CA0021 Operational pagination Multiple records Change page Show new data page CA0022 Individual actions per user Display action menu Click on dropdown Show available options CA0023 Suspend user Click on “Suspend account” Confirmation (if applicable) User goes to “Suspended” status CA0024 Access to user details Click on “See details” Click Redirection to detailed user profile HU004. View detailed user profile and location history ID History HU004 Name View detailed user profile and location history Weight 3 HISTORY ROLE System administrator Function View a user's full profile, including their location history. Result Admin views all user information and can track current and historical location from the dashboard. Normal Flow From the general list of users, the administrator selects “See details” Select “Location History” The view opens user profile , where the following data is displayed: User photo Name and status (Active / Suspended) User ID Membership Telephone Email Creation date User type On the right side is the submodule Location history with two sections: Current location : Address, reporting time, current speed, mobility status (Ex: On route). Viewing in interactive map with marked point. Location history: Table with location records (coordinates), date, time and speed. Search field by date (DD/MM/YYYY). Button Search r to filter results. Validations and Expected Behaviors If there are no results in the history → “No locations found for the selected date”. The map should update the current location in real time or at predefined intervals (if applicable). The coordinates must match the history points. System Notifications ✔ History uploaded correctly. ❌ Error when searching → “No records found for the date entered.” ❌ Map connection error → “The map could not be loaded.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0025 Viewing user profile Click on “See details” Redirection Show personal information and status CA0026 Current location display User has active location Load screen Show point on map with current data CA0027 Viewing full history User with registered history Profile access Show table with ordered records CA0028 History search by date Valid date entered Click Search Filter and show matching results CA0029 Functional map Loading the map with current coordinates Profile access Correct geographic display HU005. Viewing shields to which the user belongs ID History HU005 Name Viewing shields to which the user belongs Weight 2 HISTORY ROLE System administrator Function Consult the list of shields in which the user has active participation. Result The administrator accesses the user's profile and views the shields they are linked to, along with their role, hierarchy, and shield details. Normal Flow From the general list of users, the administrator selects “See details” The administrator accesses the tab "Shields" . A list is displayed with the following information per shield: Shield logo and identification Shield name Shield ID Shield Administrator (name and ID) Creation date Type of user participation (Ex: Standard, Admin) User hierarchy in the shield (Ex: Collaborative) Membership Action: “See details” link for each shield Redirects to the details of the selected shield The list can have multiple pages with bottom pagination. When you click “See details” , you access the specific profile of the shield (flow covered in the Shields module). Validations and Expected Behaviors If the user is not linked to any shield → Show message: “This user does not currently belong to any shield.” The hierarchy field should reflect the type of functional role within the shield. System Notifications No notification is required, it is an informational view. Actions are only generated in case of navigation or data loading error. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0030 Viewing the list of shields User with linked shields Tab access Show complete table with shields CA0031 Role and hierarchy display User has assigned role Screen loading Show user type and corresponding hierarchy CA0032 Shield Manager Display Shield has admin defined Screen loading Show administrator name and ID CA0033 Navigation to shield details Click on “See details” Click Redirection to selected shield profile CA0034 List pagination More than X shields per user Page turn Show new records without reloading full profile HU006. Viewing the history of alerts and SOS events associated with the user ID History HU006 Name Viewing the history of alerts and SOS events associated with the user Weight 3 HISTORY ROLE System administrator Function Consult the SOS alerts and events generated by a user, with the possibility of reviewing location, details and evidence. Result The administrator accesses the list of user-generated events and can review their content and attached evidence. Normal Flow From the general list of users, the administrator selects “See details” The administrator accesses the tab “Alerts and SOS” . A list is displayed with the following information: Event type (Ej: SOS, Alert - Police). Unique alert/SOS code . Geographic location (coordinates). Issue date and time . Access to multimedia evidence (image/video). Related shield . If evidence is available, a link is displayed in the format “Evidence#ID” . Clicking on the evidence link opens a emerging modal which shows: Multimedia (photo/video of the accident). Geographic location on map. Description Alert type, date and time. Additional information (username, phone, ID). Button Download Evidence and button Cancel . Validations and Expected Behaviors If there is no evidence → Show “–”. If a multimedia loading error occurs → Display the message “The evidence could not be loaded.” Evidence must be available in standard formats (JPG, MP4, PDF, etc.). System Notifications ✔ Modal loaded correctly with evidence. ❌ Download error → “The file could not be downloaded, try later.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0035 Viewing alert history User has registered events Tab access Show full table CA0036 Access to available evidence Event with attached evidence Click on evidence link Open modal with details CA0037 Full display of the modal Evidence uploaded successfully Modal load Show map, data and multimedia CA0038 Download evidence Available evidence Click on the “Download Evidence” button Start downloading the file CA0039 Handling errors in evidence Damaged or unavailable file Click on evidence link Show corresponding error message HU007. Viewing user membership history ID History HU007 Name Viewing user membership history Weight 2 HISTORY ROLE System administrator Function View the memberships the user has had, including dates, type, payments and associated transactions. Result The administrator accesses the user's profile and can review the complete history of their memberships. Normal Flow From the general list of users, the administrator selects “See details” The administrator accesses the tab “Membership” . A table is displayed with the user's membership history, where each row includes: Membership type (Ex: Level 0, Level 1, Professional, Business, etc.). Membership start date. Membership end date. Payment code (Ex: Payment#123123). Transaction ID (Ex: T-1231231231). Link: “See details” for each record. Selecting it redirects us to membership details The listing can have multiple records depending on the user's history. By clicking on “View details”, you access the expanded membership details (if applicable, it may include data on the associated payment method or receipt). Validations and Expected Behaviors If the user has no membership history → Show message: “This user has not purchased memberships so far.” The field Pay It must be linked to the corresponding record in the payment module. System Notifications ✔ History uploaded successfully. ❌ Display error → “Unable to load membership history.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0040 Viewing Membership History User with previous registrations Tab access Show full list CA0041 Viewing dates and types Records available Screen loading Show dates and levels correctly CA0042 Viewing associated payments Registration has payment and transaction Screen loading Show Payment and Transaction fields CA0043 Access to payment details Click on “See details” Click Redirection to detailed view (if applicable) CA0044 Error handling due to data not loaded System or connection failure Failed access Corresponding error message HU008. Viewing the user's biometric history and downloading reports ID History HU008 Name Viewing the user's biometric history and downloading reports Weight 3 HISTORY ROLE System administrator Function Consult the user's biometric records (entries and exits) with the option to view photographic evidence and download reports. Result The administrator accesses the user's biometric history, can view associated photos and generate reports by date. Normal Flow From the general list of users, the administrator selects “See details” The administrator accesses the tab “Biometric” . A table is displayed with the user's biometric history, where each row includes: Record ID Name, user avatar and User ID Time Date Registered location (address + coordinates) Record type : Entry/Exit Evidence (photo) → Link: “Far photo” Additional features: Search by date (DD/MM/YYYY) to filter records. Button Download report which opens a modal with: Fields: Date range and member name Format options: PDF / XLSX Download button. When you click "Far photo" , a modal opens with: Registration photo. Name, user ID and registration code. Validations and Expected Behaviors If there are no records on the searched date → Message: “No records were found for this date.” Photographic evidence must match registration data. Download must generate readable file and ordered by date. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0045 Viewing biometric history User has records Tab access Show full table CA0046 Filter by date Valid date entered Click Search Show corresponding records CA0047 Record type display Record has type assigned Table loading Show Entry/Exit with visual badge CA0048 Viewing associated photo Click on “See photo” Click Show modal with image and data CA0049 Download report by date Fill in fields and select format Click Download Generate PDF/XLSX file with filtered records CA0050 Error handling Error generating file or uploading image failed action Show corresponding error message HU009. Viewing and managing the list of registered companies ID History HU009 Name Viewing and managing the list of registered companies Weight 3 HISTORY ROLE System administrator Function Consult the complete list of companies registered in the system, apply filters and perform actions on each record. Result The administrator can view all the key information of each company, perform specific searches and access individual management options. Normal Flow The administrator accesses the module Companies from the side menu of the panel. A table is displayed with the following fields per row: Logo and company name Associated Shield ID Company administrator user (name + ID ) Number of associated shields Number of associated users Creation date Current status (Active, Suspended) Action (See details) See details Redirect to company details Suspend account Included at the top are: free search engine (by company name, shield ID or admin). Filters button (allows you to filter by status, creation date, etc.). Pagination at the bottom of the listing. Each row allows you to manage the company from the actions dropdown: See details Suspend company (Other possible actions according to extended design) Validations and Expected Behaviors If there are no search results or filters → “ No companies found for the selected criteria ”. The status of each company should reflect its current operational condition in the system. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0051 Viewing the list of companies Registered companies Access to the module Show complete table with all data CA0052 Effective search by name or admin Text entry Click on magnifying glass or enter Filter results dynamically CA0053 Filter application Selected filters Apply Show only matches with filters CA0054 Status display Companies with different statuses Table loading Show color/badge by status CA0055 Navigation between records More than X companies listed Bottom pagination Show new rows when browsing CA0056 Access to shares by company Click on the “Action” button Display options Execute options such as “View details” or “Suspend” CA0057 Company suspension Click on the “Suspend” option Confirmation (if applicable) Company goes to Suspended status Company goes to Suspended status HU010. Viewing shields associated with a company ID History HU010 Name Viewing shields associated with a company Weight 2 HISTORY ROLE System administrator Function Consult the shields linked to a company, along with its relevant details. Result The administrator accesses the company profile and can view all the shields associated with their respective data and redirect to the details of each shield if required. Normal Flow From the general list of Companies, the administrator selects “See details” The administrator accesses the tab “Shields” . The company profile is displayed with: Name and logo State company ID Number of shields Assigned commercial advisor Advisor ID and telephone Creation date User type: Corporate The default submodule shows the tab “Shields” , with a structured list where each row includes: Shield name Shield ID Number of members of the shield Creation date Membership Type Shield Administrator (Name and ID) Link “See details” to consult complete information about the shield. Redirects to the details of the selected shield Pagination navigation in case of multiple records. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0058 Viewing the list of shields Company with assigned shields Submodule access Show table with complete data CA0059 Viewing members by shield Shield has associated users Table loading Show corresponding number CA0060 Shield Manager Display Shield has admin defined Screen loading Show name and ID CA0061 Redirection to shield profile Click on “See details” Click Access the details of the corresponding shield CA0062 Pagination navigation More shields than the limit per page Page turn Show new records correctly HU011. Viewing members associated with a company ID History HU011 Name Viewing members associated with a company Weight 2 HISTORY ROLE System administrator Function Consult the members belonging to a company, with membership details, date and number of associated shields. Result The administrator accesses the company profile and views the complete list of members registered in said entity. Normal Flow From the general list of Companies, the administrator selects “See details” The administrator accesses the tab “Members” . A table is displayed with the following fields per row: Member Photo Member's full name User ID Profile creation date Number of shields in which it participates Current Membership Level The listing allows page navigation if the number of members exceeds the visible limit. Validations and Expected Behaviors If there are no associated members → Show message: “This company has no currently registered members.” The total number of members must match the information added in the Companies module (Number of users column). System Notifications ✔ Data loaded correctly. ❌ Error loading the list → “The company members could not be loaded.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0063 Viewing the list of members Company with registered users Submodule access Show table with data per row CA0064 Membership level display User has assigned membership Table loading Show corresponding level CA0065 Display of the number of shields User is in several shields Table loading Show precise quantity CA0066 Paginated listing navigation Number of members exceeds limit Page turn Show new records without reloading full profile CA0067 Management of companies without members Company without registered users Tab access Show empty message correctly HU012. Viewing promotion codes associated with a company ID History HU012 Name Viewing promotion codes associated with a company Weight 2 HISTORY ROLE System administrator Function View promotion codes linked to a company, along with their settings and status. Result The administrator accesses the company profile and views the list of active promotional codes, their stock and associated benefits. Normal Flow From the general list of Companies, the administrator selects “See details” The administrator accesses the tab “Promotion codes” . A table is displayed with the following fields per row: ID of the code Promotion code (string visible to users) Applicable membership level % discount Total code stock (number of available uses) Label/Internal Code Description Code Status (Active / Inactive) Optionally, from the actions column (if applicable), future functions such as edit code or deactivate could be included. Validations and Expected Behaviors If the company does not have codes assigned → Show: “This company does not have active promotional codes.” The total stock should reflect the actual availability of the code. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0068 Viewing the list of promo codes Company with registered codes Tab access Show table with all attributes CA0069 Discount % display Code has discount assigned Table loading Show value correctly with “%” symbol CA0070 Viewing available stock Code has stock assigned Table loading Show correct number CA0071 Viewing code status Code can be active or inactive Table loading Show correct visual badge CA0072 Managing companies without codes Company does not have promotions Tab access Show empty list message HU013. Viewing the history of memberships purchased by a company ID History HU013 Name Viewing the history of memberships purchased by a company Weight 2 HISTORY ROLE System administrator Function Consult the memberships acquired by a company with their history of dates, payments and transactions. Result The administrator accesses the details of the company and can view the different levels of membership it has had, with dates, payments and linked transactions. Normal Flow From the general list of Companies, the administrator selects “See details” The administrator accesses the tab “Memberships” . A table is displayed with the history of acquired memberships, where each row includes: Membership level (0, 1, etc.) Start date End date Payment code (payment ID) transaction ID Link “See details” for extended payment information (if applicable) Validations and Expected Behaviors If the company has never acquired memberships → Message: “ This company does not register purchased memberships ”. The system should list levels chronologically by start date. System Notifications ✔ Membership history successfully uploaded. ❌ Loading error → “ Could not display membership history ”. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0073 Viewing Membership History Company with previous memberships Tab access Show table ordered chronologically CA0074 Viewing Effective Dates Membership has a beginning and an end Table loading Show both fields correctly CA0075 Payment and transaction display Payment registration available Table loading Show payment and transaction ID CA0076 Viewing payment details Click on “See details” Click Show extended payment information CA0077 Business management without membership Company without purchases Tab access Show empty list message HU014. Viewing and managing the list of shields ID History HU014 Name Viewing and managing the list of shields Weight 3 HISTORY ROLE System administrator Function View all shields created on the platform, with their key details and search/filter options. Result The administrator views the general list of available shields, can filter them, sort them and access actions for each individual shield. Normal Flow The administrator accesses the module “Shields” from the side menu. The system loads a list in table format with the following information per shield: Shield name Shield ID Shield Administrator (name and ID) Shield type (e.g. Corporate, Family, etc.) Number of associate members Creation date Status (Active/Inactive) Action Dropdown menu: See details Redirects to shield details Suspend shield The table includes functions of: General text search Advanced filter (if applicable) Pagination to browse the complete list The administrator can interact with the “Action” menu in each row to perform tasks such as: view details, suspend (depending on later configuration). Validations and Expected Behaviors If there are no shields → Show message: “No shields have been registered so far.” If there are no search results → Show: “No shields were found with the criteria entered.” System Notifications ✔ Shields loaded correctly. ❌ Loading error → “It was not possible to display the shields at this time.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0078 General view of the list Shields registered on platform Access to the module Table loaded successfully CA0079 Type and status display Shield has type and state defined Data upload Show in table in corresponding column CA0080 Use of the search engine Entered text matches shield Look for Successfully filtered results CA0081 Using advanced filters Active filters by category Apply filter Table shows consistent results CA0082 Paginated navigation Shields exceed limit per page Change page Show new records CA0083 Actions per shield Click on Action menu See options Display context menu correctly HU015. Viewing points of interest on a shield and its visit history ID History HU015 Name Viewing points of interest on a shield and its visit history Weight 3 HISTORY ROLE System administrator Function View all shields created on the platform, with their key details and search/filter options. Result The administrator views the general list of available shields, can filter them, sort them and access actions for each individual shield. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Points of Interest” . The system displays: The family profile: Name and logo State ID Administrator N ° of members Creation date User type: Corporate And interactive map with numbered markers representing points of interest. A lateral list of points of interest with its corresponding address. When selecting a point in the list or on the map: It updates the “Point History” , where it is displayed Member who passed the point Date Time You can also use the search filter by date. Validations and Expected Behaviors If there are no registered points → Show message: “This shield has no defined points of interest.” If there is no history for a point → Show: “There are no visits recorded for this point.” System Notifications ✔ Points of interest loaded correctly. ✔ History updated when selecting a point. ❌ Error → “Could not load visit history.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0084 Viewing points on map Shield with registered points Submodule load Show geographic markers CA0085 Viewing the list of points Points with assigned address Submodule load Show side list CA0086 Viewing visit history Point with associated records Point selection Table with member, date and time CA0087 Filter by history date Specific date entry Look for Table filters by date correctly CA0088 Management without data Shield without points or visits Submodule loading Show empty messages HU016. Viewing members of a shield ID History HU016 Name Viewing members of a shield Weight 5 HISTORY ROLE System administrator Function Consult the list of members assigned to a specific shield, view associated details and access the history of individual locations. Result The administrator will be able to see all the members belonging to the selected shield, along with their hierarchy, type, creation date and direct access to their location history. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Members” . A list is displayed with: profile photo Full name Member ID Creation date Member Type (Standard) Hierarchy within the shield (Ex: Collaborative) Direct access to “Location History” When you click on “Location History”, a modal window is displayed with: Time, location (textual address), GPS coordinates Filter by date search button Validations and Errors: If there are no records in the location history, the message should be displayed: “There are no locations available for the selected range” . Process Notifications: ✅ Modal open correctly. ⚠ Error messages if there are no results. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0089 View member list The administrator accesses the shield and selects the “Members” tab See list The table is correctly displayed with members, hierarchy, type, creation date and history access CA0090 Access to location history Admin clicks on a member's “Location History” Open modal The pop-up window is displayed with a table of times, locations and coordinates CA0091 Filter by date in history A valid date is entered in the search filter Look for Only locations corresponding to the date are listed CA0092 Validation of empty fields Date filter is empty Click search Search should be prevented and an error message displayed CA0093 Modal without data There are no locations on the filtered date Look for “There are no available locations for the selected range” message is displayed HU017. Viewing a shield's route history ID History HU017 Name Viewing a shield's route history Weight 3 HISTORY ROLE System administrator Function Consult the history of routes associated with members of a specific shield Result The administrator will be able to view the routes taken by the members of the shield with detailed information, access their geolocation, consult the individual details of each route and download reports in PDF or Excel format. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Route history” . View the list of members of the shield with Name Hierarchy Option to select a member. Route ID Speed Route details Search routes by date (DD/MM/YYYY field) The administrator selects Download Routes . Search routes by date (DD/MM/YYYY field) Download massive report in PDF or Excel by selecting member and date range. When you click "See Details" , a modal window is displayed with: Map of the route Start and end information (time and date) Point of origin and destination (Home/Office) Maximum speed reached Minimum device battery level Detailed table by time and location (coordinates). Notifications: When you click "Download Route" the "Download History" , show toast of: Success: “The report has been downloaded correctly.” Error: “Error generating report. Please try again.” Validations and error handling: If a member is not selected: “You must select a member to view routes” message. If the date field is empty when searching: the entire history is displayed without a filter. If the date format is invalid: “Incorrect date format, use DD/MM/YYYY.” Additional technical specifications: Modal should allow vertical scrolling if the route detail is extensive. The generated file must contain the same structure as the displayed detail. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0094 View routes associated with a shield User accesses the “Route History” tab Select a member The routes of the selected member are listed CA0095 Check details of a route User clicks on “View Details” Modal opens with detail Map, route, speed and battery of the route are displayed CA0096 Search routes by date User enters a valid date Click on “Search” The list is filtered by that date CA0097 Download individual route details User inside the modal Click on “Download Route” PDF file is generated with details visible CA0098 Download route report User chooses member and dates Click on “Download History” PDF/XLSX file is generated according to selection, with all routes CA0099 Validation of empty or incorrectly entered fields Empty member field or invalid date Click on corresponding action Error message is displayed depending on the affected field HU018. Viewing Alerts and SOS from the shield ID History HU018 Name Viewing Alerts and SOS from the shield Weight 3 HISTORY ROLE System administrator Function View and consult the logs of alerts and SOS requests issued by shield members. Result The administrator must be able to access a detailed list of SOS alerts and requests with information such as alert type, location, time, associated multimedia evidence, and data on the issuing member. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Alerts and SOS . View a table with columns: Type (Ej.: Police Alert or SOS). Location (coordinates). Schedule (exact date and time). Evidence (link to see the multimedia evidence). Member (name and surname of the issuer). When you click on the “Evidence” link, a pop-up modal opens: Image or video is displayed. A map with the location of the event is included. Complementary data: type of alert, date, time, name of issuer, telephone number. If it is type Alert , will have a description field Option for download the evidence . You can search by date through a top filter field. Validations and Error Handling Validate that evidence exists before opening the modal. Validate empty fields in records. Network error: user notification. Technical Specifications Evidence supports image (.JPG, .PNG) or video (.MP4) format. Evidence modal must be responsive. Link “Download Evidence” must activate direct download of the file. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0100 Viewing logs There are alert/SOS logs Access the “Alerts and SOS” tab The records are displayed in a detailed table CA0101 Filter by date Enter valid date range Use search field Table is updated with filter results CA0102 View evidence Registry has multimedia evidence Click on “Evidence#” Modal opens with multimedia, map and data CA0103 Download evidence Evidence is available Click on the “Download Evidence” button File downloads successfully CA0104 Error handling No evidence available Click on “Evidence#” Appropriate error message displayed HU019. Viewing Shield Membership History ID History HU019 Name Viewing Shield Membership History Weight 2 HISTORY ROLE System administrator Function Consult the history of memberships contracted for a specific shield. Result The administrator must be able to view the membership levels that the shield has had, along with its validity period, payment details and associated transaction. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Membership” . A table is displayed with the following columns: Membership (Level 0, Level 1, etc.). Start (membership start date). END (end date). Pay (Related payment ID, example: Payment#123123). Transaction (Bank transaction ID or internal system). See details (optional, to consult more payment information if available). Redirect to membership details The user can access detailed payment information from the “View details” link if they need to validate additional information. Process Notifications ✅ Success: The table correctly loads the membership history. ❌ Error: “Unable to load membership history. Please try again.” Validations and Error Handling Validate that all fields have information before displaying in the table. Error checking if the “View details” link has no information available. If there is no history available, display a message: “This shield does not have registered memberships.” ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0100 Viewing history The shield has previous memberships Access the “Membership” tab Table with detailed information is displayed correctly CA0101 Date validation Membership has registered start and end Show in table Dates are displayed correctly in your column CA0102 View payment ID There is a relationship with the payment system Show payment field “Payment#…” is displayed in the corresponding column CA0103 View transaction Membership is associated with a bank transaction Show transaction field “T-…” is displayed in the corresponding column CA0104 See details Additional data is available Click on “See details” Access additional payment information HU020 Viewing and searching shield chat history ID History HU020 Name Viewing and searching shield chat history Weight 3 HISTORY ROLE System administrator Function Consult the history of conversations between the members of a shield and use the search by date. Result The user must be able to view chat messages, including text, audio and alerts, as well as use the search engine to find messages by date. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Chat” . The message history is displayed in chronological order, including: Text messages. Audio messages with play button. Messages generated by the system (e.g. alerts). In the right side panel, you will find the search engine by date. The user can select a date on the calendar and click “Search”. The system filters the messages of the selected day and displays them. Process Notifications ✅ Success: “Messages filtered correctly.” ❌ Error: “No messages found on the selected date.” Validations and Error Handling Validate date format before running search. If there are no messages for the selected date, show an informative message. Handle error if media content cannot be played. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0105 Viewing history There are registered messages Access the “Chat” tab The list of messages is displayed in chronological order CA0106 Audio viewing There are voice messages recorded Play icon displayed User can hear them correctly CA0107 Viewing alerts The system generates an alert in the chat Show featured message The message is displayed in highlighted color CA0108 Search by date User selects a date Click on “Search” Messages are filtered by selected date CA0109 No results There are no messages for the searched date Search by date The message “No messages recorded for this date” is displayed HU021 Viewing and managing biometric records of Shield members ID History HU021 Name Viewing and managing biometric records of Shield members Weight 3 HISTORY ROLE System administrator Function Consult the biometric entry and exit records of the members of a Shield. Result The administrator can view detailed biometric history, with access to photographic evidence, search filters and report generation. Normal Flow From the general list of Shields, the administrator selects “See details” The administrator accesses the tab “Biometrics” . A table is displayed with the available biometric records with the following columns: Event ID Member Name and ID Time Location (Direction + Coordinates) Record Type (Entry/Exit) Attached file (Facial recognition photo) The photograph is shown The user can: Search by date or ID . See details of each photograph by clicking on "View photo". Filter by event type or specific members . Download biometric report (PDF or XLSX format) by choosing members and date ranges. The system generates the downloadable file with the selected information. Process Notifications: ✅ “Report generated successfully” ❌ “No biometric records were found in the indicated range” ⚠️ “You must select at least one member to download report” Validation and Error Handling Cases: If no members are selected when generating the report → an alert is displayed. If the date range has no records → empty message. If there is a failure to load the registration photo → display the message “File not available”. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0110 View logs Shield has biometric records Enter the “Biometric” tab Complete and updated table with data CA0111 Far photo Registration has file attached Click on “See photo” Show modal with member image CA0112 Search by date Valid date entered Apply filter Show only corresponding records CA0113 Generate report Selecting at least one member and date range Click on “Download report” .PDF or .XLSX file downloaded successfully CA0114 Input/Output Type Filter applied Select type in interface Show records corresponding to the chosen type HU022 Promotional code management ID History HU022 Name Promotional code management Weight 5 HISTORY ROLE System administrator Function Create, edit, activate, suspend or delete a promotional code. Result The system must allow you to register promotional codes with configurable attributes, and manage their subsequent status from the list view. Normal Flow The administrator accesses the "Promo Codes" module from the side menu. View the list of existing promotional codes. You can filter or search for specific codes by free text. Click the “New” button to register a new code. The promotion creation form opens with the following fields: Promo code (Mandatory text) Label (Mandatory text) Membership Level (Radio: Level 1, Level 2 or Level 3 - required) Duration : Start and end date (Mandatory date) Stock total (Mandatory number) % discount (Mandatory number) The user completes the fields and clicks “Create coupon”. The system displays a success modal “Coupon created successfully”. The new code is displayed in the general list with status “Active”. The administrator can apply actions from the list: See details Redirect to promotion details Activate The system displays a modal “Do you want to activate this coupon?” Continue The system displays a confirmation modal “Coupon activated”. Suspend The system displays a “Do you want to suspend this coupon?” modal. Continue The system displays a confirmation modal “Coupon suspended”. Edit The promotion editing form opens with the fields completed, with the option to edit them Eliminate The system displays a “Do you want to delete this coupon?” modal. Continue The system displays a confirmation modal “Coupon deleted”. Actions immediately affect the state or properties of the code depending on the option selected. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0115 Create a new promo code All valid fields Click on “Create coupon” Code registered and displayed as "Active" CA0116 Validate required fields empty field Click on “Create coupon” Show error message CA0117 Activate a suspended code Code with status "Suspended" Click "Activate" in the action menu Code goes to "Active" status CA0118 Suspend an active code Code with status "Active" Click "Suspend" in the action menu Code goes to "Suspended" status CA0119 Delete promotional code Existing code Click "Delete" Code removed from list CA0120 View code status and details Previously created code Click on “See details” View full code information HU023 Viewing details of promotional codes and stock consumption ID History HU023 Name Viewing details of promotional codes and stock consumption Weight 3 HISTORY ROLE System administrator Function Consult the complete information of a promotional code and its consumption history per user. Result The system must show the details of the code attributes and the history of stock consumed by users linked to Escudos. Normal Flow The administrator accesses the module o “C “Promo codes”. From the general list, select a promotional code and click on the “See details” option. The system redirects to the view of Promotion details . All information for the selected code is displayed code ID Promo code Label Creation date Duration (Start - End) % discount Membership Level Stock total Stock disponible actual The administrator can click “Edit” to modify some data (depending on permissions). In the same view, the system shows the User Stock History , including: User Date of use Associated shield User type (e.g. Collaborative) You can search history by specific date using a filter field. Process notifications: Immediate visual change in history when applying filters. The “Edit” button activates the configurable fields. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0121 View full details of the promo code Previously created code Click on “See details” The tab is displayed with complete attributes CA0122 Consult consumption history by user Code with previous use of stock View history table List of users who used the code CA0123 Filter history by date Valid date field Table is updated with filtered records Table is updated with filtered records CA0124 Show updated available stock Code with partial use Tab display “Available Stock” field with correct value HU024 Management of alerts and evidence of the Alerts and SOS module ID History HU024 Name Management of alerts and evidence of the Alerts and SOS module Weight 5 HISTORY ROLE System administrator Function View, manage and update the status of alerts and SOS generated by users. Result The administrator must be able to review active alerts, consult details, associated evidence, modify the status, leave comments, view modification history and service rating. Normal Flow The administrator accesses the “Alerts and SOS” module. View a list with columns: Alert ID, User, Location, Schedule, Status, Evidence, Comments, Modified History, Rating. Each alert is presented with its type (Police, SOS, etc.), user name and GPS location. You can view the current status of the alert (Pending, Help sent, Resolved). By clicking on the status, it can be modified by pop-up confirmation. Earring Help sent Resolved The administrator can click on “Evidence” to display images or videos along with the georeferenced location. You can leave comments associated with the alert. View the modification history, with data of the user who modified it, time, alert ID... View the rating and opinion registered by the user about the care received. You can filter or search by date and alert type. Validation Cases and Errors Validation if an empty comment already exists. Validation if a state is not selected when modifying. Error if evidence is not available ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0125 Viewing alert list The user enters the module Show table Table with visible columns and loaded alert data CA0126 Viewing evidence Click on the “Evidence” button Open modal Image or video + GPS location CA0127 Alert status change Click on status and confirmation in modal Confirm change Updated status and success feedback CA0128 Add comment Enter text and press “Create comment” Save comment Comment saved with name and date/time CA0129 Viewing modification history Click on the “View history” button Show modal Details of changes with user and time CA0130 Service rating display Click on star icon Show rating modal Comment and stars displayed correctly CA0131 Validation of empty fields when commenting Comment field is empty Press "Create comment" “You must enter a comment” alert CA0132 Validation of errors when changing state Connection failure Press "Modify status" System error message HU025 Payments and Memberships ID History HU025 Name Viewing and managing membership payments Weight 5 HISTORY ROLE System administrator Function View the history of payments made by users for memberships. Result The administrator must be able to access a table with details of each payment, including order, date, transaction ID, membership type, amount, user ID, payment status and option to view the full detail. Normal Flow The administrator accesses the “Membership Payments” module. View a list with the following columns: ID Orders Date Transaction ID Membership (Level 1, Level 2, Level 3) Amount ($) User ID Payment status (Ex: Made) Action (See details) You can use filters or the search engine to locate specific transactions. Clicking on "View details" opens a modal or new view with expanded information for the selected payment. Process Notifications Success: “Payment loaded correctly” Error: “Payment details could not be loaded” Validation Cases and Errors Validation if there are no results for applied filters. Incorrect date format validation. Connection error when trying to open details. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0133 Payment list display The user enters the module Show table Visible payments with their attributes correctly loaded CA0134 Search by order or user ID A value is entered in the search engine Press Enter Filter applied, matching results shown CA0135 Filter by date, membership or status A filter is selected Apply filters Updated table with filtered criteria CA0136 Viewing payment details Click on “See details” Open modal/screen Complete payment information visible CA0137 Validation by empty results There are no matches Apply filter “No results found” message CA0138 Validation due to incorrect date format misspelled date Apply filter “Invalid date format” message CA0139 Error loading payment details Fallo a backend Click on “See details” Show message “Error obtaining information” HU026 Membership Details ID History HU026 Name Membership Details Weight 2 HISTORY ROLE System administrator Function View full details of a specific membership payment. Result By selecting “View details” in the payment list, the administrator accesses a complete view with user information, membership purchased, amount breakdown, payment method, and transaction data. Normal Flow The administrator clicks on “View details” from the main table of the Payments and Memberships module. The view is shown with 3 blocks of information: User Information : Photo, name, ID, status (active), phone, email, user type, creation date. Payment Details : Membership, quantity, unit price, subtotal, VAT (%), total. Transaction Detail: Transaction ID, payment method, IP address, collection address, date, collection status. Validation Cases and Errors Validate if linked transaction ID exists. Show error if data cannot be recovered. Validate complete user data upload, transaction and breakdown. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0140 Correct display of user data Access payment details Open view Personal information, contact and status are displayed CA0141 Correct display of payment breakdown Payment has membership and associated values See breakdown Table with quantity, unit, subtotal, VAT and total CA0142 Viewing transaction information Collection data is registered Show fields ID, payment method, IP address and status visible CA0143 Error handling when loading details Failed to connect or incomplete data Show message “Payment information could not be loaded” CA0144 Return navigation User presses “Return” Click on button Redirects to the general list of payments and memberships HU027 Documentation ID History HU027 Name Management and editing of legal content (Terms and conditions / Privacy policy) Weight 2 HISTORY ROLE System administrator Function View, edit and publish legal content (terms and conditions and privacy policy) from the “About” section. Result The administrator can modify the legal texts of the system and publish them so that they are available to users. Normal Flow The administrator enters the Documentation module. The tab is displayed by default About , where two contents appear: Terms and conditions Privacy Policy The administrator selects one of the contents. The corresponding text is displayed in a rich text editor (WYSIWYG) with: Last modified date Editable field for title Editable field for description with formatting options The administrator can: Press Edit to enable the fields Modify content Save changes (unpublished) Publish changes (available to users) A success notification is displayed. Validation Cases and Error Handling Validate that the “Title” field is not empty. Validate that there is content in the description body. Show message if there is an error when saving or publishing. Avoid publication if the fields are incomplete. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0145 Viewing legal texts The module is accessed Show content Viewing titles and description CA0146 Editing functionality Editing is enabled Click on “Edit” Text and title fields are enabled CA0147 Saving updated content Change is made and click “Save” Keep Show message “Changes saved successfully” CA0148 Publication of new legal content After editing content and clicking “Publish” Publish changes Show message “Content published successfully” CA0149 Validation of empty fields Missing title or description Click publish Show error “You must complete the required fields” HU028 Tickets and Frequently Asked Questions ID History HU028 Name Management of Help Tickets and Frequently Asked Questions Weight 5 HISTORY ROLE System administrator Function Manage and update support documentation by creating help tickets and categorized FAQs. Result The administrator can create, edit and publish help tickets and FAQs organized by categories, visible to end users in the support module. Normal Flow The administrator accesses the "Documentation" module and selects the "Tickets" section. View a list of existing tickets with title, content, and editing option. You can select a ticket to edit the content or create a new one by clicking “+ Create Subject” . When creating or editing a ticket, the administrator: Enter a subject title (mandatory text input). Write a detailed description (mandatory rich text type input). Save or publish the changes. In the section Frequently Asked Questions , displays existing categories. Can: Create new categories (mandatory text input). Select a category and add new questions using “+Create Question” . Enter the text of the question and your answer in rich format. Edit or save existing questions. Finally, you can publish all the changes made by clicking “Publish Changes” . Process Notifications: ✅ Success : "Ticket saved/posted successfully", "Question added successfully", "Changes posted". ❌ Error : "All fields are required", "Error saving", "Cannot publish without content". Validations and Error Handling: Validation of empty fields when creating ticket, category or question. Block publish action if no changes are made. Alert display in case of technical error (error mode). ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0150 Create new help ticket The title and description are complete Click on “Create Subject” and “Save” The ticket is created and displayed in the list CA0151 Edit existing ticket A ticket is selected Edit fields and save Ticket updates correctly CA0152 Create FAQ Category Enter category title Click on “+ Create Category.” Category visible in the question list CA0153 Create FAQ within category Enter question and answer Click on “+ Create Question” and save Question added and visible in the selected category CA0154 Validate required fields Empty fields when saving or publishing Try to save or publish Error alert: “Empty required fields” CA0155 Publish changes to documentation Changes made Click on “Publish Changes” Changes reflected in the system and success message HU029 Support ID History HU029 Name Management and attention to support tickets by the administrative team Weight 5 HISTORY ROLE System administrator Function View, respond to, and track user-generated support tickets Result The administrator can effectively manage received tickets, respond to the user, mark the ticket as resolved, and view the user's ticket history. Normal Flow The administrator accesses the Support module from the side menu. A list is displayed with all registered tickets, including their status (Pending / Resolved), date and time. The administrator can search for a specific ticket using a search field. When selecting a ticket, the detail opens in chat format where: Messages between the user and the administrator are displayed. The administrator can type a response in a text field and send it. On the right side it shows: User information (name, ID, status, subject, date, location, IP). History of previously generated tickets. The administrator can mark the ticket as resolved with a highlighted button. Mark resolved Once marked as resolved, the ticket status is updated in the main list. Process Notifications ✅ Message sent successfully. ❌ Error sending message (in case of empty fields or connection error). ✅ Ticket status updated correctly. Validations and Error Handling Blocking the send button if the message is empty. Visual confirmation when a ticket is marked as resolved. Scroll control to display the last chat message automatically. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0156 Viewing ticket list The module is loaded correctly Access the support module List of tickets displayed with statuses CA0157 Sending a response to a ticket Message field has text Click "Send" Message sent and visible in chat thread CA0158 Mark ticket as resolved Selected ticket Click on “Mark resolved” Ticket status changes to “Resolved” CA0159 Viewing ticket history User with associated history View side tab Previous tickets are shown CA0160 Empty message validation Message field is empty Click on “Send” Button lock and validation alert HU030 Administrator Role Management ID History HU030 Name User Roles and Permissions Management Weight 8 HISTORY ROLE System administrator Function Manage system administrator profiles, permissions, accesses and passwords. Result The administrator can create, edit, suspend, delete and assign specific access to other administrators from the Roles module. Normal Flow The administrator accesses the “Roles” module from the side menu. View a table with all the administrators created, their role type, creation date, last activity, and available actions. Can: Check details of an administrator. Edit personal data Name, Last Name, Email, Telephone and Profile Photo Change password. Current Password, New Password and Confirm Password Temporarily suspend or remove users. To create a new administrator, click “+ Create Admin”: Step 1 : Complete personal information: name, last name, email, password, phone number and upload a profile photo (750x750px in .PNG). Step 2 : Assign access permissions by section (Users, Shields, Alerts and SOS, Payment History, Support, Roles) or select full access. Finish the process with the button "Keep" . A confirmation message is displayed: "Administrator created." In each existing manager you can click "Action" to: See details. Edit information. Suspend. Eliminate. In the current administrator profile you can also edit your details or update your password from your personal panel. Process Notifications ✅ “Administrator created successfully” ✅ “Changes saved successfully” ❌ “Error saving data: empty required fields” ❌ “Invalid or mismatched password” ⚠️ “Are you sure you want to delete this administrator?” Validations and Errors Checking empty fields. Correct email validation. Password confirmation validation. Telephone field only allows valid numbers. Do not allow duplicate mail. ACCEPTANCE CRITERIA # Criterion Condition Action Result CA0161 Create new administrator All required fields completed Click on “Create Admin” and “Save” The new administrator is registered and appears in the list CA0162 Assign permissions by sections At least one section marked Keep Administrator-specific access is saved CA0163 Edit administrator data Data edited successfully Save changes Administrator information is updated CA0164 Change administrator password Current valid and new password confirmed Save changes Password updated successfully CA0165 Suspend administrator Action confirmation Click suspend The administrator is temporarily inactive CA0166 Delete administrator Action confirmation Click delete The administrator is removed from the list CA0167 Mandatory fields validation Empty fields detected Save changes Show error message: “Complete the required fields” CA0168 Email validation Invalid email entered Keep Show “Invalid email format” error