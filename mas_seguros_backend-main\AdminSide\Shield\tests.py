from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth.models import User
from Account.models import UserProfile
from Shield.models import ShieldModel, Location
from AdminSide.Shield.serializers import ShieldMemberLocationHistorySerializer
from datetime import date, timedelta


class ShieldMemberLocationHistoryTests(APITestCase):
    """
    Test cases for CA0092 and CA0093 acceptance criteria
    """

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            full_name='Test User',
            phone='+**********'
        )

        # Create test shield
        self.shield = ShieldModel.objects.create(
            shield_name='Test Shield'
        )
        self.shield.members.add(self.user_profile)

        # Create test location
        self.location = Location.objects.create(
            userprofile=self.user_profile,
            shield=self.shield,
            location='Test Location',
            lat='40.7128',
            long='-74.0060'
        )

    def test_ca0092_empty_date_validation(self):
        """
        CA0092: Validation of empty fields - Date filter is empty
        Should prevent search and display error message
        """
        serializer = ShieldMemberLocationHistorySerializer(
            data={'member_id': self.user.id, 'date': ''},
            context={'request': type('MockRequest', (), {
                'query_params': {'date': ''}
            })()}
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn('date', serializer.errors)
        self.assertIn('La fecha es requerida para la búsqueda', str(serializer.errors['date']))

    def test_ca0092_valid_date_validation(self):
        """
        CA0092: Valid date should pass validation
        """
        today = date.today()
        serializer = ShieldMemberLocationHistorySerializer(
            data={'member_id': self.user.id, 'date': today.strftime('%Y-%m-%d')},
            context={'request': type('MockRequest', (), {
                'query_params': {'date': today.strftime('%Y-%m-%d')}
            })()}
        )

        self.assertTrue(serializer.is_valid())

    def test_ca0092_future_date_validation(self):
        """
        CA0092: Future date should be rejected
        """
        future_date = date.today() + timedelta(days=1)
        serializer = ShieldMemberLocationHistorySerializer(
            data={'member_id': self.user.id, 'date': future_date.strftime('%Y-%m-%d')}
        )

        self.assertFalse(serializer.is_valid())
        self.assertIn('date', serializer.errors)
        self.assertIn('La fecha no puede ser futura', str(serializer.errors['date']))

    def test_ca0093_no_data_message(self):
        """
        CA0093: Modal without data - should display appropriate message
        """
        # Test with date filter but no matching data
        past_date = date.today() - timedelta(days=30)
        serializer = ShieldMemberLocationHistorySerializer(
            data={'member_id': self.user.id, 'date': past_date.strftime('%Y-%m-%d')}
        )

        self.assertTrue(serializer.is_valid())

        # The actual message testing would be done in the view/frontend
        # This test ensures the serializer validation works correctly
