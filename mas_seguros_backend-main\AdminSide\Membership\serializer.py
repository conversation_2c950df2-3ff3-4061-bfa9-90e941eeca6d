from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from Membership import models as membership_models
from Account import serializers as account_seri
from datetime import datetime


class membershipSerializer(serializers.ModelSerializer):
    user_id = serializers.SerializerMethodField()
    formatted_date = serializers.SerializerMethodField()
    membership_display = serializers.SerializerMethodField()
    conditions_display = serializers.SerializerMethodField()

    class Meta:
        model = membership_models.MembershipModel
        fields = "__all__"

    def get_user_id(self, obj):
        """Get user ID with proper fallback"""
        if obj.userprofile and obj.userprofile.user:
            return obj.userprofile.user.id
        elif obj.userprofile:
            return obj.userprofile.id
        return None

    def get_formatted_date(self, obj):
        """Format date for display"""
        if obj.date:
            return obj.date.strftime('%d/%m/%Y')
        return None

    def get_membership_display(self, obj):
        """Get membership level display name"""
        membership_map = {
            'Level_1': 'Nivel 1',
            'Level_2': 'Nivel 2',
            'Level_3': 'Nivel 3',
            'Level_4': 'Nivel 4'
        }
        return membership_map.get(obj.membership, obj.membership)

    def get_conditions_display(self, obj):
        """Get conditions display name"""
        conditions_map = {
            'Effected': 'Efectuado',
            'Failed': 'Fallido'
        }
        return conditions_map.get(obj.conditions, obj.conditions)

    def to_representation(self, instance):
        response = super().to_representation(instance)
        if instance.userprofile:
            response['userprofile'] = account_seri.UserProfileSerializer(instance.userprofile).data
        else:
            response['userprofile'] = None

        return response