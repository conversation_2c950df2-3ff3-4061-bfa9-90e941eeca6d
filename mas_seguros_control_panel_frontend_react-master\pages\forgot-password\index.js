import Auth from "@/components/layouts/Auth";
import InputGroup from "@/components/utility/InputGroup";
import { ArrowLeftIcon, ArrowPathIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import useAxios from "@/hooks/useAxios";

const ForgotPassword = () => {
  const router = useRouter();
  const { axios } = useAxios();
  const [processing, setProcessing] = useState(false);
  const { register, handleSubmit, formState: { errors } } = useForm();

  const submit = handleSubmit((data) => {
    setProcessing(true);

    axios
      .post("/adminside/api/admin/admin-password-recovery/", data)
      .then((response) => {
        toast.success(response.data.message || "Enviamos la nueva contraseña a tu correo");

        router.push({
          pathname: "/password-sent",
          query: { email: data.email }
        });
      })
      .catch((error) => {
        console.error("Password recovery error:", error);

        // Handle different error response formats
        if (error?.response?.data?.message) {
          toast.error(error.response.data.message);
        } else if (error?.response?.data?.errors) {
          // Handle field-specific errors
          const fieldErrors = error.response.data.errors;
          Object.keys(fieldErrors).forEach(field => {
            if (Array.isArray(fieldErrors[field])) {
              toast.error(fieldErrors[field][0]);
            } else {
              toast.error(fieldErrors[field]);
            }
          });
        } else {
          // Fallback error message
          toast.error("No se pudo enviar la nueva contraseña. Verifica tu correo electrónico.");
        }
      })
      .finally(() => {
        setProcessing(false);
      });
  });

  return (
    <Auth>
      <form
        onSubmit={submit}
        className="block w-full max-w-md rounded border bg-white px-10 pt-8 pb-40 shadow-md"
      >
        {/* Back Btn */}
        <Link href="/">
          <ArrowLeftIcon className="h-6 w-6" />
        </Link>

        <h1 className="mt-3 text-2xl font-semibold">Recupera tu contraseña</h1>
        <p className="mt-4 text-secondary">
          Ingresa tu correo para enviar tu contraseña.
        </p>

        <div className="mt-11">
          <InputGroup.Label className="!mb-2 !text-base !font-semibold !text-opacity-100">
            Correo
          </InputGroup.Label>
          <InputGroup>
            <InputGroup.Input
              {...register("email", {
                required: "Campo requerido",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Formato de correo electrónico incorrecto"
                }
              })}
              className="!py-2"
              placeholder="<EMAIL>"
            />
          </InputGroup>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        <button
          type="submit"
          disabled={processing}
          className="mt-16 inline-flex w-full items-center justify-center gap-2.5 rounded-xl bg-primary py-5 px-4 text-base text-white ring-primary focus:outline-none focus:ring-2 focus:ring-offset-2"
        >
          Enviar
          {processing ? (
            <ArrowPathIcon className="h-5 w-5 animate-spin" />
          ) : null}
        </button>
      </form>
    </Auth>
  );
};

export default ForgotPassword;


