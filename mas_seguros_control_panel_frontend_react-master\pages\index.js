import Auth from "@/components/layouts/Auth";
import InputGroup from "@/components/utility/InputGroup";
import useAxios from "@/hooks/useAxios";
import { setLoggedIn, setToken, update } from "@/redux/userSlice";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useDispatch } from "react-redux";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";

const Login = () => {
  const router = useRouter();
  const { axios } = useAxios();
  const dispatch = useDispatch();
  const { register, handleSubmit, formState: { errors } } = useForm({
    mode: "onBlur"
  });

  const [processing, setProcessing] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const submit = handleSubmit((data) => {
    setProcessing(true);
    axios
      .post("/adminside/api/admin/adminlogin/", data)
      .then((response) => {
        console.log("Login response:", response.data);
        const data = response.data.data;

        // Log the entire response to see the structure
        console.log("Full login response data:", data);

        // Extract user data safely with fallbacks
        const userData = {
          first_name: data?.user_profile?.user?.first_name || "",
          last_name: data?.user_profile?.user?.last_name || "",
          email: data?.user_profile?.user?.email || data.email_address || "",
          phone: data?.user_profile?.phone || "",
          image: data?.user_profile?.image_url || "",
          // The user ID is in user_profile.user.id as shown in the API response
          id: data?.user_profile?.user?.id || "",
          role: data?.user_profile?.role || "Admin",
          // Add additional fields from the API response
          full_name: data?.user_profile?.full_name || "",
          identification_card: data?.user_profile?.identification_card || "",
          birth_date: data?.user_profile?.birth_date || "",
          // Add admin permissions
          admin_permissions: data?.user_profile?.admin_permissions || {
            users_access: false,
            shields_access: false,
            alerts_sos_access: false,
            payment_history_access: false,
            support_access: false,
            roles_access: false,
            full_access: false,
          },
        };

        // If we don't have a first name but have an email, use the email username
        if (!userData.first_name && userData.email) {
          userData.first_name = userData.email.split('@')[0];
        }

        console.log("Final user data being stored in Redux:", userData);

        console.log("Extracted user data:", userData);

        dispatch(setToken(data.token));
        dispatch(update(userData));
        dispatch(setLoggedIn(true));
        toast.success("¡Bienvenido al Panel Administrativo!");
        router.push("/dashboard");
      })
      .catch((error) => {
        console.error("Login error:", error);

        // Handle different error response formats
        if (error?.response?.data?.message) {
          // Handle string message from backend
          toast.error(error.response.data.message);
        } else if (error?.response?.data?.errors) {
          // Handle field-specific errors
          const fieldErrors = error.response.data.errors;
          Object.keys(fieldErrors).forEach(field => {
            if (Array.isArray(fieldErrors[field])) {
              toast.error(fieldErrors[field][0]);
            } else {
              toast.error(fieldErrors[field]);
            }
          });
        } else {
          // Fallback error message
          toast.error("Correo electrónico o contraseña incorrectos");
        }
      })
      .then(() => {
        setProcessing(false);
      });
  });

  return (
    <Auth>
      <form
        onSubmit={submit}
        className="block w-full max-w-md rounded border bg-white px-10 py-12 shadow-md"
      >
        <div>
          <img
            src="/assets/img/logo-black-text.svg"
            className="mx-auto block w-full max-w-[230px]"
            alt="Company Logo"
          />
        </div>
        <h1 className="mt-8 text-2xl font-semibold">
          Bienvenido administrador
        </h1>
        <p className="mt-4 text-secondary">Ingresa los datos para continuar.</p>

        <div className="mt-5 space-y-6">
          <div>
            <InputGroup.Label className="!mb-2 !text-base !font-semibold !text-opacity-100">
              Correo
            </InputGroup.Label>
            <InputGroup>
              <InputGroup.Input
                {...register("email_address", {
                  required: "Campo requerido",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Formato de correo electrónico incorrecto"
                  }
                })}
                className="!py-2"
                placeholder="<EMAIL>"
              />
            </InputGroup>
            {errors.email_address && (
              <p className="mt-1 text-sm text-red-600">{errors.email_address.message}</p>
            )}
          </div>
          <div>
            <InputGroup.Label className="!mb-2 !text-base !font-semibold !text-opacity-100">
              Contraseña
            </InputGroup.Label>
            <InputGroup className="relative">
              <InputGroup.Input
                {...register("password", {
                  required: "Campo requerido",
                  minLength: {
                    value: 6,
                    message: "La contraseña debe tener al menos 6 caracteres"
                  }
                })}
                type={showPassword ? "text" : "password"}
                className="!py-2 pr-10"
                placeholder="*********"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center pr-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                ) : (
                  <EyeIcon className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </InputGroup>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>
        </div>

        <button
          disabled={!!processing}
          type="submit"
          className="mt-16 inline-flex w-full items-center justify-center gap-2.5 whitespace-nowrap rounded-xl bg-primary py-5 px-4 text-base text-white ring-primary focus:outline-none focus:ring-2 focus:ring-offset-2"
        >
          Iniciar Sesión
          {processing ? (
            <ArrowPathIcon className="h-5 w-5 animate-spin" />
          ) : null}
        </button>

        <div className="mt-9 flex justify-center">
          <Link
            href="/forgot-password"
            className="mx-auto inline-block font-semibold underline hover:text-primary"
          >
            Olvide mi contraseña
          </Link>
        </div>
      </form>
    </Auth>
  );
};

export default Login;

