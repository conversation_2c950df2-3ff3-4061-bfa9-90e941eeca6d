import React from 'react';

const GoogleStaticMap = ({ 
  lat, 
  lng, 
  zoom = 15, 
  width = 400, 
  height = 300, 
  className = "",
  mapType = 'roadmap',
  markers = [],
  style = {}
}) => {
  if (!lat || !lng) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 text-gray-500 ${className}`} style={{ width, height, ...style }}>
        <div className="text-center">
          <div className="text-4xl mb-2">📍</div>
          <p className="text-sm">No hay datos de ubicación disponibles</p>
        </div>
      </div>
    );
  }

  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 text-gray-500 ${className}`} style={{ width, height, ...style }}>
        <div className="text-center">
          <div className="text-4xl mb-2">⚠️</div>
          <p className="text-sm">Google Maps API key not configured</p>
        </div>
      </div>
    );
  }

  // Build the Google Static Maps URL
  const baseUrl = 'https://maps.googleapis.com/maps/api/staticmap';
  const params = new URLSearchParams({
    center: `${lat},${lng}`,
    zoom: zoom.toString(),
    size: `${width}x${height}`,
    maptype: mapType,
    key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
  });

  // Add main marker
  params.append('markers', `color:red|label:A|${lat},${lng}`);

  // Add additional markers if provided
  if (markers && markers.length > 0) {
    markers.forEach((marker, index) => {
      if (marker.lat && marker.lng) {
        const markerColor = marker.color || 'blue';
        const markerLabel = marker.label || String.fromCharCode(66 + index); // B, C, D, etc.
        params.append('markers', `color:${markerColor}|label:${markerLabel}|${marker.lat},${marker.lng}`);
      }
    });
  }

  const mapUrl = `${baseUrl}?${params.toString()}`;

  return (
    <div className={className} style={{ width, height, ...style }}>
      <img
        src={mapUrl}
        alt="Mapa de ubicación"
        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        onError={(e) => {
          e.target.style.display = 'none';
          e.target.nextSibling.style.display = 'flex';
        }}
      />
      <div 
        className="flex items-center justify-center bg-gray-100 text-gray-500"
        style={{ width: '100%', height: '100%', display: 'none' }}
      >
        <div className="text-center">
          <div className="text-4xl mb-2">⚠️</div>
          <p className="text-sm">Error al cargar el mapa</p>
        </div>
      </div>
    </div>
  );
};

export default GoogleStaticMap;
