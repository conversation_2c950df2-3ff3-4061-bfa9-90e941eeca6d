import calendar
from datetime import datetime

from dateutil.relativedelta import relativedelta
from django.db.models import Sum
from Account import models as account_models


def get_months_total_data(all_data):
    month_list = ["ENE", "FEB", "MAR", "ABR", "MAY", "J<PERSON>", "JUL", "AGO", "SEP", "OCT", "NOV", "DIC"]
    users_count = []
    month_count_list = []

    # Get current date
    today = datetime.today()

    # Start from 11 months ago and go forward to current month (12 months total)
    for i in range(12):
        # Calculate the month and year we're looking at
        target_month = (today.month - 11 + i) % 12
        if target_month == 0:
            target_month = 12

        target_year = today.year
        if today.month - 11 + i < 1:
            target_year -= 1

        # Get count for this month/year
        count = all_data.filter(created_at__year=target_year,
                               created_at__month=target_month).count()

        print(f"Month: {target_month}, Year: {target_year}, Count: {count}")

        users_count.append(count)
        month_count_list.append("{}".format(month_list[target_month - 1]))

    return users_count, month_count_list


def get_months_to_show():
    month_today = datetime.today().month
    months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October',
              'November', 'December']
    months_to_show = []
    month_index = month_today
    for i in range(13):
        try:
            months_to_show.append(months[month_index - 1])
            month_index += 1
        except:
            month_index = 0
            months_to_show.append(months[month_index])
            month_index += 1
    return months_to_show


def get_data_per_month(all_data):
    # months_to_show = get_months_to_show()
    months_total_users = get_months_total_data(all_data)
    return months_total_users
