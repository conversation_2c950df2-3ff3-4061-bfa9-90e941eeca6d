from django.shortcuts import render
from rest_framework.permissions import IsAuthenticated

# Create your views here.

from Account.models import UserProfile

from django.views.decorators.csrf import csrf_exempt

from rest_framework.views import APIView
from Ticket import models as ticket_model
from . import serializers as ticket_serializers
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from rest_framework.parsers import JSONParser
from django.http import HttpResponse, JsonResponse
from rest_framework.generics import get_object_or_404

from rest_framework.permissions import AllowAny


# Create your views here.


class Ticket(APIView):
    # permission_classes = (IsAuthenticated,)
    def get(self, request):
        if 'user_id' in request.query_params:
            data = ticket_model.Ticket.objects.filter(user=request.query_params.get('user_id')).prefetch_related(
                'title').order_by('-id')
        else:
            data = ticket_model.Ticket.objects.prefetch_related('title').order_by('-id')
        final_response = list()
        for ticket_obj in data:
            ticket = dict()
            ticket["id"] = ticket_obj.id
            ticket["description"] = ticket_obj.description
            ticket["title"] = ticket_obj.title.title
            ticket["ticket_num"] = ticket_obj.ticket_num
            ticket["resolved"] = ticket_obj.resolved
            ticket["created_at"] = str(ticket_obj.created_at)
            ticket["updated_at"] = str(ticket_obj.updated_at)
            user_info = dict()
            user_info["id"] = ticket_obj.user.id
            user_info["full_name"] = ticket_obj.user.full_name
            user_info["lat"] = ticket_obj.user.lat
            user_info["long"] = ticket_obj.user.long
            ticket["user"] = user_info
            final_response.append(ticket)

        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=final_response,
                                           msg='all tickets'), status.HTTP_200_OK)

    def post(self, request):
        subject_serializer = ticket_serializers.TicketSubjectCompleteSerializer(data=request.data)
        response = subject_serializer.is_valid(raise_exception=False)
        user = UserProfile.objects.filter(id=request.data.get('user_id'))
        if user:
            if response:
                subject_obj = subject_serializer.save()
                subject_obj.save()

                new_data = request.data.copy()
                new_data["title"] = subject_obj.id
                new_data["user"] = user[0].id

                ticket_serializer = ticket_serializers.TicketSerializer(data=new_data)
                response = ticket_serializer.is_valid(raise_exception=False)
                if response:
                    ticket_obj = ticket_serializer.save()
                    ticket_obj.save()
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data=ticket_serializer.data,
                                                       msg='Ticket succesfully created'))
                else:
                    print(ticket_serializer.error_messages)
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg=list(ticket_serializer.errors.items())[0][1]),
                        status.HTTP_400_BAD_REQUEST)

            else:
                print(subject_serializer.error_messages)
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg=list(subject_serializer.errors.items())[0][1]),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(subject_serializer.errors.items())[0][1]),
                status.HTTP_400_BAD_REQUEST)


class TicketSubject(APIView):
    # permission_classes = (IsAuthenticated,)
    def get(self, request):
        data = ticket_model.TicketSubject.objects.all()
        serializer = ticket_serializers.TicketSubjectCompleteSerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='ticket subject'), status.HTTP_200_OK)

    def put(self, request, pk=None, format=None):
        post = get_object_or_404(ticket_model.TicketSubject.objects.all(), pk=pk)
        serializer = ticket_serializers.TicketSubjectCompleteSerializer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = ticket_serializers.TicketSubjectCompleteSerializer(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        subject_obj = serializer.save()
        subject_obj.save()
        serializer = ticket_serializers.TicketSubjectCompleteSerializer(subject_obj)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='ticket subject succuesfully created'))


class ticket_id(APIView):
    def get(self, request, id=None):
        if id:
            try:
                # First try to get a ticket
                ticket_obj = ticket_model.Ticket.objects.get(id=id)

                # Get messages for this ticket
                messages = ticket_model.TicketMessage.objects.filter(ticket=ticket_obj).order_by('created_at')
                message_serializer = ticket_serializers.TicketMessageSerializer(messages, many=True)

                ticket = {
                    "id": ticket_obj.id,
                    "description": ticket_obj.description,
                    "title": ticket_obj.title.title,
                    "ticket_num": ticket_obj.ticket_num,
                    "resolved": ticket_obj.resolved,
                    "created_at": str(ticket_obj.created_at),
                    "updated_at": str(ticket_obj.updated_at),
                    "messages": message_serializer.data
                }

                # Add user info if available
                if ticket_obj.user:
                    user_info = {
                        "id": ticket_obj.user.id,
                        "full_name": ticket_obj.user.full_name,
                        "lat": ticket_obj.user.lat,
                        "long": ticket_obj.user.long
                    }
                    ticket["user"] = user_info

                return Response({"status": "success", "data": ticket}, status=status.HTTP_200_OK)
            except ticket_model.Ticket.DoesNotExist:
                # If ticket not found, try to get a ticket subject
                try:
                    item = ticket_model.TicketSubject.objects.get(id=id)
                    serializer = ticket_serializers.TicketSubjectCompleteSerializer(item)
                    return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)
                except ticket_model.TicketSubject.DoesNotExist:
                    return Response(
                        {"status": "error", "message": "No ticket or ticket subject found with this ID"},
                        status=status.HTTP_404_NOT_FOUND
                    )
        return Response(
            {"status": "error", "message": "ID parameter is required"},
            status=status.HTTP_400_BAD_REQUEST
        )


class GetSpecificUserTicket(APIView):
    permission_classes = (AllowAny,)

    # Get specific user Tickets
    def get(self, request):
        if 'user_id' in request.query_params:
            try:
                # First try to get tickets by user ID directly
                data = ticket_model.Ticket.objects.filter(
                    user_id=request.query_params.get('user_id')).prefetch_related(
                    'title').order_by('-id')

                # If no results, try with user__user_id
                if not data.exists():
                    data = ticket_model.Ticket.objects.filter(
                        user__user_id=request.query_params.get('user_id')).prefetch_related(
                        'title').order_by('-id')
            except Exception as e:
                print(f"Error fetching tickets: {e}")
                data = ticket_model.Ticket.objects.none()
        else:
            data = ticket_model.Ticket.objects.prefetch_related('title').order_by('-id')

        final_response = list()
        if data.exists():
            for ticket_obj in data:
                ticket = dict()
                ticket["id"] = ticket_obj.id
                ticket["ticket_num"] = ticket_obj.ticket_num
                ticket["description"] = ticket_obj.description
                ticket["title"] = ticket_obj.title.title
                ticket["resolved"] = ticket_obj.resolved
                ticket["created_at"] = str(ticket_obj.created_at)
                ticket["updated_at"] = str(ticket_obj.updated_at)

                # Handle user info carefully to avoid errors
                user_info = dict()
                try:
                    if hasattr(ticket_obj.user, 'user') and ticket_obj.user.user:
                        user_info["id"] = ticket_obj.user.user.id
                        user_info["full_name"] = ticket_obj.user.user.get_full_name()
                    else:
                        user_info["id"] = ticket_obj.user.id
                        user_info["full_name"] = ticket_obj.user.full_name

                    user_info["lat"] = ticket_obj.user.lat
                    user_info["long"] = ticket_obj.user.long
                except Exception as e:
                    print(f"Error getting user info: {e}")
                    user_info["id"] = ticket_obj.user.id if ticket_obj.user else None
                    user_info["full_name"] = "Unknown User"

                ticket["user"] = user_info
                final_response.append(ticket)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=final_response,
                                               msg='all tickets'), status.HTTP_200_OK)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=[],
                                           msg='no tickets found'), status.HTTP_200_OK)


class ResolveTicket(APIView):
    permission_classes = (AllowAny,)

    def post(self, request):
        serializer = ticket_serializers.TicketResolvedSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        ticket = ticket_model.Ticket.objects.filter(id=request.data.get('id')).last()
        if ticket:
            ticket.resolved = True
            ticket.save()

            # Clean up old messages in Firestore
            try:
                import firebase_admin
                from firebase_admin import firestore
                from datetime import datetime, timedelta

                # Get a reference to the Firestore database
                db = firestore.client()

                # Create a reference to the messages collection for this ticket
                messages_collection = db.collection('ticket_messages').document(str(ticket.id)).collection('messages')

                # Get messages older than 7 days
                cutoff_date = datetime.now() - timedelta(days=7)

                # Query for old messages
                old_messages = messages_collection.where('created_at', '<', cutoff_date).limit(100).get()

                # Delete old messages in batches
                batch = db.batch()
                deleted_count = 0

                for doc in old_messages:
                    batch.delete(doc.reference)
                    deleted_count += 1

                # Commit the batch delete if there are messages to delete
                if deleted_count > 0:
                    batch.commit()
                    print(f"Deleted {deleted_count} old messages for ticket {ticket.id}")

            except Exception as e:
                print(f"Error cleaning up old messages in Firestore: {e}")

            # Return the updated ticket data
            ticket_data = {
                "id": ticket.id,
                "ticket_num": ticket.ticket_num,
                "description": ticket.description,
                "title": ticket.title.title if ticket.title else "",
                "resolved": ticket.resolved,
                "created_at": str(ticket.created_at),
                "updated_at": str(ticket.updated_at)
            }

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=ticket_data,
                                               msg='ticket marked as resolved successfully'), status.HTTP_200_OK)
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='No Ticket Found'), status.HTTP_400_BAD_REQUEST)


class TicketMessages(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        ticket_id = request.query_params.get('ticket_id')
        if not ticket_id:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='Ticket ID is required'), status.HTTP_400_BAD_REQUEST)

        try:
            ticket = ticket_model.Ticket.objects.get(id=ticket_id)
            messages = ticket_model.TicketMessage.objects.filter(ticket=ticket).order_by('created_at')
            serializer = ticket_serializers.TicketMessageSerializer(messages, many=True)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='ticket messages retrieved successfully'), status.HTTP_200_OK)
        except ticket_model.Ticket.DoesNotExist:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                               msg='Ticket not found'), status.HTTP_404_NOT_FOUND)

    def post(self, request):
        serializer = ticket_serializers.TicketMessageCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=serializer.errors), status.HTTP_400_BAD_REQUEST)

        ticket_id = serializer.validated_data.get('ticket_id')
        message_text = serializer.validated_data.get('message')
        is_admin = serializer.validated_data.get('is_admin', True)
        sender_id = serializer.validated_data.get('sender_id')

        try:
            ticket = ticket_model.Ticket.objects.get(id=ticket_id)

            # Get sender (admin or user)
            sender = None
            if sender_id:
                try:
                    sender = UserProfile.objects.get(id=sender_id)
                except UserProfile.DoesNotExist:
                    pass

            # Create message
            message = ticket_model.TicketMessage.objects.create(
                ticket=ticket,
                sender=sender,
                message=message_text,
                is_admin=is_admin
            )

            # Serialize and return the created message
            response_serializer = ticket_serializers.TicketMessageSerializer(message)

            # If FCM is enabled, send notification to the user
            try:
                from Account import fcm as fcm_file
                from Account.models import FcmDeviceRegistration

                # Only send notification if the message is from admin to user
                if is_admin and ticket.user:
                    device = FcmDeviceRegistration.objects.filter(userprofile=ticket.user).last()
                    if device and device.device_id:
                        # Use the specialized ticket message notification function
                        fcm_file.send_ticket_message_notification(
                            registration_id=device.device_id,
                            ticket_id=ticket.id,
                            message_text=message_text,
                            sender_name="Administrador"
                        )
            except Exception as e:
                print(f"Error sending FCM notification: {e}")

            # Write message to Firebase Firestore for real-time updates
            try:
                import firebase_admin
                from firebase_admin import firestore

                # Get a reference to the Firestore database
                db = firestore.client()

                # Create a reference to the messages collection for this ticket
                messages_collection = db.collection('ticket_messages').document(str(ticket_id)).collection('messages')

                # Set the message data
                try:
                    # Prepare message data
                    message_data = {
                        'id': message.id,
                        'ticket': ticket_id,
                        'message': message_text,
                        'is_admin': is_admin,
                        'sender_name': 'Administrador' if is_admin else (sender.full_name if sender else 'Usuario'),
                        'created_at': firestore.SERVER_TIMESTAMP
                    }

                    # Add the message to Firestore
                    messages_collection.add(message_data)

                except Exception as e:
                    print(f"Error formatting message data for Firestore: {e}")
                    # Fallback with simpler data
                    message_data = {
                        'id': message.id,
                        'ticket': ticket_id,
                        'message': message_text,
                        'is_admin': is_admin,
                        'sender_name': 'Administrador' if is_admin else 'Usuario',
                        'created_at': firestore.SERVER_TIMESTAMP
                    }
                    messages_collection.add(message_data)

                print(f"Message added to Firestore for ticket {ticket_id}")
            except Exception as e:
                print(f"Error writing to Firestore: {e}")

            return Response(
                backend_utils.success_response(status_code=status.HTTP_201_CREATED,
                                              data=response_serializer.data,
                                              msg='Message sent successfully'), status.HTTP_201_CREATED)

        except ticket_model.Ticket.DoesNotExist:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                              msg='Ticket not found'), status.HTTP_404_NOT_FOUND)
