import React, { useState, useRef } from "react";
import Admin from "@/components/layouts/Admin";
import { DocumentTextIcon, DocumentArrowDownIcon } from "@heroicons/react/24/outline";
import SectionHeading from "@/components/SectionHeading";
import TopCardsSection from "@/components/home/<USER>";
import UserCountLineChart from "@/components/home/<USER>/UserCountLineChart";
import UserCountBarChart from "@/components/home/<USER>/UserCountBarChart";
import MonthSelector from "@/components/dashboard/MonthSelector";
import { toast } from "react-hot-toast";
import { useSelector } from "react-redux";
import useAxios from "@/hooks/useAxios";

const Home = () => {
  const [selectedMonth, setSelectedMonth] = useState({
    month: "enero",
    value: 1,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isPdfLoading, setIsPdfLoading] = useState(false);
  const topCardsSectionRef = useRef(null);
  const user = useSelector((state) => state.user);
  const { axios } = useAxios();

  const handlePdfDownload = async () => {
    setIsPdfLoading(true);
    try {
      const response = await axios.get(`/api/dashboard/download-pdf-file-for-dashboard/?month=${selectedMonth.value}&year=${new Date().getFullYear()}`, {
        responseType: 'blob'
      });

      // Check if the response is an error message (not a PDF)
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/json')) {
        // Convert blob to text to read the error message
        const text = await response.data.text();
        const errorData = JSON.parse(text);
        toast.error(errorData.msg || "¡No hay datos en el período de tiempo seleccionado!");
        setIsPdfLoading(false);
        return;
      }

      // Create a URL for the blob
      const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;

      // Get month name for filename
      const monthNames = {
        1: "Enero", 2: "Febrero", 3: "Marzo", 4: "Abril", 5: "Mayo", 6: "Junio",
        7: "Julio", 8: "Agosto", 9: "Septiembre", 10: "Octubre", 11: "Noviembre", 12: "Diciembre"
      };

      // Generate filename with current date
      const now = new Date();
      link.setAttribute('download', `Dashboard_${monthNames[selectedMonth.value]}_${now.getFullYear()}.pdf`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      toast.success("PDF descargado correctamente");
    } catch (error) {
      console.error("Error downloading PDF:", error);
      if (error.response && error.response.status === 404) {
        toast.error("¡No hay datos en el período de tiempo seleccionado!");
      } else {
        toast.error("Error al descargar el PDF");
      }
    } finally {
      setIsPdfLoading(false);
    }
  };

  const handleExcelDownload = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(`/api/dashboard/download-excel-file-for-dashboard/?month=${selectedMonth.value}&year=${new Date().getFullYear()}`, {
        responseType: 'blob'
      });

      // Check if the response is an error message (not an Excel file)
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/json')) {
        // Convert blob to text to read the error message
        const text = await response.data.text();
        const errorData = JSON.parse(text);
        toast.error(errorData.msg || "¡No hay datos en el período de tiempo seleccionado!");
        setIsLoading(false);
        return;
      }

      // Create a URL for the blob
      const url = window.URL.createObjectURL(
        new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
      );

      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = url;

      // Get month name for filename
      const monthNames = {
        1: "Enero", 2: "Febrero", 3: "Marzo", 4: "Abril", 5: "Mayo", 6: "Junio",
        7: "Julio", 8: "Agosto", 9: "Septiembre", 10: "Octubre", 11: "Noviembre", 12: "Diciembre"
      };

      // Generate filename with current date
      const now = new Date();
      link.setAttribute('download', `Dashboard_${monthNames[selectedMonth.value]}_${now.getFullYear()}.xlsx`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      toast.success("Excel descargado correctamente");
    } catch (error) {
      console.error("Error downloading Excel:", error);
      if (error.response && error.response.status === 404) {
        toast.error("¡No hay datos en el período de tiempo seleccionado!");
      } else {
        toast.error("Error al descargar el Excel");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Admin pageTitle="Dashboard" headerTitle={`Bienvenido, ${user?.first_name || 'Usuario'}`}>
      {/* Topbar */}
      <div className="bg-neutral">
        <div className="container-padding items-center justify-end gap-3 space-y-2 py-2.5 lg:flex lg:space-y-0">
          <p className="text-sm text-secondary-3">
            Visualizar información del mes
          </p>
          <div className="flex max-w-sm items-center justify-end gap-3">
            <MonthSelector
              selectedMonth={selectedMonth}
              setSelectedMonth={setSelectedMonth}
            />

            <button
              type="button"
              onClick={handlePdfDownload}
              disabled={isPdfLoading}
              className="inline-flex flex-grow items-center gap-2 rounded-md border border-transparent bg-primary px-2 py-2 text-sm font-normal leading-4 text-white focus:outline-none disabled:opacity-70 disabled:cursor-not-allowed sm:w-auto sm:min-w-[150px] sm:px-4 mr-2"
            >
              <DocumentArrowDownIcon className="h-5 w-5" />
              {isPdfLoading ? "Descargando..." : "Descargar PDF"}
            </button>

            <button
              type="button"
              onClick={handleExcelDownload}
              disabled={isLoading}
              className="inline-flex flex-grow items-center gap-2 rounded-md border border-transparent bg-success px-2 py-2 text-sm font-normal leading-4 text-white focus:outline-none disabled:opacity-70 disabled:cursor-not-allowed sm:w-auto sm:min-w-[150px] sm:px-4"
            >
              <DocumentTextIcon className="h-5 w-5" />
              {isLoading ? "Descargando..." : "Descargar Excel"}
            </button>
          </div>
        </div>
      </div>

      {/* Main */}
      <div className="space-y-2">
        <TopCardsSection ref={topCardsSectionRef} selectedMonth={selectedMonth} />

        {/* Graphcs Section */}
        <div className="container-padding">
          <SectionHeading className="py-5">
            Métricas de crecimiento
          </SectionHeading>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2">
            {/* Left Graph */}
            <div className="flex flex-col gap-2 bg-white p-4">
              <div className="text-sm font-semibold">
                <h3>N°. de Usuarios registrados por mes</h3>
                <UserCountLineChart />
              </div>
            </div>

            {/* Right Graph */}
            <div className="flex flex-col gap-2 bg-white p-4">
              <div className="text-sm font-semibold">
                <h3>N°. de Escudos creados por mes</h3>
                <UserCountBarChart />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Admin>
  );
};

export default Home;
