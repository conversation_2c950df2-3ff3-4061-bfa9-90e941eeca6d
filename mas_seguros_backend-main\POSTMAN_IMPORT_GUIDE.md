# 🚀 Mas Seguros Backend API - Postman Import Guide

## 📦 What's Included

Your complete Postman collection package includes:

1. **`Mas_Seguros_Backend_API.postman_collection.json`** - Main API collection (51 endpoints)
2. **`Mas_Seguros_Environments.postman_environment.json`** - Environment variables
3. **`POSTMAN_COLLECTION_README.md`** - Detailed documentation
4. **`POSTMAN_IMPORT_GUIDE.md`** - This import guide
5. **`validate_postman_collection.py`** - Validation script

## 🔧 Quick Import Steps

### Step 1: Open Postman
- Launch Postman application
- Make sure you're logged in to your Postman account

### Step 2: Import Collection
1. Click the **"Import"** button (top left)
2. Drag and drop or browse to select:
   - `Mas_Seguros_Backend_API.postman_collection.json`
3. Click **"Import"**

### Step 3: Import Environment
1. Click the **"Import"** button again
2. Select: `Mas_Seguros_Environments.postman_environment.json`
3. Click **"Import"**

### Step 4: Set Active Environment
1. Click the environment dropdown (top right)
2. Select **"Mas Seguros Environments"**

## ✅ Verification

After import, you should see:

### Collection Structure:
```
📁 Mas Seguros Backend API
├── 🔐 Authentication (6 endpoints)
├── 👤 Profile Management (4 endpoints)
├── 📍 Location Management (3 endpoints)
├── 🚨 Alert Management (6 endpoints)
├── 🛡️ Shield Management (6 endpoints)
├── 🆘 SOS Emergency (4 endpoints)
├── 💳 Membership & Payments (2 endpoints)
├── 🎫 Support Tickets (3 endpoints)
├── ❓ FAQ (2 endpoints)
├── 📄 About & Legal (2 endpoints)
├── 📱 Device & Notifications (3 endpoints)
└── 👨‍💼 Admin Dashboard (10 endpoints)
```

### Environment Variables:
- `base_url` = `http://************:8000`
- `auth_token` = (empty - will be set after login)
- `user_id` = (empty - will be set after login)
- Additional helper variables for testing

## 🎯 First Test Run

### 1. Test Authentication Flow:
```
Authentication → Login User
```
- Use valid credentials
- Check that `auth_token` is automatically saved

### 2. Test Authenticated Endpoint:
```
Profile Management → Edit Profile
```
- Should use the saved token automatically

### 3. Test Different Environments:
- Switch `base_url` to `http://127.0.0.1:8000` for local testing
- Switch to `https://masseguros.limacreativa.com` for staging

## 🔄 Environment Switching

### For Local Development:
```json
{
  "base_url": "http://127.0.0.1:8000"
}
```

### For Production:
```json
{
  "base_url": "http://************:8000"
}
```

### For Staging:
```json
{
  "base_url": "https://masseguros.limacreativa.com"
}
```

## 🛠️ Customization Tips

### Adding New Endpoints:
1. Right-click on appropriate folder
2. Select "Add Request"
3. Configure method, URL, headers, body
4. Use `{{base_url}}` and `{{auth_token}}` variables

### Modifying Environment:
1. Click the eye icon next to environment name
2. Click "Edit"
3. Add/modify variables as needed

### Creating Test Scripts:
```javascript
// Example: Save response data to environment
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set('user_id', response.data.id);
}
```

## 🔍 Troubleshooting

### Common Issues:

**❌ "Could not get any response"**
- Check if the server is running
- Verify the `base_url` in environment
- Check network connectivity

**❌ "401 Unauthorized"**
- Run the login endpoint first
- Check if `auth_token` is set in environment
- Verify token hasn't expired

**❌ "404 Not Found"**
- Check the endpoint URL
- Verify the API version
- Ensure the endpoint exists on the server

### Debug Steps:
1. Check Postman Console (View → Show Postman Console)
2. Verify environment variables are set
3. Test with a simple GET endpoint first
4. Check server logs if available

## 📞 Support

- **API Documentation**: See `POSTMAN_COLLECTION_README.md`
- **Backend Issues**: Check Django backend logs
- **Collection Issues**: Run `python validate_postman_collection.py`

## 🎉 You're Ready!

Your Mas Seguros Backend API collection is now ready for testing. Start with the authentication endpoints and explore the comprehensive API functionality.

Happy testing! 🚀
