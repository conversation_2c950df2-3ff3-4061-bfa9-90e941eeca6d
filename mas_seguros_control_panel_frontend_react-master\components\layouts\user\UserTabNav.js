import InputGroup from "@/components/utility/InputGroup";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { Fragment } from "react";
import { Menu, Transition } from "@headlessui/react";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const UserTabNav = ({ userId }) => {
  const router = useRouter();
  const user_id = userId || router.query.user_id;

  const tabs = [
    {
      title: "Historial de ubicaciones",
      href: `/users/${user_id}`,
      activePaths: ["/users/[user_id]"],
    },
    {
      title: "Escudos",
      href: `/users/${user_id}/shields`,
      activePaths: ["/users/[user_id]/shields"],
    },
    {
      title: "Alertas y SOS",
      href: `/users/${user_id}/sos`,
      activePaths: ["/users/[user_id]/sos"],
    },
    {
      title: "Membresía",
      href: `/users/${user_id}/membership`,
      activePaths: ["/users/[user_id]/membership"],
    },
    {
      title: "Biométrico",
      href: `/users/${user_id}/biometric`,
      activePaths: ["/users/[user_id]/biometric"],
    },
  ];

  const handleChange = (e) => {
    router.push(e.target.value);
  };

  return (
    <nav className="mt-6 flex items-center gap-5 2xl:mt-0">
      {/* Mobile Only */}
      <div className="flex-grow xl:hidden">
        <InputGroup>
          <InputGroup.Input as="select" onChange={handleChange}>
            {tabs.map((tab) => (
              <option key={tab.title} value={tab.href}>
                {tab.title}
              </option>
            ))}
          </InputGroup.Input>
        </InputGroup>
      </div>

      {/* Desktop Only */}
      <div className="no-scrollbar hidden overflow-auto xl:block">
        <ul className="flex flex-nowrap gap-9 whitespace-nowrap">
          {tabs.map((tab) => (
            <Item key={tab.title} tab={tab} />
          ))}
        </ul>
      </div>

      <div className="ml-auto text-right">
        <ActionBtn />
      </div>
    </nav>
  );
};

const Item = ({ tab }) => {
  const router = useRouter();
  const isActive = tab.activePaths?.includes(router.pathname);

  return (
    <li key={tab.title}>
      <Link
        href={tab.href}
        className={classNames(
          "block py-1.5 text-lg font-semibold",
          isActive ? "border-b-2 border-b-black text-black" : "text-secondary"
        )}
      >
        {tab.title}
      </Link>
    </li>
  );
};

const ActionBtn = () => {
  const router = useRouter();
  const { axios } = useAxios();
  const user_id = router.query.user_id;
  const [isLoading, setIsLoading] = React.useState(false);
  // Get user suspend status from UserLayout context if possible
  const [userSuspend, setUserSuspend] = React.useState(false);
  React.useEffect(() => {
    // Try to get suspend status from DOM (UserCard renders it)
    const badge = document.querySelector('.bg-red-100.text-red-600');
    setUserSuspend(!!badge);
  }, []);

  const handleSuspendUser = async () => {
    try {
      setIsLoading(true);
      if (!user_id) {
        toast.error('ID de usuario no encontrado');
        return;
      }
      const response = await axios.post('/adminside/api/dashboard/suspend_users/', {
        id: user_id,
        suspended: "true"
      });
      if (response.data.success) {
        toast.success(response.data.message || 'Estado del usuario actualizado');
        window.location.reload();
      } else {
        toast.error(response.data.message || 'Error al actualizar el estado del usuario');
      }
    } catch (error) {
      console.error('Error suspending user:', error);
      toast.error(error.response?.data?.message || 'Error al actualizar el estado del usuario');
    } finally {
      setIsLoading(false);
    }
  };

  const handleActivateUser = async () => {
    try {
      setIsLoading(true);
      if (!user_id) {
        toast.error('ID de usuario no encontrado');
        return;
      }
      const response = await axios.post('/adminside/api/dashboard/suspend_users/', {
        id: user_id,
        suspended: "false"
      });
      if (response.data.success) {
        toast.success(response.data.message || 'Estado del usuario actualizado');
        window.location.reload();
      } else {
        toast.error(response.data.message || 'Error al actualizar el estado del usuario');
      }
    } catch (error) {
      console.error('Error activating user:', error);
      toast.error(error.response?.data?.message || 'Error al actualizar el estado del usuario');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Menu as="div" className="relative inline-block text-left">
      {({ open }) => (
        <>
          <Menu.Button
            className={classNames(
              "inline-flex w-full items-center justify-between gap-2 rounded-md border border-transparent px-2 py-2 text-sm font-normal capitalize leading-4 focus:outline-none sm:px-4",
              open ? "bg-primary text-white" : "text-black"
            )}
          >
            Acción
            <ChevronDownIcon
              className={classNames(
                "-mr-1 ml-2 h-5 w-5 transition-transform duration-300",
                open ? "rotate-180" : ""
              )}
              aria-hidden="true"
            />
          </Menu.Button>

          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="absolute left-0 mt-2 w-56 origin-top-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none lg:left-auto lg:right-0 lg:origin-top-right">
              <div className="py-1">
                <Menu.Item>
                  {({ active }) => (
                    <a
                      href={`/users/${user_id}`}
                      className={classNames(
                        active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                        "block px-4 py-2 text-sm capitalize"
                      )}
                    >
                      Ver detalles
                    </a>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={userSuspend ? handleActivateUser : handleSuspendUser}
                      disabled={isLoading}
                      className={classNames(
                        active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                        "block w-full text-left px-4 py-2 text-sm capitalize",
                        isLoading && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      {isLoading
                        ? "Procesando..."
                        : userSuspend
                        ? "Activar cuenta"
                        : "Suspender cuenta"}
                    </button>
                  )}
                </Menu.Item>
              </div>
            </Menu.Items>
          </Transition>
        </>
      )}
    </Menu>
  );
};

export default UserTabNav;
