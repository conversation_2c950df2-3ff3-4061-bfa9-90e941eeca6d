from django.contrib import admin
from .models import *


# Register your models here.

# admin.site.register(admin_user)
# admin.site.register(companies)
# admin.site.register(routes)
# admin.site.register(Promo_code)
# admin.site.register(Alert)
# admin.site.register(sheild)

# @admin.register(admin_user)
# class admin_userAdmin(admin.ModelAdmin):
#     list_display = ['full_name','No_of_idefication','country','telephone']

# @admin.register(routes)
# class routesAdmin(admin.ModelAdmin):
#     list_display = ['route_id','sheild','date','start_speed','end_speed']

# @admin.register(companies)
# class companies(admin.ModelAdmin):
#     list_display = ['companie_name','companie_id','Date_of_creation','state']

@admin.register(PromoCode)
class promo_codeAdmin(admin.ModelAdmin):
    list_display = ['promo_code', 'start_duration', 'end_duration', 'stocks', 'discount', 'state']

# @admin.site.register(sheild)
# class sheildAdmin(admin.ModelAdmin):
#     list_display= ['condition','sheild_name','administrator_name']
