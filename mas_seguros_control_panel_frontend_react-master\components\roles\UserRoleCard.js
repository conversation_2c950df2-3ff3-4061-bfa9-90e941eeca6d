import React, { Fragment, useState, useEffect } from "react";
import SectionHeading from "../SectionHeading";
import { PencilIcon } from "@heroicons/react/24/solid";
import AdminEditModalBtn from "./AdminEditModalBtn";
import PasswordFormModalBtn from "./PasswordFormModalBtn";
import useAxios from "@/hooks/useAxios";
import { useSelector } from "react-redux";

const UserRoleCard = () => {
  const [currentAdmin, setCurrentAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    shields: null, // No default values - only show when API returns data
    members: null
  });
  const [statisticsLoaded, setStatisticsLoaded] = useState(false);
  const { axios } = useAxios();

  // Get current user data from Redux store
  const user = useSelector((state) => state.user);
  const isLoggedIn = useSelector((state) => state.user.logged_in);

  useEffect(() => {
    fetchCurrentAdmin();
  }, [user, isLoggedIn]);

  // Fetch statistics when admin data is available
  useEffect(() => {
    if (currentAdmin?.id) {
      fetchStatistics();
    }
  }, [currentAdmin]);

  const fetchCurrentAdmin = async () => {
    try {
      setLoading(true);

      // Check if user is logged in and has data
      if (!isLoggedIn || !user.id) {
        console.log('User not logged in or no user ID available');
        setCurrentAdmin(null);
        return;
      }

      // Try to fetch current admin profile using the logged-in user's ID
      const response = await axios.get('/adminside/api/roles/userprofile/', {
        params: { id: user.id }
      });

      if (response.data.success) {
        setCurrentAdmin(response.data.data);
      } else {
        // If API call fails, use Redux data as fallback
        setCurrentAdmin({
          id: user.id,
          full_name: user.full_name || `${user.first_name} ${user.last_name}`.trim() || "Administrador",
          user: {
            email: user.email || "<EMAIL>",
            first_name: user.first_name || "",
            last_name: user.last_name || ""
          },
          phone: user.phone || "+57 1234567890",
          identification_card: user.identification_card || "123456789",
          image_url: user.image || null
        });
      }
    } catch (error) {
      console.error('Error fetching current admin:', error);

      // Use Redux data as fallback when API fails
      if (isLoggedIn && user.id) {
        setCurrentAdmin({
          id: user.id,
          full_name: user.full_name || `${user.first_name} ${user.last_name}`.trim() || "Administrador",
          user: {
            email: user.email || "<EMAIL>",
            first_name: user.first_name || "",
            last_name: user.last_name || ""
          },
          phone: user.phone || "+57 1234567890",
          identification_card: user.identification_card || "123456789",
          image_url: user.image || null
        });
      } else {
        setCurrentAdmin(null);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await axios.get('/adminside/api/roles/admin/statistics/');

      if (response.data.success) {
        const stats = response.data.data;
        setStatistics({
          shields: stats.shields_display || stats.total_shields,
          members: stats.members_display || stats.total_members
        });
        setStatisticsLoaded(true);
      } else {
        // Don't show statistics if API fails
        setStatistics({ shields: null, members: null });
        setStatisticsLoaded(false);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      // Don't show statistics if API fails
      setStatistics({ shields: null, members: null });
      setStatisticsLoaded(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white p-5 pb-14">
        <div className="animate-pulse">
          <div className="flex gap-5">
            <div className="h-28 w-28 bg-gray-200 rounded-full"></div>
            <div className="space-y-2.5 flex-1">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="flex gap-5">
                <div className="h-16 bg-gray-200 rounded w-24"></div>
                <div className="h-16 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isLoggedIn || !currentAdmin) {
    return (
      <div className="bg-white p-5 pb-14">
        <div className="text-center py-8">
          <p className="text-gray-500">Por favor, inicie sesión para ver la información del administrador.</p>
        </div>
      </div>
    );
  }

  // Determine admin role dynamically
  const getAdminRole = () => {
    if (!currentAdmin?.admin_permissions) return "Administrador";
    return currentAdmin.admin_permissions.full_access ? "Superadministrador" : "Administrador";
  };

  const data = [
    {
      key: "Nombre completo",
      value: currentAdmin?.full_name || "N/A",
    },
    {
      key: "N°. de Identificación",
      value: currentAdmin?.identification_card && String(currentAdmin.identification_card).trim() !== '' ? currentAdmin.identification_card : "N/A",
    },
    {
      key: "Correo electrónico",
      value: currentAdmin?.user?.email || "N/A",
    },
    {
      key: "País",
      value: currentAdmin?.country || "Ecuador", // Dynamic country with fallback
    },
    {
      key: "Teléfono",
      value: currentAdmin?.phone || "N/A",
    },
    {
      key: "Fecha de nacimiento",
      value: currentAdmin?.birth_date ? (currentAdmin.birth_date.includes('-') ? currentAdmin.birth_date.split('-').reverse().join('/') : currentAdmin.birth_date) : "N/A",
    },
  ];

  return (
    <div className="bg-white p-5 pb-14">
      <div className="flex flex-col gap-6">
        <div className="flex gap-5">
          <div className="flex items-center justify-center">
            <div className="h-28 w-28 flex-shrink-0">
              <img
                className="block h-28 w-28 rounded-full object-cover"
                src={currentAdmin?.image_url || "/assets/img/default-profile-pic-1.jpg"}
                alt="User"
                onError={(e) => {
                  e.target.src = "/assets/img/default-profile-pic-1.jpg";
                }}
              />
            </div>
          </div>
          <div className="space-y-2.5">
            <h3 className="text-xl font-semibold">{currentAdmin?.full_name || "Administrador"}</h3>
            <span className="inline-flex items-center rounded-full bg-primary bg-opacity-20 px-3 py-1.5 text-sm font-semibold text-primary">
              <svg
                className="mr-1.5 h-2 w-2"
                fill="currentColor"
                viewBox="0 0 8 8"
              >
                <circle cx={5} cy={4} r={3} />
              </svg>
              {getAdminRole()}
            </span>
            {/* Only show statistics when data is available */}
            {statisticsLoaded && (statistics.shields !== null || statistics.members !== null) && (
              <div className="flex gap-5">
                {statistics.shields !== null && (
                  <div className="rounded border px-4 py-2.5">
                    <dd className="text-xs text-secondary">Escudos de Empresa</dd>
                    <dd className="text-xl">{statistics.shields}</dd>
                  </div>
                )}
                {statistics.members !== null && (
                  <div className="rounded border px-4 py-2.5">
                    <dd className="text-xs text-secondary">Miembros creados</dd>
                    <dd className="text-xl">{statistics.members}</dd>
                  </div>
                )}
              </div>
            )}

            {/* Show loading state for statistics */}
            {!statisticsLoaded && currentAdmin && (
              <div className="flex gap-5">
                <div className="rounded border px-4 py-2.5">
                  <dd className="text-xs text-secondary">Escudos de Empresa</dd>
                  <dd className="text-xl">
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-12"></div>
                  </dd>
                </div>
                <div className="rounded border px-4 py-2.5">
                  <dd className="text-xs text-secondary">Miembros creados</dd>
                  <dd className="text-xl">
                    <div className="h-6 bg-gray-200 rounded animate-pulse w-12"></div>
                  </dd>
                </div>
              </div>
            )}

            {/* Show message when statistics are not available */}
            {statisticsLoaded && statistics.shields === null && statistics.members === null && (
              <div className="text-sm text-gray-500 italic">
                Estadísticas no disponibles
              </div>
            )}
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <SectionHeading>Datos de Administrador</SectionHeading>
            <AdminEditModalBtn
              currentAdmin={currentAdmin}
              onUpdate={fetchCurrentAdmin}
              className="inline-flex items-center justify-center gap-2 rounded-full bg-accent px-4 py-2 font-medium"
            >
              <PencilIcon className="h-4 w-4" />
              <span>Editar</span>
            </AdminEditModalBtn>
          </div>
          <hr className="my-2" />

          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            {data.map((item) => (
              <Fragment key={item.key}>
                <dd className="font-semibold">{item.key}</dd>
                <dd>{item.value}</dd>
              </Fragment>
            ))}
          </div>

          <div className="mt-6 flex items-center justify-between">
            <SectionHeading>Contraseña</SectionHeading>
            <PasswordFormModalBtn
              currentAdmin={currentAdmin}
              className="inline-flex items-center justify-center gap-2 rounded-full bg-accent px-4 py-2 font-medium"
            >
              <PencilIcon className="h-4 w-4" />
              <span>Editar</span>
            </PasswordFormModalBtn>
          </div>
          <hr className="my-2" />

          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            <dd className="text-secondary">Contraseña</dd>
            <dd>***********</dd>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserRoleCard;
