# Mas Seguros Backend API - Postman Collection

This repository contains a comprehensive Postman collection for the Mas Seguros Backend API, a security and emergency response system.

## 📁 Files Included

1. **Mas_Segu<PERSON>_Backend_API.postman_collection.json** - Main API collection
2. **Mas_Seguros_Environments.postman_environment.json** - Environment variables
3. **POSTMAN_COLLECTION_README.md** - This documentation file

## 🚀 Quick Start

### 1. Import Collection and Environment

1. Open Postman
2. Click "Import" button
3. Import both JSON files:
   - `Mas_Seguros_Backend_API.postman_collection.json`
   - `Mas_Seguros_Environments.postman_environment.json`

### 2. Set Environment

1. Select "Mas Seguros Environments" from the environment dropdown
2. Configure the base URL for your target environment:
   - **Production**: `http://************:8000`
   - **Local**: `http://127.0.0.1:8000`
   - **Staging**: `https://masseguros.limacreativa.com`

### 3. Authentication Flow

1. **Register a new user** (if needed):
   - Use `Authentication > Register User`
   - Fill in required fields: full_name, email, phone, password, etc.

2. **Login**:
   - Use `Authentication > Login User`
   - Provide phone and password
   - The auth token will be automatically saved to environment variables

3. **Start using authenticated endpoints**:
   - All other endpoints will use the saved auth token automatically

## 📋 API Endpoints Overview

### 🔐 Authentication
- **POST** `/api/account/register/` - Register new user
- **POST** `/api/account/login/` - User login
- **POST** `/api/account/logout/` - User logout
- **POST** `/api/account/sendverificationcode/` - Send password reset code
- **POST** `/api/account/forgetpasswordverifycode/` - Verify reset code
- **POST** `/api/account/setnewpassword/` - Set new password

### 👤 Profile Management
- **POST** `/api/account/editprofile/` - Update user profile
- **POST** `/api/account/changephone/` - Change phone number
- **POST** `/api/account/changepassword/` - Change password
- **POST** `/api/account/confirmcurrentpasschangepassword/` - Confirm current password

### 📍 Location Management
- **POST** `/api/account/enablelocation/` - Enable/disable location tracking
- **POST** `/api/account/realtimelocation/` - Update real-time location
- **GET** `/api/account/realtimelocation/` - Get current location

### 🚨 Alert Management
- **POST** `/api/alert/` - Create new alert
- **GET** `/api/alert/getallalerts/` - Get all alerts
- **GET** `/api/alert/alertcategories/` - Get alert categories
- **GET** `/api/alert/alertstatuses/` - Get alert statuses
- **GET** `/api/alert/unresolvedalertr/` - Get unresolved alerts
- **GET** `/api/alert/resolvedalert/` - Get resolved alerts

### 🛡️ Shield Management
- **POST** `/api/shield/create-shield/` - Create new shield
- **GET** `/api/shield/get-user-shields/` - Get user's shields
- **GET** `/api/shield/get-shield/` - Get shield details
- **POST** `/api/shield/join-shield-request/` - Request to join shield
- **GET** `/api/shield/shield-members/` - Get shield members
- **GET** `/api/shield/shield-members-locations/` - Get member locations

### 🆘 SOS Emergency
- **POST** `/api/sos/sos-create/` - Create emergency SOS
- **POST** `/api/sos/sos-evidence/` - Add evidence to SOS
- **POST** `/api/sos/sos-cancel/` - Cancel SOS
- **GET** `/api/sos/user-sos/` - Get user's SOS alerts

### 💳 Membership & Payments
- **POST** `/api/Membership/payments/` - Process payment
- **GET** `/api/Membership/ship/{id}` - Get membership details

### 🎫 Support Tickets
- **POST** `/api/ticket/ticket/` - Create support ticket
- **GET** `/api/ticket/ticket-subjects/` - Get ticket categories
- **PUT** `/api/ticket/update-ticket/{id}` - Update ticket

### ❓ FAQ
- **GET** `/api/faq/faq-categories/` - Get FAQ categories
- **GET** `/api/faq/faq-category-questions/` - Get FAQ questions

### 📄 About & Legal
- **GET** `/api/about/datapolicy/` - Get data policy
- **GET** `/api/about/termsandcondition/` - Get terms and conditions

### 📱 Device & Notifications
- **POST** `/api/account/fcm-device-token/` - Register FCM token
- **POST** `/api/account/deleteaccount/` - Delete account
- **GET** `/api/account/accounts/` - Get all accounts (Admin)

## 🔧 Environment Variables

The collection uses the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `base_url` | API base URL | `http://************:8000` |
| `auth_token` | Authentication token | Auto-set after login |
| `user_id` | Current user ID | Auto-set after login |
| `shield_id` | Shield ID for testing | Set manually |
| `alert_id` | Alert ID for testing | Set manually |
| `sos_id` | SOS ID for testing | Set manually |
| `ticket_id` | Ticket ID for testing | Set manually |

## 🔒 Authentication

The API uses Token-based authentication. Include the token in the Authorization header:

```
Authorization: Token your_auth_token_here
```

The collection automatically handles this for authenticated endpoints after login.

## 📝 Request Examples

### Register User
```json
{
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "password": "SecurePassword123",
    "identification_card": "12345678",
    "birth_date": "15/01/1990"
}
```

### Login
```json
{
    "phone": "+**********",
    "password": "SecurePassword123"
}
```

### Create Alert
```json
{
    "title": "Emergency Alert",
    "description": "This is an emergency situation",
    "category": "1",
    "lat": "40.7128",
    "long": "-74.0060"
}
```

## 🧪 Testing

1. Start with authentication endpoints
2. Use the login response to set environment variables
3. Test other endpoints in logical order
4. Use the provided example data or modify as needed

## 🌐 Environment Setup

### Production
- Base URL: `http://************:8000`
- Use for production testing

### Local Development
- Base URL: `http://127.0.0.1:8000`
- Use for local development

### Staging
- Base URL: `https://masseguros.limacreativa.com`
- Use for staging environment testing

## 📞 Support

For API-related questions or issues, please refer to the Django backend documentation or contact the development team.

## 🔄 Updates

This collection is maintained alongside the Django backend. Update the collection when new endpoints are added or existing ones are modified.
