{"data_mtime": **********, "dep_lines": [2, 3, 5, 2, 3, 4, 5, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 20, 10, 20, 5, 30, 30, 30, 5], "dependencies": ["Account.models", "Shield.models", "mas_seguros_backend.settings", "Account", "Shield", "random", "mas_seguros_backend", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "73b93cc5a897ccae70b1267d7af9ae5305541b2d", "id": "AdminSide.CompanyDashboard.models", "ignore_all": true, "interface_hash": "96a4461434e31ec13d7d393f5cb45395931cb902", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\AdminSide\\CompanyDashboard\\models.py", "plugin_data": null, "size": 1849, "suppressed": ["django.db"], "version_id": "1.15.0"}