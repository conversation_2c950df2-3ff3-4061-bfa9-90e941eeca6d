from django.urls import path, reverse, include, re_path
from . import views as shield_views
from . import routes_views as routes_views

# router = routers.DefaultRouter()
# router.register('promo', admin_url.promo_codes)
urlpatterns = [
    path("shield-point-of-interest/", shield_views.ShieldPointOfInterest.as_view()),
    path("point-of-interest-visit-history/", shield_views.PointOfInterestVisitHistory.as_view()),
    # todo confusion
    path("shield-members-locations/", shield_views.ShieldMemberLocations.as_view()),
    # TODO:: 64_MIEMBROS ESCUDO
    path("shield-members-locations-battery/", routes_views.ShieldMemberLocationsBattery.as_view()),
    path("shield-members-routes/", shield_views.ShieldMemberRoutes.as_view()),
    # TODO:: 67_HISTORIAL RUTAS INTEGRANTE ESCUDO
    path("member-routes-history/", routes_views.MemberRoutesHistory.as_view()),
    path('member-location/', routes_views.MemberLocation.as_view()),
    path("route-detail/", routes_views.RouteDetail.as_view()),
    path("download-route-history/", routes_views.DownloadRouteHistory.as_view()),
    path("user-route-profile/", routes_views.UserRouteProfile.as_view()),
    path("member-audio-status/", shield_views.MemberAudioStatus.as_view()),
    path("walkie-talkie-receivers/", shield_views.WalkieTalkieReceivers.as_view()),
    path("shield-alert-and-sos/", shield_views.ShieldAlertAndSos.as_view()),
    path("shield-biometrics/", shield_views.ShieldBiometrics.as_view()),
    path("shield-biometrics-report/", shield_views.ShieldBiometricsReport.as_view()),
    path('sheild-membership/', shield_views.sheild_membership.as_view()),
    path('change-shield-name/', shield_views.ChangeShieldName.as_view()),
    path('create-shield/', shield_views.CreateShield.as_view()),
    path('get-shield/', shield_views.GetShield.as_view()),
    path('join-shield-details/', shield_views.ShieldDetailsAgainstJoiningCode.as_view()),
    path('join-shield-request/', shield_views.ShieldJoiningRequest.as_view()),
    path('shield-requesters/', shield_views.ShieldRequesters.as_view()),
    path('shield-update-status/', shield_views.ShieldUpdateJoiningStatus.as_view()),
    path('pending-shields/', shield_views.ShieldPending.as_view()),
    path('get-user-shields/', shield_views.GetUserShields.as_view()),
    path('assign-shield-admin/', shield_views.AssignShieldAdmin.as_view()),
    path('change-shield-image/', shield_views.ChangeShieldImage.as_view()),
    path('change-shield-join-code/', shield_views.ChangeShieldCode.as_view()),
    path('exit-shield/', shield_views.ExitShield.as_view()),
    path('delete-shield/', shield_views.DeleteShield.as_view()),
    path('add-shield-members/', shield_views.AddShieldMembersInAddMemberViewMobile.as_view()),
    path('remove-shield-members/', shield_views.RemoveShieldMembersInAddMemberViewMobile.as_view()),
    path("shield-members/", shield_views.ShieldMembers.as_view()),
    path("shield-member-hierarchies/", shield_views.ShieldHierarchies.as_view()),
    path("shield-member-hierarchies-by-member/", shield_views.ShieldMemberHierarchies.as_view()),
    path("change-shield-member-hierarchies/", shield_views.ChangeShieldMemberHierarchies.as_view()),
    path('get-onetime-shield-status/', shield_views.GetOneTimeShieldStatus.as_view()),
    path('check-user-in-shield/', shield_views.CheckUserInShield.as_view()),
]

# TODO API to make in shields
# Add shield
