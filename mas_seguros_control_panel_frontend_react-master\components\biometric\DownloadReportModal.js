import React, { useState, useEffect } from "react";
import Modal from "@/components/utility/Modal";
import InputGroup from "@/components/utility/InputGroup";
import { toast } from "react-hot-toast";
import useAxios from "@/hooks/useAxios";
import { utils, writeFile } from "xlsx";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { format } from "date-fns";

const DownloadReportModal = ({ open, close, userData, biometricData = [] }) => {
  const { axios } = useAxios();
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [selectedFormat, setSelectedFormat] = useState("PDF");
  const [isDownloading, setIsDownloading] = useState(false);

  useEffect(() => {
    if (open) {
      setStartDate("");
      setEndDate("");
      setSelectedFormat("PDF");
      setIsDownloading(false);
    }
  }, [open]);

  const handleDownload = async () => {
    if (!startDate || !endDate) {
      toast.error("Por favor selecciona un rango de fechas");
      return;
    }

    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (isNaN(startDateObj) || isNaN(endDateObj)) {
      toast.error("Por favor selecciona fechas válidas");
      return;
    }

    if (startDateObj > endDateObj) {
      toast.error("La fecha de inicio debe ser anterior a la fecha de fin");
      return;
    }

    if (!biometricData || biometricData.length === 0) {
      toast.error("No hay datos biométricos disponibles para descargar");
      return;
    }

    setIsDownloading(true);

    try {
      const filteredData = biometricData.filter(bio => {
        if (!bio.created_at) return false;
        const bioDate = new Date(bio.created_at);
        if (isNaN(bioDate)) return false;

        const bioOnly = new Date(bioDate.getFullYear(), bioDate.getMonth(), bioDate.getDate());
        const startOnly = new Date(startDateObj.getFullYear(), startDateObj.getMonth(), startDateObj.getDate());
        const endOnly = new Date(endDateObj.getFullYear(), endDateObj.getMonth(), endDateObj.getDate());

        return bioOnly >= startOnly && bioOnly <= endOnly;
      });

      filteredData.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

      if (filteredData.length === 0) {
        toast.error("No se encontraron registros en el rango de fechas seleccionado");
        return;
      }

      if (selectedFormat === "PDF") {
        await generatePDF(filteredData, startDate, endDate);
      } else {
        await generateExcel(filteredData, startDate, endDate);
      }

      toast.success(`Reporte ${selectedFormat} descargado correctamente (${filteredData.length} registros)`);
      close();
    } catch (error) {
      console.error("Error generating report:", error);
      toast.error("Error al generar el reporte. Intente nuevamente.");
    } finally {
      setIsDownloading(false);
    }
  };

  const generatePDF = async (data, startDate, endDate) => {
    const doc = new jsPDF();
    const memberName = userData?.full_name || "Usuario";

    try {
      const response = await fetch("/assets/img/logo-white-text.png");
      const blob = await response.blob();
      const base64 = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = () => reject(reader.error);
        reader.readAsDataURL(blob);
      });

      // ✅ Add black background for logo
      doc.setFillColor(0, 0, 0);
      doc.rect(15, 15, 50, 17, "F");         // Y: 15–32
      doc.addImage(base64, "PNG", 20, 17, 40, 13); // inside black bg
    } catch (e) {
      console.warn("Logo not loaded.");
    }

    // ✅ Start content just below logo
    const startY = 42;

    doc.setFontSize(10);
    doc.text(format(new Date(), "dd/MM/yyyy"), doc.internal.pageSize.getWidth() - 20, startY - 10, { align: "right" });

    doc.setFontSize(16);
    doc.text("Reporte Biométrico", 15, startY);

    doc.setFontSize(12);
    doc.text(`Miembro: ${memberName}`, 15, startY + 10);

    const startFormatted = format(new Date(startDate), "dd/MM/yyyy");
    const endFormatted = format(new Date(endDate), "dd/MM/yyyy");
    doc.text(`Período: ${startFormatted} - ${endFormatted}`, 15, startY + 18);

    const tableData = data.map(bio => {
      const dateObj = new Date(bio.created_at);
      return [
        bio.biometric_code || `B${bio.id || "N/A"}`,
        bio.userprofile?.full_name || "Usuario",
        format(dateObj, "HH:mm"),
        format(dateObj, "dd/MM/yyyy"),
        bio.address || "Ubicación no disponible",
        bio.type || "SALIDA"
      ];
    });

    doc.autoTable({
      startY: 68,
      theme: "grid",
      tableWidth: 'wrap', // ✅ Ensure it fits inside the page
      head: [["ID", "Nombre", "Hora", "Fecha", "Ubicación", "Tipo"]],
      body: tableData,
      styles: {
        fontSize: 8,
        halign: "left",
        valign: "middle",
        lineColor: [0, 0, 0],
        lineWidth: 0.1,
        cellPadding: 3
      },
      headStyles: {
        fillColor: [0, 0, 0],
        textColor: [255, 255, 255],
        fontSize: 9,
        halign: "center"
      },
      columnStyles: {
        0: { cellWidth: 20 },
        1: { cellWidth: 35 },
        2: { cellWidth: 15, halign: "center" },
        3: { cellWidth: 20, halign: "center" },
        4: { cellWidth: 60 }, // reduce from 65
        5: { cellWidth: 20, halign: "center" }
      }
    });

    const safeName = memberName.replace(/[^a-zA-Z0-9\s]/g, "").replace(/\s+/g, "_");
    const fileName = `Reporte_Biometrico_${safeName}_${format(new Date(), "yyyy-MM-dd")}.pdf`;
    doc.save(fileName);
  };

  const generateExcel = async (data, startDate, endDate) => {
    const wb = utils.book_new();
    const memberName = userData?.full_name || "Usuario";
    const startFormatted = format(new Date(startDate), "dd/MM/yyyy");
    const endFormatted = format(new Date(endDate), "dd/MM/yyyy");

    const sheetData = [
      ["Reporte Biométrico"],
      [`Miembro: ${memberName}`],
      [`Período: ${startFormatted} - ${endFormatted}`],
      [],
      ["ID", "Nombre", "Hora", "Fecha", "Ubicación", "Coordenadas", "Tipo"],
      ...data.map(bio => {
        const dateObj = new Date(bio.created_at);
        return [
          bio.biometric_code || `B${bio.id || "N/A"}`,
          bio.userprofile?.full_name || "Usuario",
          format(dateObj, "HH:mm"),
          format(dateObj, "dd/MM/yyyy"),
          bio.address || "Ubicación no disponible",
          (bio.lat && bio.long) ? `${bio.lat}, ${bio.long}` : "Coordenadas no disponibles",
          bio.type || "SALIDA"
        ];
      })
    ];

    const ws = utils.aoa_to_sheet(sheetData);
    ws["!cols"] = [
      { wch: 10 },
      { wch: 25 },
      { wch: 8 },
      { wch: 12 },
      { wch: 50 },
      { wch: 25 },
      { wch: 10 }
    ];

    utils.book_append_sheet(wb, ws, "Reporte Biométrico");
    const safeName = memberName.replace(/[^a-zA-Z0-9\s]/g, "").replace(/\s+/g, "_");
    const fileName = `Reporte_Biometrico_${safeName}_${format(new Date(), "yyyy-MM-dd")}.xlsx`;
    writeFile(wb, fileName);
  };

  return (
    <Modal open={open} close={close} className="w-full max-w-md overflow-hidden bg-white shadow-xl">
      <Modal.Wrapper>
        <Modal.Header className="bg-accent">
          <h2 className="text-lg font-medium">Descargar Reportes</h2>
          <Modal.XBtn onClick={close} />
        </Modal.Header>
        <Modal.Body className="p-6">
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Miembro</label>
              <div className="bg-gray-50 p-3 rounded">
                <p className="font-medium">{userData?.full_name || "Usuario"}</p>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Fecha</label>
              <div className="grid grid-cols-2 gap-3">
                <InputGroup>
                  <InputGroup.Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="text-sm"
                  />
                </InputGroup>
                <InputGroup>
                  <InputGroup.Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="text-sm"
                  />
                </InputGroup>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Formato</label>
              <div className="flex gap-6">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="format"
                    value="PDF"
                    checked={selectedFormat === "PDF"}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    className="h-4 w-4 text-primary border-gray-300"
                  />
                  <span className="ml-2 text-sm">PDF</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="format"
                    value="XLSX"
                    checked={selectedFormat === "XLSX"}
                    onChange={(e) => setSelectedFormat(e.target.value)}
                    className="h-4 w-4 text-primary border-gray-300"
                  />
                  <span className="ml-2 text-sm">XLSX</span>
                </label>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer className="bg-accent px-6 py-4">
          <div className="flex justify-end">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className="bg-black text-white px-6 py-2 rounded font-medium hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDownloading ? "Descargando..." : "Descargar"}
            </button>
          </div>
        </Modal.Footer>
      </Modal.Wrapper>
    </Modal>
  );
};

export default DownloadReportModal;
