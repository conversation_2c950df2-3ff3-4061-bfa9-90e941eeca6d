from rest_framework import serializers
from django.contrib.auth.models import User
from Account import models as account_models
from Membership import models as membership_models
from Shield import models as shield_models
from Alert import models as alert_models
from Ticket import models as ticket_models
from Sos import models as sos_models


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializerForDashboard(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        # fields = '__all__'
        exclude = ['id', ]


class MembershipSerializerForDashboard(serializers.ModelSerializer):
    userprofile = UserProfileSerializerForDashboard()

    class Meta:
        model = membership_models.MembershipModel
        fields = '__all__'


class AlertSerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializerForDashboard()

    class Meta:
        model = alert_models.AlertModel
        fields = "__all__"


class SosSerializer(serializers.ModelSerializer):
    # sender = UserProfileSerializerForDashboard()

    class Meta:
        model = sos_models.Sos
        fields = "__all__"


class ShieldsSerializerForDashboard(serializers.ModelSerializer):
    members = UserProfileSerializerForDashboard(many=True)
    admin = UserProfileSerializerForDashboard(many=True)

    # alert = AlertSerializer()

    class Meta:
        model = shield_models.ShieldModel
        fields = '__all__'


class TicketSubjectSerializerForDashboard(serializers.ModelSerializer):
    class Meta:
        model = ticket_models.TicketSubject
        fields = '__all__'


class TicketSerializerForDashboard(serializers.ModelSerializer):
    user = UserProfileSerializerForDashboard()
    title = TicketSubjectSerializerForDashboard()

    class Meta:
        model = ticket_models.Ticket
        fields = '__all__'


class GetMonthlySerializer(serializers.Serializer):
    month = serializers.CharField(required=True)
    year = serializers.CharField(required=True)


class GetUserIDSerializer(serializers.Serializer):
    # shield_id = serializers.IntegerField(required=True)
    user_id = serializers.IntegerField(required=True)
    month = serializers.CharField(required=False)
    year = serializers.CharField(required=False)


class UserDownloadBiometricSerializer(serializers.Serializer):
    member_id = serializers.IntegerField(required=True)
    date = serializers.DateField(required=False)
    report_type = serializers.CharField(max_length=100, required=True)


class UserLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = shield_models.Route
        fields = "__all__"


class UserShieldsSerializer(serializers.ModelSerializer):
    admin = UserProfileSerializerForDashboard(many=True)
    members_count = serializers.SerializerMethodField()
    logo_url = serializers.SerializerMethodField()

    def get_members_count(self, obj):
        """Get the count of members in the shield"""
        return obj.members.count()

    def get_logo_url(self, obj):
        """Get the full URL for the shield logo"""
        if obj.logo:
            from mas_seguros_backend import settings as backend_setting
            return backend_setting.Base_url_path.format(url=obj.logo.url)
        return None

    class Meta:
        model = shield_models.ShieldModel
        fields = ['id', 'shield_name', 'logo', 'logo_url', 'shield_code', 'admin', 'shield_type', 'members_count', 'created_at',
                  'condition', ]


class SuspendSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    suspended = serializers.CharField(max_length=10, required=True)
