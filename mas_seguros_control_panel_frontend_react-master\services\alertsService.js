/**
 * Alerts and SOS Service
 * Handles API calls for alerts and SOS functionality
 */

export const alertsService = {
  /**
   * Post a comment to an alert or SOS
   * @param {Object} axios - Axios instance
   * @param {Object} data - Comment data
   * @param {number} data.id - Alert/SOS ID
   * @param {string} data.type - Type ('alert' or 'sos')
   * @param {string} data.comment - Comment text
   */
  postComment: async (axios, data) => {
    const response = await axios.post('/adminside/api/alert/postcommentalertsos/', data);
    return response.data;
  },

  /**
   * Get comments for an alert or SOS
   * @param {Object} axios - Axios instance
   * @param {number} id - Alert/SOS ID
   * @param {string} type - Type ('alert' or 'sos')
   */
  getComments: async (axios, id, type) => {
    const response = await axios.get('/adminside/api/alert/getcommentalertsos/', {
      params: { id, type }
    });
    return response.data?.data || [];
  },

  /**
   * Change alert status
   * @param {Object} axios - Axios instance
   * @param {Object} data - Status change data
   * @param {number} data.id - Alert ID
   * @param {string} data.status - New status
   */
  changeAlertStatus: async (axios, data) => {
    const response = await axios.post('/adminside/api/alert/changealertstatus/', data);
    return response.data;
  },

  /**
   * Change SOS status
   * @param {Object} axios - Axios instance
   * @param {Object} data - Status change data
   * @param {number} data.id - SOS ID
   * @param {string} data.status - New status
   */
  changeSosStatus: async (axios, data) => {
    const response = await axios.post('/adminside/api/alert/changesosstatus/', data);
    return response.data;
  },

  /**
   * Get modification history for an alert
   * @param {Object} axios - Axios instance
   * @param {number} id - Alert ID
   */
  getAlertHistory: async (axios, id) => {
    const response = await axios.get('/adminside/api/alert/getalertmodifyhistory/', {
      params: { id }
    });
    return response.data?.data || [];
  },

  /**
   * Get modification history for a SOS
   * @param {Object} axios - Axios instance
   * @param {number} id - SOS ID
   */
  getSosHistory: async (axios, id) => {
    const response = await axios.get('/adminside/api/alert/getsosmodifyhistory/', {
      params: { id }
    });
    return response.data?.data || [];
  },

  /**
   * Get modification history for an alert or SOS
   * @param {Object} axios - Axios instance
   * @param {number} id - Alert/SOS ID
   * @param {string} type - Type ('alert' or 'sos')
   */
  getHistory: async (axios, id, type) => {
    if (type === 'sos') {
      return alertsService.getSosHistory(axios, id);
    } else {
      return alertsService.getAlertHistory(axios, id);
    }
  },

  /**
   * Change status for an alert or SOS
   * @param {Object} axios - Axios instance
   * @param {number} id - Alert/SOS ID
   * @param {string} type - Type ('alert' or 'sos')
   * @param {string} status - New status
   */
  changeStatus: async (axios, id, type, status) => {
    const data = { id, status };
    if (type === 'sos') {
      return alertsService.changeSosStatus(axios, data);
    } else {
      return alertsService.changeAlertStatus(axios, data);
    }
  }
};

export default alertsService;
