from django.db.models import Q
from django.shortcuts import render
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework.generics import get_object_or_404
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils, prefixes as backend_prefixes
from . import utils as alert_utils, serializers as alert_serializers
from django.http import JsonResponse
from Alert import models as alert_models
from rest_framework import status
from Sos import models as sos_models, serializer as sos_serializer
from Account import models as account_models
from django.core.paginator import Paginator
from django.db import transaction


# Create your views here.


class ChangeAlertStatus(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.ChangeAlertStatusSerializer
    get_alert_serializer_class = alert_serializers.GetAlertSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        alert_obj = alert_models.AlertModel.objects.filter(id=request.data.get('id')).last()
        if alert_obj:
            status_name = request.data.get('status')
            # Try to find status by name first, then by ID as fallback
            status_obj = alert_models.AlertStatus.objects.filter(name=status_name).last()
            if not status_obj and status_name.isdigit():
                status_obj = alert_models.AlertStatus.objects.filter(id=int(status_name)).last()

            if status_obj:
                alert_obj.status = status_obj
                alert_obj.save()
                alert_models.AlertModifyHistory.objects.create(alert=alert_obj, userprofile=request.user.userprofile,
                                                               status=status_obj.name)
                serializer = self.get_alert_serializer_class(alert_obj)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Alert status changed'))
            else:
                # Provide more helpful error message
                valid_statuses = [choice[0] for choice in alert_models.alert_status]
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg=f'Invalid status. Valid options are: {", ".join(valid_statuses)}'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='Alert Not Found'))


class getAllAlert(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetAlertSerializer

    def get(self, request):
        try:
            # Get query parameters for filtering - handle multiple values
            category_filter = request.query_params.getlist('category') or [request.query_params.get('category')] if request.query_params.get('category') else []
            status_filter = request.query_params.getlist('status') or [request.query_params.get('status')] if request.query_params.get('status') else []
            search_query = request.query_params.get('search', None)

            # Clean up None values from filters
            category_filter = [f for f in category_filter if f is not None]
            status_filter = [f for f in status_filter if f is not None]

            # Optimize alerts query with select_related and prefetch_related
            alerts_queryset = alert_models.AlertModel.objects.select_related(
                'userprofile__user',
                'category',
                'status'
            ).prefetch_related(
                'shield_alerts'
            ).order_by('-created_at')

            # Apply filters for alerts
            if category_filter:
                # Handle multiple category filters
                category_conditions = Q()
                for cat_filter in category_filter:
                    if cat_filter.lower() == 'alert':
                        # Show all alerts except those that are specifically police alerts
                        category_conditions |= Q(category__name__isnull=False) & ~Q(category__name__icontains='police')
                    elif cat_filter.lower() == 'alert-police':
                        category_conditions |= Q(category__name__icontains='police')
                    elif cat_filter.lower() != 'sos':
                        # For any other alert type, include it
                        category_conditions |= Q(category__name__icontains=cat_filter)

                if category_conditions:
                    alerts_queryset = alerts_queryset.filter(category_conditions)
                elif 'sos' in [f.lower() for f in category_filter]:
                    # If only SOS is selected, exclude all alerts
                    alerts_queryset = alerts_queryset.none()

            if status_filter:
                # Map frontend status values to backend status values
                status_mapping = {
                    'pendiente': ['Alerta enviada'],
                    'ayuda enviada': ['Ayuda enviada'],
                    'resuelto': ['Alerta resuelta'],
                    'en proceso': ['Ayuda enviada'],  # Map to existing status
                    'activo': ['Alerta enviada'],     # Map to existing status
                    'inactivo': ['Alerta resuelta']   # Map to existing status
                }

                # Handle multiple status filters
                all_mapped_statuses = []
                for status_value in status_filter:
                    mapped_statuses = status_mapping.get(status_value.lower(), [status_value])
                    all_mapped_statuses.extend(mapped_statuses)

                if all_mapped_statuses:
                    alerts_queryset = alerts_queryset.filter(status__name__in=all_mapped_statuses)

            if search_query:
                alerts_queryset = alerts_queryset.filter(
                    Q(userprofile__full_name__icontains=search_query) |
                    Q(userprofile__user__username__icontains=search_query) |
                    Q(description__icontains=search_query) |
                    Q(num__icontains=search_query)
                )

            # Optimize SOS query with select_related and prefetch_related
            sos_queryset = sos_models.Sos.objects.select_related(
                'sender__user',
                'shield'
            ).prefetch_related(
                'soscomment_set',
                'sosevidence_set'
            ).order_by('-created_at')

            # Apply filters for SOS
            if category_filter:
                # Check if SOS should be included
                include_sos = any(cat.lower() == 'sos' for cat in category_filter)
                exclude_sos = any(cat.lower() in ['alert', 'alert-police'] for cat in category_filter)

                if not include_sos and exclude_sos:
                    # If only alert types are selected, exclude SOS
                    sos_queryset = sos_queryset.none()
                elif not include_sos and not exclude_sos:
                    # If no relevant category is selected, include SOS
                    pass
                # If include_sos is True, keep all SOS records

            if status_filter:
                # Map frontend status values to backend SOS status values
                sos_status_mapping = {
                    'pendiente': ['Sos enviada'],
                    'ayuda enviada': ['Ayuda enviada'],
                    'resuelto': ['Sos resuelta'],
                    'en proceso': ['Ayuda enviada'],  # Map to existing status
                    'activo': ['Sos enviada'],       # Map to existing status
                    'inactivo': ['Sos resuelta']     # Map to existing status
                }

                # Handle multiple status filters for SOS
                all_mapped_sos_statuses = []
                for status_value in status_filter:
                    mapped_statuses = sos_status_mapping.get(status_value.lower(), [status_value])
                    all_mapped_sos_statuses.extend(mapped_statuses)

                if all_mapped_sos_statuses:
                    sos_queryset = sos_queryset.filter(status__in=all_mapped_sos_statuses)

            if search_query:
                sos_queryset = sos_queryset.filter(
                    Q(sender__full_name__icontains=search_query) |
                    Q(sender__user__username__icontains=search_query) |
                    Q(location__icontains=search_query)
                )

            # Serialize the data
            alert_serializer = alert_serializers.GetAlertSerializer(alerts_queryset, many=True)
            sos_serializer = alert_serializers.SosSerializer(sos_queryset, many=True)

            # Transform alerts data
            transformed_alerts = []
            for alert in alert_serializer.data:
                try:
                    transformed_alerts.append({
                        'id': alert.get('id', ''),
                        'type': 'alert',
                        'category': alert.get('category', ''),
                        'status': alert.get('status_name', ''),
                        'alert_date': alert.get('alert_date', ''),
                        'alert_time': alert.get('alert_time', ''),
                        'evidence_url': alert.get('evidence_url'),
                        'evidence_number': f"A{alert.get('id', '')}",
                        'rating': alert.get('rating'),
                        'rating_description': alert.get('rating_description', ''),
                        'description': alert.get('description', 'No hay comentarios disponibles'),
                        'userprofile': {
                            'full_name': alert.get('userprofile', {}).get('full_name', 'Usuario'),
                            'user': alert.get('userprofile', {}).get('user', {'id': alert.get('id', '')})
                        },
                        'lat': alert.get('lat'),
                        'long': alert.get('long'),
                        'current_speed': alert.get('current_speed'),
                        'phone_battery': alert.get('phone_battery'),
                        'created_at': alert.get('created_at')
                    })
                except Exception as e:
                    print(f"Error transforming alert {alert.get('id', 'unknown')}: {str(e)}")
                    continue

            # Transform SOS data
            transformed_sos = []
            for sos_item in sos_serializer.data:
                try:
                    transformed_sos.append({
                        'id': sos_item.get('id', ''),
                        'type': 'sos',
                        'category': 'SOS',
                        'status': sos_item.get('status', ''),
                        'alert_date': sos_item.get('alert_datetime', '').split('T')[0] if sos_item.get('alert_datetime') else '',
                        'alert_time': sos_item.get('alert_datetime', '').split('T')[1].split('.')[0] if sos_item.get('alert_datetime') else '',
                        'evidence_url': None,
                        'evidence_number': f"S{sos_item.get('id', '')}",
                        'rating': sos_item.get('rating'),
                        'rating_description': sos_item.get('rating_description', ''),
                        'description': sos_item.get('description', 'No hay comentarios disponibles para este SOS'),
                        'sender': sos_item.get('sender', {}),
                        'userprofile': None,  # SOS doesn't have userprofile, it has sender
                        'lat': sos_item.get('lat'),
                        'long': sos_item.get('long'),
                        'created_at': sos_item.get('created_at')
                    })
                except Exception as e:
                    print(f"Error transforming SOS {sos_item.get('id', 'unknown')}: {str(e)}")
                    continue

            # Combine both alerts and SOS records with pagination info
            total_alerts = len(transformed_alerts)
            total_sos = len(transformed_sos)
            total_records = total_alerts + total_sos

            # Combine and sort by created_at with proper null handling
            combined_data = transformed_alerts + transformed_sos
            combined_data.sort(key=lambda x: x.get('created_at') or '', reverse=True)

            data = {
                'alerts': transformed_alerts,
                'sos': transformed_sos,
                'combined': combined_data,
                'pagination': {
                    'total_alerts': total_alerts,
                    'total_sos': total_sos,
                    'total_records': total_records,
                    'has_filters': bool(category_filter or status_filter or search_query),
                    'applied_filters': {
                        'category': category_filter,
                        'status': status_filter,
                        'search': search_query
                    }
                }
            }

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=data,
                    msg=f'Retrieved {total_records} records ({total_alerts} alerts, {total_sos} SOS)'
                )
            )
        except Exception as e:
            print(f"Error in getAllAlert: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving alerts and SOS: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request, pk=None, format=None):
        post = get_object_or_404(alert_models.AlertModel.objects.all(), pk=pk)
        serializer = alert_serializers.getallAlertSerilizer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)


def get_alert(request):
    if request.method == "GET":  # get request to fetch the data
        codes = alert_models.AlertModel.objects.all()
        serializer = alert_serializers.getallAlertSerilizer(codes, many=True)
        return JsonResponse(serializer.data, safe=False)


class GetAlertModifyHistorty(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetAlertModifyHistorySerializer
    get_alert_serializer_class = alert_serializers.AlertModifyHistorySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        alert_obj = alert_models.AlertModel.objects.filter(id=id).last()
        history_models = alert_models.AlertModifyHistory.objects.filter(alert=alert_obj).order_by('-created_at')
        if alert_obj:
            if history_models:
                serializer = self.get_alert_serializer_class(history_models, many=True)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Alert history'))
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='History Model Not Found'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='Alert Not Found'))


class GetSosModifyHistorty(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetAlertModifyHistorySerializer
    get_alert_serializer_class = alert_serializers.SosModifyHistorySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        # Get SOS object first
        sos_obj = sos_models.Sos.objects.filter(id=id).last()
        if not sos_obj:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='SOS Not Found'))

        # Get SOS modification history
        alert_obj = sos_models.SosModifyHistory.objects.filter(sos=sos_obj).order_by('-created_at')
        if alert_obj:
            serializer = self.get_alert_serializer_class(alert_obj, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Sos history'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='sos Not Found'))


class ChangeSosStatus(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.ChangeSosStatusSerializer
    get_alert_serializer_class = alert_serializers.SosSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)

        status_name = request.data.get('status')
        sos_obj = sos_models.Sos.objects.filter(id=request.data.get('id')).last()

        if sos_obj:
            # Validate status against allowed choices
            valid_statuses = [choice[0] for choice in sos_models.sos_status]
            if status_name not in valid_statuses:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg=f'Invalid status. Valid options are: {", ".join(valid_statuses)}'))

            sos_obj.status = status_name
            sos_obj.save()
            sos_models.SosModifyHistory.objects.create(sos=sos_obj, userprofile=request.user.userprofile,
                                                       status=status_name)
            serializer = self.get_alert_serializer_class(sos_obj)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Sos Status Changed'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='SOS Not Found'))


class PostCommentAlertSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.PostCommentAlertSosSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        obj_id = request.data.get('id')
        type = request.data.get('type').lower()
        object = None
        if type == 'alert':
            object = alert_models.AlertModel.objects.filter(id=obj_id).last()
        elif type == 'sos':
            object = sos_models.Sos.objects.filter(id=obj_id).last()
        if object:
            try:
                if type == 'alert':
                    alert_models.AlertComment.objects.create(alert=object, comment=request.data.get('comment'),
                                                             userprofile=request.user.userprofile)
                elif type == 'sos':
                    sos_models.SosComment.objects.create(sos=object, comment=request.data.get('comment'),
                                                         userprofile=request.user.userprofile)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Comment Posted Successfully'))
            except:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='Something went wrong From Backend'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg=f'{type} Not Found'))


class GetCommentAlertSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetCommentAlertSosSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        obj_id = request.query_params.get('id')
        type = request.query_params.get('type').lower()
        print("===this is type==", type)
        object = None
        if type == 'alert':
            object = alert_models.AlertModel.objects.filter(id=obj_id).last()
        elif type == 'sos':
            object = sos_models.Sos.objects.filter(id=obj_id).last()
        if object:
            try:
                if type == 'alert':
                    alert_comments = alert_models.AlertComment.objects.filter(alert=object)
                    serializer = alert_serializers.AlertCommentHistorySerializer(alert_comments, many=True)
                elif type == 'sos':
                    sos_comments = sos_models.SosComment.objects.filter(sos=object)
                    serializer = sos_serializer.SosCommentHistorySerializer(sos_comments, many=True)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Comment Posted Successfully'))
            except Exception as e:
                print("===this is exception==", e)
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='Something went wrong From Backend'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg=f'{type} Not Found'))
        # return Response(
        #     backend_utils.success_response(status_code=status.HTTP_200_OK, data={'name': 'mannan',
        #                                                                          'age': 25,
        #                                                                          'domain': 'python'},
        #                                    msg="ok testing finish"))


class GetUserAlerts(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='user_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get user profile
            try:
                user_profile = account_models.UserProfile.objects.get(user_id=user_id)
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='User not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get alerts for this user with optimized queries
            alerts = alert_models.AlertModel.objects.filter(userprofile=user_profile).select_related(
                'userprofile__user',
                'category',
                'status'
            ).prefetch_related(
                'shield_alerts'
            ).order_by('-created_at')

            alert_serializer = alert_serializers.GetAlertSerializer(alerts, many=True)

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=alert_serializer.data,
                    msg='User alerts retrieved successfully'
                )
            )

        except Exception as e:
            print(f"Error in GetUserAlerts: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving user alerts: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetUserAlertsAndSos(APIView):
    """
    Combined endpoint to get both alerts and SOS data for a user in a single API call
    This reduces frontend API calls and improves performance
    """
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='user_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get user profile
            try:
                user_profile = account_models.UserProfile.objects.get(user_id=user_id)
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='User not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get alerts for this user with optimized queries
            alerts = alert_models.AlertModel.objects.filter(userprofile=user_profile).select_related(
                'userprofile__user',
                'category',
                'status'
            ).prefetch_related(
                'shield_alerts'
            ).order_by('-created_at')

            # Get SOS records for this user with optimized queries
            from Sos import models as sos_models
            from Sos import serializer as sos_serializers

            sos_records = sos_models.Sos.objects.filter(sender=user_profile).select_related(
                'shield'
            ).prefetch_related(
                'soscomment_set',
                'sosevidence_set'
            ).order_by('-created_at')

            # Serialize the data
            alert_serializer = alert_serializers.GetAlertSerializer(alerts, many=True)
            sos_serializer = sos_serializers.SosDetailSerializer(sos_records, many=True)

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data={
                        'alerts': alert_serializer.data,
                        'sos': sos_serializer.data
                    },
                    msg='User alerts and SOS records retrieved successfully'
                )
            )

        except Exception as e:
            print(f"Error in GetUserAlertsAndSos: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving user data: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
