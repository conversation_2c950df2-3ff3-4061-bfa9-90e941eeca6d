import re

NUMBER_LENGTH = 6
ALERT = 'Alert'
USER_UI = 'UI'


def get_new_prefix_number_for_user_profile(_model, field_name, prefix: str):
    """
    Params:
        company: Company object to filter
        _model: Model name
        field_name: To get value from
        prefix: Constant prefix i:e (CUS, BILL, etc.)
    return:
        str type new bill number
    """
    obj = _model.objects.all().last()
    last_value = 0
    if obj:
        last_value = getattr(obj, field_name)
    next_numeric_value = get_next_numeric_value(last_value)
    return "{}{}".format(prefix, generate_str_numer(number=next_numeric_value))


def get_new_prefix_number(userprofile, _model, field_name, prefix: str):
    """
    Params:
        company: Company object to filter
        _model: Model name
        field_name: To get value from
        prefix: Constant prefix i:e (CUS, BILL, etc.)
    return:
        str type new bill number
    """
    obj = get_last_object(_model, userprofile)
    last_value = 0
    if obj:
        last_value = get_field_value(obj, field_name)
    next_numeric_value = get_next_numeric_value(last_value)
    return "{}-{}".format(prefix, generate_str_numer(number=next_numeric_value))


def get_last_object(_model, userprofile):
    """
    Params:
        _model: Model Name
    return:
        model object
    """
    return _model.objects.filter(userprofile=userprofile).last()


def get_field_value(obj, field_name: str):
    """
    Params:
        obj: object of model
        field_name: name of the field to get value from
    return:
        field value
    """
    return getattr(obj, field_name)


def get_next_numeric_value(value: str):
    """
    Params:
        value: str value to get number from

    return:
        next Numeric value
    """
    try:
        number = int(re.findall(r'\d+', value)[0])
    except:
        number = 0
    return number + 1


def generate_str_numer(num_len=NUMBER_LENGTH, number: int = 1):
    """
    Params:
        num_len: length of number to generate
        number: to convert in string number
    return
        str number with defined length
    """
    n_str = str(number)
    le = len(n_str)
    rest = num_len - le if le <= num_len else 0
    zeros = ['0' for i in range(rest)]
    return "{}{}".format("".join(zeros), n_str)
