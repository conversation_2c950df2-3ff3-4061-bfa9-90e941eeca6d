import ReduxWrapper from "@/components/layouts/ReduxWrapper";
import { QueryClient, QueryClientProvider } from "react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 30000, // 30 seconds
      cacheTime: 300000, // 5 minutes
      retry: false,
    },
  },
});

const Wrapper = ({ children }) => {
  return (
    <ReduxWrapper>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </ReduxWrapper>
  );
};

export default Wrapper;
