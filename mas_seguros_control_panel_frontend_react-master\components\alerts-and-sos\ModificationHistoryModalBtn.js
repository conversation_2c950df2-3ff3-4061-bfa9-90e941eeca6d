import Modal from "@/components/utility/Modal";
import classNames from "classnames";
import React, { createElement, useState, useMemo } from "react";
import Table from "../Table";
import useAxios from "@/hooks/useAxios";
import { useQuery } from "react-query";
import { format } from "date-fns";

const ModificationHistoryModalBtn = ({
  as = "button",
  alert = {},
  className = "",
  ...props
}) => {
  const [open, setOpen] = useState(false);
  const { axios } = useAxios();

  // Determine alert type and ID
  const alertType = useMemo(() => {
    const type = alert?.type?.toLowerCase();
    const categoryName = alert?.category?.name?.toLowerCase();
    return type === 'sos' || categoryName === 'sos' || alert?.sender ? 'sos' : 'alert';
  }, [alert?.type, alert?.category?.name, alert?.sender]);

  const alertId = alert?.id;

  // Fetch modification history for this alert/SOS
  const { data: history = [], isLoading: historyLoading } = useQuery(
    [`history-${alertType}-${alertId}`],
    async () => {
      if (!alertId) return [];
      try {
        const endpoint = alertType === 'sos'
          ? '/adminside/api/alert/getsosmodifyhistory/'
          : '/adminside/api/alert/getalertmodifyhistory/';

        const response = await axios.get(endpoint, {
          params: { id: alertId }
        });
        return response.data?.data || [];
      } catch (error) {
        console.error('Error fetching modification history:', error);
        return [];
      }
    },
    {
      enabled: !!alertId && open,
      refetchOnWindowFocus: false,
    }
  );

  const close = () => {
    setOpen(false);
  };

  // Format date for display
  const formatDate = (dateStr) => {
    try {
      if (!dateStr) return "N/A";
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return "N/A";
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      });
    } catch (error) {
      return "N/A";
    }
  };

  const formatTime = (dateStr) => {
    try {
      if (!dateStr) return "N/A";
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return "N/A";
      return date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      }) + ' Hrs';
    } catch (error) {
      return "N/A";
    }
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-2xl overflow-hidden bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Historial de modificación</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="!p-0">
            <div className="grid grid-cols-3 gap-5 p-5 text-sm">
              <div>
                <dd className="font-semibold">ID Alerta</dd>
                <dd className={`font-semibold ${alertType === 'sos' ? 'text-danger' : 'text-primary'}`}>
                  {alertType === 'sos' ? 'SOS' : alert.category?.name || 'Alerta'}
                </dd>
                <dd>
                  {alertType === 'sos' ? `SOS#${alertId}` : `${alert.category?.name || 'Alerta'}#${alertId}`}
                </dd>
              </div>
              <div>
                <dd className="font-semibold">Usuario</dd>
                <dd>{alert.userprofile?.full_name || alert.sender?.full_name || "N/A"}</dd>
                <dd>ID. {alert.userprofile?.user?.id || alert.sender?.user?.id || alertId}</dd>
              </div>
              <div>
                <dd className="font-semibold">Horario creación</dd>
                <dd>{formatDate(alert.created_at)}</dd>
                <dd>{formatTime(alert.created_at)}</dd>
              </div>
            </div>
            <div className="bg-accent py-2.5 px-4">
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Estado</Table.Th>
                    <Table.Th>Modificado por</Table.Th>
                    <Table.Th>Horario</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {historyLoading ? (
                    <Table.Tr>
                      <Table.Td colSpan={3} className="text-center">
                        Cargando historial...
                      </Table.Td>
                    </Table.Tr>
                  ) : history.length > 0 ? (
                    history.map((historyItem, index) => {
                      // Get status styling
                      const getStatusStyle = (status) => {
                        const statusLower = status?.toLowerCase() || '';
                        if (statusLower.includes('resuel') || statusLower.includes('resolve')) {
                          return { className: 'bg-primary text-primary', text: 'Resuelto' };
                        } else if (statusLower.includes('ayuda') || statusLower.includes('help')) {
                          return { className: 'bg-warning text-warning', text: 'Ayuda enviada' };
                        } else {
                          return { className: 'bg-danger text-danger', text: 'Pendiente' };
                        }
                      };

                      const statusStyle = getStatusStyle(historyItem.status);

                      return (
                        <Table.Tr key={index}>
                          <Table.Td>
                            <span className={`inline-flex items-center rounded-full bg-opacity-20 px-3 py-1.5 text-sm font-semibold ${statusStyle.className}`}>
                              <svg
                                className="mr-1.5 h-2 w-2"
                                fill="currentColor"
                                viewBox="0 0 8 8"
                              >
                                <circle cx={5} cy={4} r={3} />
                              </svg>
                              {statusStyle.text}
                            </span>
                          </Table.Td>
                          <Table.Td>
                            <div className="inline-flex items-center gap-3.5">
                              <div className="h-11 w-11 bg-gray-200 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-600">
                                  {(historyItem.userprofile?.full_name || "Admin").charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <div>
                                <dd>{historyItem.userprofile?.full_name || "Administrador"}</dd>
                                <dd>ID-{historyItem.userprofile?.user?.id || "N/A"}</dd>
                              </div>
                            </div>
                          </Table.Td>
                          <Table.Td>
                            <dd>{formatDate(historyItem.created_at)}</dd>
                            <dd>{formatTime(historyItem.created_at)}</dd>
                          </Table.Td>
                        </Table.Tr>
                      );
                    })
                  ) : (
                    <Table.Tr>
                      <Table.Td colSpan={3} className="text-center text-gray-500">
                        No hay historial de modificaciones disponible
                      </Table.Td>
                    </Table.Tr>
                  )}
                </Table.Tbody>
              </Table>
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-black text-white">
              Cerrar
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        type: "button",
        onClick: () => setOpen(true),
        className: classNames(className, "font-semibold hover:underline"),
        ...props,
      })}
    </>
  );
};

export default ModificationHistoryModalBtn;
