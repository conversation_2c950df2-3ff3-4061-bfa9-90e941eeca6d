import React, { createElement, Fragment, useState, useMemo, memo } from "react";
import Table from "../Table";
import classNames from "classnames";
import { StarIcon } from "@heroicons/react/20/solid";
import EvidenceModalBtn from "../shields/EvidenceModalBtn";
import CommentsModalBtn from "./CommentsModalBtn";
import ModificationHistoryModalBtn from "./ModificationHistoryModalBtn";
import QualificationModalBtn from "./QualificationModalBtn";
import { Popover, Transition } from "@headlessui/react";
import ConfirmationModal from "../utility/ConfirmationModal";
import { format } from "date-fns";
import useAxios from "@/hooks/useAxios";
import { useMutation, useQueryClient } from "react-query";
import toast from "react-hot-toast";

const AlertsSOSTable = memo(({ alerts = [], isLoading, isSuccess, isError, error, sort, setSort }) => {
  // Memoize alerts to prevent unnecessary re-renders
  const memoizedAlerts = useMemo(() => alerts, [alerts]);
  return (
    <Table
      wrapperClassName="pb-96 no-scrollbar"
      dataCount={memoizedAlerts.length}
      isLoading={isLoading}
      isError={isError}
      error={error}>
      <Table.Thead>
        <Table.Tr>
          <Table.Th sortable sort={sort} setSort={setSort} name="id">ID Alerta</Table.Th>
          <Table.Th>Usuario</Table.Th>
          <Table.Th>Ubicación</Table.Th>
          <Table.Th sortable sort={sort} setSort={setSort} name="alert_date">Horario</Table.Th>
          <Table.Th>Estado</Table.Th>
          <Table.Th>Evidencia</Table.Th>
          <Table.Th>Comentario</Table.Th>
          <Table.Th>Historial modif.</Table.Th>
          <Table.Th sortable sort={sort} setSort={setSort} name="rating">Calificación</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {isSuccess && memoizedAlerts?.map((alert, index) => <Row alert={alert} key={`${alert.type}-${alert.id}-${index}`} />)}
      </Table.Tbody>
    </Table>
  );
});

const Row = memo(({ alert }) => {
  // Memoize computed values to prevent unnecessary recalculations
  const type = useMemo(() => {
    const alertType = alert?.type?.toLowerCase();
    const categoryName = alert?.category?.name?.toLowerCase();
    return alertType === 'sos' || categoryName === 'sos' || alert?.sender ? 'sos' : 'alert';
  }, [alert?.type, alert?.category?.name, alert?.sender]);

  // Optimized status mapping with better performance
  const status = useMemo(() => {
    const statusText = typeof alert.status === 'string' ? alert.status.toLowerCase() : '';

    // Enhanced status mapping with more comprehensive checks
    const statusMap = {
      // Backend status values (exact matches)
      'alerta enviada': { text: "Pendiente", className: "text-danger bg-danger" },
      'sos enviada': { text: "Pendiente", className: "text-danger bg-danger" },
      'ayuda enviada': { text: "Ayuda enviada", className: "text-warning bg-warning" },
      'alerta resuelta': { text: "Resuelto", className: "text-primary bg-primary" },
      'sos resuelta': { text: "Resuelto", className: "text-primary bg-primary" },

      // Legacy/alternative mappings
      'help sent': { text: "Ayuda enviada", className: "text-warning bg-warning" },
      'resuelto': { text: "Resuelto", className: "text-primary bg-primary" },
      'resolved': { text: "Resuelto", className: "text-primary bg-primary" },
      'en proceso': { text: "En Proceso", className: "text-blue-600 bg-blue-600" },
      'activo': { text: "Activo", className: "text-green-600 bg-green-600" },
      'inactivo': { text: "Inactivo", className: "text-gray-600 bg-gray-600" },
      'pendiente': { text: "Pendiente", className: "text-danger bg-danger" },
      'pending': { text: "Pendiente", className: "text-danger bg-danger" },
    };

    // Find exact match first
    if (statusMap[statusText]) {
      return statusMap[statusText];
    }

    // Find partial match
    for (const [key, value] of Object.entries(statusMap)) {
      if (statusText.includes(key)) {
        return value;
      }
    }

    // Default fallback
    return { text: "Pendiente", className: "text-danger bg-danger" };
  }, [alert.status]);

  // Memoize expensive computations
  const formattedDate = useMemo(() => {
    try {
      // Try different date sources based on alert type
      let dateToFormat = null;

      if (alert.alert_date) {
        dateToFormat = alert.alert_date;
      } else if (alert.created_at) {
        dateToFormat = alert.created_at;
      } else if (alert.alert_datetime) {
        dateToFormat = alert.alert_datetime;
      }

      if (!dateToFormat) {
        return "N/A";
      }

      // If it's already in DD/MM/YYYY format, return as is
      if (typeof dateToFormat === 'string' && /^\d{2}\/\d{2}\/\d{4}$/.test(dateToFormat)) {
        return dateToFormat;
      }

      return format(new Date(dateToFormat), 'dd/MM/yyyy');
    } catch (error) {
      console.error("Error formatting date:", error, alert);
      return "N/A";
    }
  }, [alert.alert_date, alert.created_at, alert.alert_datetime]);

  const userName = useMemo(() =>
    alert?.userprofile?.full_name || alert?.sender?.full_name || "N/A",
    [alert?.userprofile?.full_name, alert?.sender?.full_name]
  );

  const userId = useMemo(() =>
    alert?.userprofile?.user?.id || alert?.sender?.user?.id || alert?.id || "N/A",
    [alert?.userprofile?.user?.id, alert?.sender?.user?.id, alert?.id]
  );

  const location = useMemo(() => {
    const lat = alert?.lat || alert?.userprofile?.lat || alert?.sender?.lat || "N/A";
    const long = alert?.long || alert?.userprofile?.long || alert?.sender?.long || "N/A";
    return `${lat}, ${long}`;
  }, [alert?.lat, alert?.long, alert?.userprofile?.lat, alert?.userprofile?.long, alert?.sender?.lat, alert?.sender?.long]);

  return (
    <Table.Tr>
      <Table.Td>
        {type === 'alert' ? (
          <div>
            <dd className="font-semibold capitalize">{alert.category || "Alerta"}</dd>
            <dd>{alert.id || "N/A"}</dd>
          </div>
        ) : (
          <div>
            <dd className="font-semibold text-danger">SOS</dd>
            <dd>SOS#{userId}</dd>
          </div>
        )}
      </Table.Td>
      <Table.Td>
        <dd className="capitalize">{userName}</dd>
        <dd>{userId}</dd>
      </Table.Td>
      <Table.Td>{location}</Table.Td>
      <Table.Td>
        <dd>{formattedDate}</dd>
        <dd>{alert.alert_time || "N/A"}</dd>
      </Table.Td>
      <Table.Td>
        <StatusToggleBtn alert={alert} status={status} />
      </Table.Td>
      <Table.Td className="font-semibold">
        {(() => {
          const evidenceNumber = alert.evidence_number || alert.num || alert.id;

          // Always show as clickable - modal will handle missing evidence as per CA0104
          return (
            <EvidenceModalBtn alert={alert} className="hover:text-primary hover:underline">
              {evidenceNumber}
            </EvidenceModalBtn>
          );
        })()}
      </Table.Td>
      <Table.Td className="font-semibold">
        <CommentsModalBtn alert={alert} className="hover:text-primary hover:underline">
          Ver comentarios
        </CommentsModalBtn>
      </Table.Td>
      <Table.Td className="font-semibold">
        <ModificationHistoryModalBtn alert={alert} className="hover:text-primary">
          Ver historial
        </ModificationHistoryModalBtn>
      </Table.Td>
      <Table.Td>
        {alert.rating ? (
          <QualificationModalBtn alert={alert} className="group flex items-center gap-2 hover:text-primary hover:underline">
            <StarIcon className="h-6 w-6 text-warning group-hover:text-primary" />
            <span>{alert.rating}</span>
          </QualificationModalBtn>
        ) : (
          <span className="text-gray-400">-</span>
        )}
      </Table.Td>
    </Table.Tr>
  );
});

const StatusToggleBtn = ({
  as = "button",
  className = "",
  status,
  alert,
  ...props
}) => {
  const [open, setOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { axios } = useAxios();
  const queryClient = useQueryClient();

  // Determine alert type and ID
  const alertType = useMemo(() => {
    const alertTypeValue = alert?.type?.toLowerCase();
    const categoryName = alert?.category?.name?.toLowerCase();
    return alertTypeValue === 'sos' || categoryName === 'sos' || alert?.sender ? 'sos' : 'alert';
  }, [alert?.type, alert?.category?.name, alert?.sender]);

  const alertId = alert?.id;

  const statusesMapping = {
    "resolve": {
      text: "Resuelto",
      className: "text-primary bg-primary",
      value: alertType === 'sos' ? "Sos resuelta" : "Alerta resuelta"
    },
    "help_sent": {
      text: "Ayuda enviada",
      className: "text-warning bg-warning",
      value: "Ayuda enviada"
    },
    "pending": {
      text: "Pendiente",
      className: "text-danger bg-danger",
      value: alertType === 'sos' ? "Sos enviada" : "Alerta enviada"
    },
  }

  // Status change mutation
  const statusChangeMutation = useMutation(
    async (statusData) => {
      const endpoint = alertType === 'sos'
        ? '/adminside/api/alert/changesosstatus/'
        : '/adminside/api/alert/changealertstatus/';

      const response = await axios.post(endpoint, statusData);
      return response.data;
    },
    {
      onSuccess: () => {
        toast.success('Estado actualizado exitosamente');
        setOpen(false);
        setSelectedStatus(null);
        // Refetch the main alerts table data
        queryClient.invalidateQueries(['alerts-and-sos-table-data']);
      },
      onError: (error) => {
        console.error('Error changing status:', error);
        // Extract error message from different possible response structures
        const errorMessage = error.response?.data?.message ||
                            error.response?.data?.msg ||
                            error.response?.data?.error ||
                            error.message ||
                            'Error al cambiar el estado';
        toast.error(errorMessage);
      },
      onSettled: () => {
        setIsSubmitting(false);
      }
    }
  );

  const initChangeStatus = (newStatus, close) => {
    setSelectedStatus(newStatus);
    setOpen(true);
    close();
  };

  const changeStatus = () => {
    if (!selectedStatus || !alertId) {
      toast.error('Datos de alerta no válidos');
      return;
    }

    // Validate that the selected status exists in our mapping
    if (!statusesMapping[selectedStatus]) {
      toast.error('Estado seleccionado no válido');
      return;
    }

    const statusValue = statusesMapping[selectedStatus].value;
    if (!statusValue) {
      toast.error('Valor de estado no válido');
      return;
    }

    setIsSubmitting(true);
    statusChangeMutation.mutate({
      id: alertId,
      status: statusValue
    });
  };

  return (
    <>
      <ConfirmationModal
        open={open}
        close={() => setOpen(false)}
        type="warning"
        caption={`¿Estás seguro que deseas cambiar el estado a "${statusesMapping[selectedStatus]?.text || 'estado desconocido'}"?`}
        confirmBtn={{
          show: true,
          text: isSubmitting ? "Cambiando..." : "Modificar",
          className: "w-1/2",
          onClick: changeStatus,
          disabled: isSubmitting
        }}
        closeBtn={{
          show: true,
          text: "Cancelar",
          className: "w-1/2",
          onClick: () => setOpen(false),
        }}
      />
      <Popover as="div" className="relative inline-block text-left">
        {createElement(Popover.Button, {
          ...props,
          as: as,
          className: classNames(
            className,
            status?.className,
            "inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold bg-opacity-20 cursor-pointer hover:bg-opacity-30"
          ),
          children: (
            <>
              <svg
                className="mr-1.5 h-2 w-2 "
                fill="currentColor"
                viewBox="0 0 8 8"
              >
                <circle cx={5} cy={4} r={3} />
              </svg>
              {status?.text}
            </>
          ),
        })}
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Popover.Panel className="absolute left-0 z-[1] mt-2 origin-top-left rounded-md border bg-white shadow-2xl  focus:outline-none">
            {({ close }) => (
              <div className="min-w-[300px] divide-y whitespace-normal py-1 text-xs">
                <button
                  onClick={() => initChangeStatus("pending", close)}
                  className="w-full space-y-2 px-4 py-2.5 text-left hover:bg-slate-100"
                >
                  <StatusBadge
                    as="span"
                    className="bg-danger text-danger"
                    text="Pendiente"
                  />
                  <p>
                    Alerta nueva y requiere atención para enviar ayuda al
                    usuario.
                  </p>
                </button>
                <button
                  onClick={() => initChangeStatus("help_sent", close)}
                  className="w-full space-y-2 px-4 py-2.5 text-left hover:bg-slate-100"
                >
                  <StatusBadge
                    as="span"
                    className="bg-warning text-warning"
                    text="Ayuda enviada"
                  />
                  <p>
                    Se ha enviado ayuda al usuario y está en proceso de
                    resolución.
                  </p>
                </button>
                <button
                  onClick={() => initChangeStatus("resolve", close)}
                  className="w-full space-y-2 px-4 py-2.5 text-left hover:bg-slate-100"
                >
                  <StatusBadge
                    as="span"
                    className="bg-primary text-primary"
                    text="Resuelto"
                  />
                  <p>
                    La alerta ha sido completamente resuelta y cerrada.
                  </p>
                </button>
              </div>
            )}
          </Popover.Panel>
        </Transition>
      </Popover>
    </>
  );
};

const StatusBadge = ({
  as = "button",
  className = "",
  text = "",
  ...props
}) => {
  return createElement(as, {
    ...props,
    className: classNames(
      className,
      "inline-flex items-center px-3 py-1.5 rounded-full text-sm font-semibold bg-opacity-20"
    ),
    type: "button",
    children: (
      <>
        <svg className="mr-1.5 h-2 w-2 " fill="currentColor" viewBox="0 0 8 8">
          <circle cx={5} cy={4} r={3} />
        </svg>
        {text}
      </>
    ),
  });
};

export default AlertsSOSTable;
