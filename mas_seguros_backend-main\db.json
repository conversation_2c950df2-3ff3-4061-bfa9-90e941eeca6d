[{"model": "admin.logentry", "pk": 1, "fields": {"action_time": "2023-01-04T19:02:16.768Z", "user": 1, "content_type": 12, "object_id": "1", "object_repr": "<PERSON><PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 2, "fields": {"action_time": "2023-01-04T19:02:36.754Z", "user": 1, "content_type": 12, "object_id": "2", "object_repr": "Policia", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 3, "fields": {"action_time": "2023-01-04T19:02:53.688Z", "user": 1, "content_type": 12, "object_id": "3", "object_repr": "Bombero", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 4, "fields": {"action_time": "2023-01-04T19:03:21.345Z", "user": 1, "content_type": 12, "object_id": "4", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 5, "fields": {"action_time": "2023-01-04T19:03:38.127Z", "user": 1, "content_type": 12, "object_id": "5", "object_repr": "Asistencia 24/7", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 6, "fields": {"action_time": "2023-01-04T19:03:48.843Z", "user": 1, "content_type": 13, "object_id": "1", "object_repr": "Alerta enviada", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 7, "fields": {"action_time": "2023-01-04T19:03:51.595Z", "user": 1, "content_type": 13, "object_id": "2", "object_repr": "Ayuda enviada", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 8, "fields": {"action_time": "2023-01-04T19:03:55.254Z", "user": 1, "content_type": 13, "object_id": "3", "object_repr": "<PERSON><PERSON>a resuelta", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 9, "fields": {"action_time": "2023-01-04T19:34:40.825Z", "user": 1, "content_type": 14, "object_id": "8", "object_repr": "Alert-000008", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 10, "fields": {"action_time": "2023-01-04T19:34:46.775Z", "user": 1, "content_type": 14, "object_id": "7", "object_repr": "Alert-000007", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 11, "fields": {"action_time": "2023-01-04T19:34:52.758Z", "user": 1, "content_type": 14, "object_id": "3", "object_repr": "Alert-000003", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 12, "fields": {"action_time": "2023-01-04T19:34:59.188Z", "user": 1, "content_type": 14, "object_id": "1", "object_repr": "Alert-000001", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 13, "fields": {"action_time": "2023-01-05T19:11:42.978Z", "user": 1, "content_type": 4, "object_id": "3", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 14, "fields": {"action_time": "2023-01-05T19:40:47.307Z", "user": 1, "content_type": 24, "object_id": "1", "object_repr": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type a", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 15, "fields": {"action_time": "2023-01-05T19:41:04.643Z", "user": 1, "content_type": 25, "object_id": "1", "object_repr": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type a", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 16, "fields": {"action_time": "2023-01-05T21:42:06.890Z", "user": 1, "content_type": 14, "object_id": "18", "object_repr": "Alert-000009", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 17, "fields": {"action_time": "2023-01-05T21:42:12.223Z", "user": 1, "content_type": 14, "object_id": "10", "object_repr": "Alert-000001", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 18, "fields": {"action_time": "2023-01-05T21:42:17.789Z", "user": 1, "content_type": 14, "object_id": "14", "object_repr": "Alert-000005", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 19, "fields": {"action_time": "2023-01-05T21:42:25.049Z", "user": 1, "content_type": 14, "object_id": "12", "object_repr": "Alert-000003", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 20, "fields": {"action_time": "2023-01-05T21:47:35.901Z", "user": 1, "content_type": 4, "object_id": "5", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 21, "fields": {"action_time": "2023-01-05T22:02:35.563Z", "user": 1, "content_type": 4, "object_id": "6", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 22, "fields": {"action_time": "2023-01-05T22:03:27.143Z", "user": 1, "content_type": 4, "object_id": "7", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 23, "fields": {"action_time": "2023-01-05T22:04:44.061Z", "user": 1, "content_type": 4, "object_id": "8", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 24, "fields": {"action_time": "2023-01-05T22:14:42.367Z", "user": 1, "content_type": 4, "object_id": "9", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 25, "fields": {"action_time": "2023-01-05T23:31:08.885Z", "user": 1, "content_type": 14, "object_id": "23", "object_repr": "Alerta-000001", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Num\"]}}]"}}, {"model": "admin.logentry", "pk": 26, "fields": {"action_time": "2023-01-05T23:33:02.324Z", "user": 1, "content_type": 14, "object_id": "24", "object_repr": "Alert-000002", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 27, "fields": {"action_time": "2023-01-05T23:33:08.126Z", "user": 1, "content_type": 14, "object_id": "23", "object_repr": "Alerta-000001", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 28, "fields": {"action_time": "2023-01-06T00:17:30.675Z", "user": 1, "content_type": 4, "object_id": "10", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 29, "fields": {"action_time": "2023-01-06T00:17:30.679Z", "user": 1, "content_type": 4, "object_id": "11", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 30, "fields": {"action_time": "2023-01-06T02:13:22.818Z", "user": 1, "content_type": 14, "object_id": "28", "object_repr": "Alert-000003", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Status\"]}}]"}}, {"model": "admin.logentry", "pk": 31, "fields": {"action_time": "2023-01-06T02:24:34.103Z", "user": 1, "content_type": 4, "object_id": "12", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 32, "fields": {"action_time": "2023-01-06T16:49:44.578Z", "user": 1, "content_type": 4, "object_id": "13", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 33, "fields": {"action_time": "2023-01-06T17:08:28.411Z", "user": 1, "content_type": 4, "object_id": "15", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 34, "fields": {"action_time": "2023-01-06T17:37:06.845Z", "user": 1, "content_type": 4, "object_id": "16", "object_repr": "<EMAIL>", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 35, "fields": {"action_time": "2023-01-06T17:37:23.369Z", "user": 1, "content_type": 14, "object_id": "31", "object_repr": "Alert-000002", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 36, "fields": {"action_time": "2023-01-06T17:37:23.374Z", "user": 1, "content_type": 14, "object_id": "30", "object_repr": "Alert-000001", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 37, "fields": {"action_time": "2023-01-13T15:27:37.731Z", "user": 1, "content_type": 13, "object_id": "3", "object_repr": "<PERSON><PERSON>a resuelta", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 38, "fields": {"action_time": "2023-01-13T15:27:41.535Z", "user": 1, "content_type": 13, "object_id": "2", "object_repr": "Ayuda enviada", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 39, "fields": {"action_time": "2023-01-13T15:27:44.574Z", "user": 1, "content_type": 13, "object_id": "1", "object_repr": "Alerta enviada", "action_flag": 2, "change_message": "[]"}}, {"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add user", "content_type": 4, "codename": "add_user"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change user", "content_type": 4, "codename": "change_user"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete user", "content_type": 4, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view user", "content_type": 4, "codename": "view_user"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add content type", "content_type": 5, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change content type", "content_type": 5, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete content type", "content_type": 5, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view content type", "content_type": 5, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add session", "content_type": 6, "codename": "add_session"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change session", "content_type": 6, "codename": "change_session"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete session", "content_type": 6, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view session", "content_type": 6, "codename": "view_session"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add Token", "content_type": 7, "codename": "add_token"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change Token", "content_type": 7, "codename": "change_token"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete Token", "content_type": 7, "codename": "delete_token"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view Token", "content_type": 7, "codename": "view_token"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add token", "content_type": 8, "codename": "add_tokenproxy"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change token", "content_type": 8, "codename": "change_tokenproxy"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete token", "content_type": 8, "codename": "delete_tokenproxy"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view token", "content_type": 8, "codename": "view_tokenproxy"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add feature", "content_type": 9, "codename": "add_feature"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change feature", "content_type": 9, "codename": "change_feature"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete feature", "content_type": 9, "codename": "delete_feature"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view feature", "content_type": 9, "codename": "view_feature"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add user profile", "content_type": 10, "codename": "add_userprofile"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change user profile", "content_type": 10, "codename": "change_userprofile"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete user profile", "content_type": 10, "codename": "delete_userprofile"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view user profile", "content_type": 10, "codename": "view_userprofile"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add package", "content_type": 11, "codename": "add_package"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change package", "content_type": 11, "codename": "change_package"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete package", "content_type": 11, "codename": "delete_package"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view package", "content_type": 11, "codename": "view_package"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add alert categories", "content_type": 12, "codename": "add_alertcategories"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change alert categories", "content_type": 12, "codename": "change_alertcategories"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete alert categories", "content_type": 12, "codename": "delete_alertcategories"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view alert categories", "content_type": 12, "codename": "view_alertcategories"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add alert status", "content_type": 13, "codename": "add_alertstatus"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change alert status", "content_type": 13, "codename": "change_alertstatus"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete alert status", "content_type": 13, "codename": "delete_alertstatus"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view alert status", "content_type": 13, "codename": "view_alertstatus"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add alert model", "content_type": 14, "codename": "add_alertmodel"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change alert model", "content_type": 14, "codename": "change_alertmodel"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete alert model", "content_type": 14, "codename": "delete_alertmodel"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view alert model", "content_type": 14, "codename": "view_alertmodel"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add promo_code", "content_type": 15, "codename": "add_promo_code"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change promo_code", "content_type": 15, "codename": "change_promo_code"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete promo_code", "content_type": 15, "codename": "delete_promo_code"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view promo_code", "content_type": 15, "codename": "view_promo_code"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add membership model", "content_type": 16, "codename": "add_membershipmodel"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change membership model", "content_type": 16, "codename": "change_membershipmodel"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete membership model", "content_type": 16, "codename": "delete_membershipmodel"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view membership model", "content_type": 16, "codename": "view_membershipmodel"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add location", "content_type": 17, "codename": "add_location"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change location", "content_type": 17, "codename": "change_location"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete location", "content_type": 17, "codename": "delete_location"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view location", "content_type": 17, "codename": "view_location"}}, {"model": "auth.permission", "pk": 69, "fields": {"name": "Can add points of interest", "content_type": 18, "codename": "add_pointsofinterest"}}, {"model": "auth.permission", "pk": 70, "fields": {"name": "Can change points of interest", "content_type": 18, "codename": "change_pointsofinterest"}}, {"model": "auth.permission", "pk": 71, "fields": {"name": "Can delete points of interest", "content_type": 18, "codename": "delete_pointsofinterest"}}, {"model": "auth.permission", "pk": 72, "fields": {"name": "Can view points of interest", "content_type": 18, "codename": "view_pointsofinterest"}}, {"model": "auth.permission", "pk": 73, "fields": {"name": "Can add route", "content_type": 19, "codename": "add_route"}}, {"model": "auth.permission", "pk": 74, "fields": {"name": "Can change route", "content_type": 19, "codename": "change_route"}}, {"model": "auth.permission", "pk": 75, "fields": {"name": "Can delete route", "content_type": 19, "codename": "delete_route"}}, {"model": "auth.permission", "pk": 76, "fields": {"name": "Can view route", "content_type": 19, "codename": "view_route"}}, {"model": "auth.permission", "pk": 77, "fields": {"name": "Can add shield model", "content_type": 20, "codename": "add_shieldmodel"}}, {"model": "auth.permission", "pk": 78, "fields": {"name": "Can change shield model", "content_type": 20, "codename": "change_shieldmodel"}}, {"model": "auth.permission", "pk": 79, "fields": {"name": "Can delete shield model", "content_type": 20, "codename": "delete_shieldmodel"}}, {"model": "auth.permission", "pk": 80, "fields": {"name": "Can view shield model", "content_type": 20, "codename": "view_shieldmodel"}}, {"model": "auth.permission", "pk": 81, "fields": {"name": "Can add biometric", "content_type": 21, "codename": "add_biometric"}}, {"model": "auth.permission", "pk": 82, "fields": {"name": "Can change biometric", "content_type": 21, "codename": "change_biometric"}}, {"model": "auth.permission", "pk": 83, "fields": {"name": "Can delete biometric", "content_type": 21, "codename": "delete_biometric"}}, {"model": "auth.permission", "pk": 84, "fields": {"name": "Can view biometric", "content_type": 21, "codename": "view_biometric"}}, {"model": "auth.permission", "pk": 85, "fields": {"name": "Can add faq category", "content_type": 22, "codename": "add_faqcategory"}}, {"model": "auth.permission", "pk": 86, "fields": {"name": "Can change faq category", "content_type": 22, "codename": "change_faqcategory"}}, {"model": "auth.permission", "pk": 87, "fields": {"name": "Can delete faq category", "content_type": 22, "codename": "delete_faqcategory"}}, {"model": "auth.permission", "pk": 88, "fields": {"name": "Can view faq category", "content_type": 22, "codename": "view_faqcategory"}}, {"model": "auth.permission", "pk": 89, "fields": {"name": "Can add faq question", "content_type": 23, "codename": "add_faqquestion"}}, {"model": "auth.permission", "pk": 90, "fields": {"name": "Can change faq question", "content_type": 23, "codename": "change_faqquestion"}}, {"model": "auth.permission", "pk": 91, "fields": {"name": "Can delete faq question", "content_type": 23, "codename": "delete_faqquestion"}}, {"model": "auth.permission", "pk": 92, "fields": {"name": "Can view faq question", "content_type": 23, "codename": "view_faqquestion"}}, {"model": "auth.permission", "pk": 93, "fields": {"name": "Can add data policie", "content_type": 24, "codename": "add_datapolicie"}}, {"model": "auth.permission", "pk": 94, "fields": {"name": "Can change data policie", "content_type": 24, "codename": "change_datapolicie"}}, {"model": "auth.permission", "pk": 95, "fields": {"name": "Can delete data policie", "content_type": 24, "codename": "delete_datapolicie"}}, {"model": "auth.permission", "pk": 96, "fields": {"name": "Can view data policie", "content_type": 24, "codename": "view_datapolicie"}}, {"model": "auth.permission", "pk": 97, "fields": {"name": "Can add terms and condition", "content_type": 25, "codename": "add_termsandcondition"}}, {"model": "auth.permission", "pk": 98, "fields": {"name": "Can change terms and condition", "content_type": 25, "codename": "change_termsandcondition"}}, {"model": "auth.permission", "pk": 99, "fields": {"name": "Can delete terms and condition", "content_type": 25, "codename": "delete_termsandcondition"}}, {"model": "auth.permission", "pk": 100, "fields": {"name": "Can view terms and condition", "content_type": 25, "codename": "view_termsandcondition"}}, {"model": "auth.permission", "pk": 101, "fields": {"name": "Can add ticket subject", "content_type": 26, "codename": "add_ticketsubject"}}, {"model": "auth.permission", "pk": 102, "fields": {"name": "Can change ticket subject", "content_type": 26, "codename": "change_ticketsubject"}}, {"model": "auth.permission", "pk": 103, "fields": {"name": "Can delete ticket subject", "content_type": 26, "codename": "delete_ticketsubject"}}, {"model": "auth.permission", "pk": 104, "fields": {"name": "Can view ticket subject", "content_type": 26, "codename": "view_ticketsubject"}}, {"model": "auth.permission", "pk": 105, "fields": {"name": "Can add ticket", "content_type": 27, "codename": "add_ticket"}}, {"model": "auth.permission", "pk": 106, "fields": {"name": "Can change ticket", "content_type": 27, "codename": "change_ticket"}}, {"model": "auth.permission", "pk": 107, "fields": {"name": "Can delete ticket", "content_type": 27, "codename": "delete_ticket"}}, {"model": "auth.permission", "pk": 108, "fields": {"name": "Can view ticket", "content_type": 27, "codename": "view_ticket"}}, {"model": "auth.permission", "pk": 109, "fields": {"name": "Can add super admin", "content_type": 28, "codename": "add_superadmin"}}, {"model": "auth.permission", "pk": 110, "fields": {"name": "Can change super admin", "content_type": 28, "codename": "change_superadmin"}}, {"model": "auth.permission", "pk": 111, "fields": {"name": "Can delete super admin", "content_type": 28, "codename": "delete_superadmin"}}, {"model": "auth.permission", "pk": 112, "fields": {"name": "Can view super admin", "content_type": 28, "codename": "view_superadmin"}}, {"model": "auth.permission", "pk": 113, "fields": {"name": "Can add company profile model", "content_type": 29, "codename": "add_companyprofilemodel"}}, {"model": "auth.permission", "pk": 114, "fields": {"name": "Can change company profile model", "content_type": 29, "codename": "change_companyprofilemodel"}}, {"model": "auth.permission", "pk": 115, "fields": {"name": "Can delete company profile model", "content_type": 29, "codename": "delete_companyprofilemodel"}}, {"model": "auth.permission", "pk": 116, "fields": {"name": "Can view company profile model", "content_type": 29, "codename": "view_companyprofilemodel"}}, {"model": "auth.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$390000$MeUkxowzKCxeg6AzOJIDZO$39qBMakF3NWKmuff2qfsxBtD9+fEBkUzhsSg5MLP/ak=", "last_login": "2023-01-13T15:27:31.681Z", "is_superuser": true, "username": "maan", "first_name": "", "last_name": "", "email": "", "is_staff": true, "is_active": true, "date_joined": "2023-01-04T18:54:53.218Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 14, "fields": {"password": "pbkdf2_sha256$390000$8LVqYgqcgr5PCUgklI1dlF$BnXFTTt+GHIPkwKkPiqtGgkSeINF5gcKdMTV9iU5e9E=", "last_login": null, "is_superuser": false, "username": "<EMAIL>", "first_name": "<PERSON>", "last_name": "valverde", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2023-01-06T16:40:56.907Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 15, "fields": {"password": "pbkdf2_sha256$390000$M8Eg3PpRBttUwt5Jofd2t9$uTinDIH0Uj2vUpCNJbHjie9F7dB06glO+ijmXOXAYnY=", "last_login": null, "is_superuser": false, "username": "<EMAIL>", "first_name": "muh<PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2023-01-13T15:28:27.911Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 16, "fields": {"password": "pbkdf2_sha256$390000$MAujh3JjmufWwsJRYRU0ac$RyntU0g8dyYliBInkl5xy06BBNlIVJxwWxyA8rbWvW8=", "last_login": null, "is_superuser": false, "username": "<EMAIL>", "first_name": "muh<PERSON><PERSON>", "last_name": "subial", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2023-01-13T15:29:39.383Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 17, "fields": {"password": "pbkdf2_sha256$390000$UonHCcHjMBCuhK3uyZkAed$LehJoVewpspsAJcdBuaxaY3V/JFx0lObzDe+laXd0II=", "last_login": null, "is_superuser": false, "username": "<EMAIL>", "first_name": "abdul", "last_name": "mannan", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2023-01-13T15:31:16.227Z", "groups": [], "user_permissions": []}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "auth", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "authtoken", "model": "token"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "authtoken", "model": "tokenproxy"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "Account", "model": "feature"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "Account", "model": "userprofile"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "Account", "model": "package"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "<PERSON><PERSON>", "model": "alertcategories"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "<PERSON><PERSON>", "model": "alertstatus"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "<PERSON><PERSON>", "model": "alertmodel"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "AdminAPi", "model": "promo_code"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "Membership", "model": "membershipmodel"}}, {"model": "contenttypes.contenttype", "pk": 17, "fields": {"app_label": "Shield", "model": "location"}}, {"model": "contenttypes.contenttype", "pk": 18, "fields": {"app_label": "Shield", "model": "pointsofinterest"}}, {"model": "contenttypes.contenttype", "pk": 19, "fields": {"app_label": "Shield", "model": "route"}}, {"model": "contenttypes.contenttype", "pk": 20, "fields": {"app_label": "Shield", "model": "shieldmodel"}}, {"model": "contenttypes.contenttype", "pk": 21, "fields": {"app_label": "Shield", "model": "biometric"}}, {"model": "contenttypes.contenttype", "pk": 22, "fields": {"app_label": "Faq", "model": "faqcategory"}}, {"model": "contenttypes.contenttype", "pk": 23, "fields": {"app_label": "Faq", "model": "faqquestion"}}, {"model": "contenttypes.contenttype", "pk": 24, "fields": {"app_label": "About", "model": "datapolicie"}}, {"model": "contenttypes.contenttype", "pk": 25, "fields": {"app_label": "About", "model": "termsandcondition"}}, {"model": "contenttypes.contenttype", "pk": 26, "fields": {"app_label": "Ticket", "model": "ticketsubject"}}, {"model": "contenttypes.contenttype", "pk": 27, "fields": {"app_label": "Ticket", "model": "ticket"}}, {"model": "contenttypes.contenttype", "pk": 28, "fields": {"app_label": "CompanyDashboard", "model": "superadmin"}}, {"model": "contenttypes.contenttype", "pk": 29, "fields": {"app_label": "CompanyDashboard", "model": "companyprofilemodel"}}, {"model": "sessions.session", "pk": "6dxub8rw79zj3ohqw8nhh5ez7n6gugfv", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSFQhkdduvcbyDAMUjU0Ke3K-O_apAvd3nPOfYmI21rj1nmJUxZnocXpd0tID247yHdst1nS3NZlSnJX5EG7vM6Zn5fD_Tuo2Ou3LkE7y96NRRVGAA-KAoMyevS-OGIVyOlkTYaEbJNHYxiZ9EBuAFDi_QHc_TfP:1pDVeB:iUVN4ed_Ju5KtsVIUOMgGRVqg3MMcN7fgd8aGA339LI", "expire_date": "2023-01-19T19:11:27.149Z"}}, {"model": "sessions.session", "pk": "7b2g9qh3nqa4wnh7wfpyd3n7q07df8u9", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSFQhkdduvcbyDAMUjU0Ke3K-O_apAvd3nPOfYmI21rj1nmJUxZnocXpd0tID247yHdst1nS3NZlSnJX5EG7vM6Zn5fD_Tuo2Ou3LkE7y96NRRVGAA-KAoMyevS-OGIVyOlkTYaEbJNHYxiZ9EBuAFDi_QHc_TfP:1pGLxr:neZT6J7_yAwj4yXRwaBtiikA9L49O20vd_xjyv0ic1w", "expire_date": "2023-01-27T15:27:31.684Z"}}, {"model": "sessions.session", "pk": "rtuz1f1fleyxxyxv1lj75tktq68je1fs", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSFQhkdduvcbyDAMUjU0Ke3K-O_apAvd3nPOfYmI21rj1nmJUxZnocXpd0tID247yHdst1nS3NZlSnJX5EG7vM6Zn5fD_Tuo2Ou3LkE7y96NRRVGAA-KAoMyevS-OGIVyOlkTYaEbJNHYxiZ9EBuAFDi_QHc_Tf,P:1pD8v1:ceVL-UQEwKdlSN2ajVIkgcN74cptfoonxqbg9OqoTuU", "expire_date": "2023-01-18T18:55:19.259Z"}}, {"model": "authtoken.token", "pk": "01ee9d94153ec1ec96ac21f649d391f90d978753", "fields": {"user": 17, "created": "2023-01-13T15:31:16.729Z"}}, {"model": "authtoken.token", "pk": "34205c573e634f6897872de2df6658347073b7f3", "fields": {"user": 15, "created": "2023-01-13T15:28:28.352Z"}}, {"model": "authtoken.token", "pk": "46422818eddd1a4428b6626321e32e9f5926caff", "fields": {"user": 14, "created": "2023-01-06T16:40:57.185Z"}}, {"model": "authtoken.token", "pk": "c6891da71a5a31ca94ed5a8879fcd73a602275c3", "fields": {"user": 16, "created": "2023-01-13T15:29:39.979Z"}}, {"model": "Account.userprofile", "pk": 13, "fields": {"user": 14, "firebase_uid": null, "ui_id": "UI000002", "phone": "+************", "full_name": "<PERSON>", "identification_card": "**********", "birth_date": "1998-01-14", "role": "User", "lat": null, "long": null, "verification_code": 1657, "email_verified": true, "enable_location": false, "image": "", "user_type": "Individual", "suspend": false, "created_at": "2023-01-06T16:40:57.176Z", "updated_at": "2023-01-06T17:33:11.799Z", "Hierarchy": ""}}, {"model": "Account.userprofile", "pk": 14, "fields": {"user": 15, "firebase_uid": null, "ui_id": "UI000003", "phone": "+************", "full_name": "muh<PERSON><PERSON>", "identification_card": "12345-12345-1243", "birth_date": "2023-01-09", "role": "User", "lat": null, "long": null, "verification_code": 6334, "email_verified": true, "enable_location": false, "image": "", "user_type": "Individual", "suspend": false, "created_at": "2023-01-13T15:28:28.343Z", "updated_at": "2023-01-13T15:28:28.346Z", "Hierarchy": ""}}, {"model": "Account.userprofile", "pk": 15, "fields": {"user": 16, "firebase_uid": null, "ui_id": "UI000004", "phone": "+************", "full_name": "muh<PERSON><PERSON><PERSON>", "identification_card": "12345-12455-1243", "birth_date": "2023-01-09", "role": "User", "lat": null, "long": null, "verification_code": 5471, "email_verified": true, "enable_location": false, "image": "", "user_type": "Individual", "suspend": false, "created_at": "2023-01-13T15:29:39.964Z", "updated_at": "2023-01-13T15:29:39.966Z", "Hierarchy": ""}}, {"model": "Account.userprofile", "pk": 16, "fields": {"user": 17, "firebase_uid": null, "ui_id": "UI000005", "phone": "+************", "full_name": "abdul mannan", "identification_card": "12345-12455-1240", "birth_date": "2023-01-09", "role": "User", "lat": null, "long": null, "verification_code": 3829, "email_verified": true, "enable_location": false, "image": "", "user_type": "Individual", "suspend": false, "created_at": "2023-01-13T15:31:16.724Z", "updated_at": "2023-01-13T15:31:16.725Z", "Hierarchy": ""}}, {"model": "Alert.alertcategories", "pk": 1, "fields": {"name": "<PERSON><PERSON>", "image": "alert_categories/WhatsApp_Image_2023-01-02_at_10.39.52_PM_1_ZdBq9K3.jpeg"}}, {"model": "Alert.alertcategories", "pk": 2, "fields": {"name": "Policia", "image": "alert_categories/WhatsApp_Image_2023-01-02_at_10.39.52_PM_3_MANIu8B.jpeg"}}, {"model": "Alert.alertcategories", "pk": 3, "fields": {"name": "Bombero", "image": "alert_categories/WhatsApp_Image_2023-01-02_at_10.39.52_PM_2_4Tif8Je.jpeg"}}, {"model": "Alert.alertcategories", "pk": 4, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": "alert_categories/WhatsApp_Image_2023-01-02_at_10.39.52_PM_bZV97Yk.jpeg"}}, {"model": "Alert.alertcategories", "pk": 5, "fields": {"name": "Asistencia 24/7", "image": "alert_categories/WhatsApp_Image_2023-01-02_at_10.39.52_PM_4_RyLZGbS.jpeg"}}, {"model": "<PERSON><PERSON><PERSON>alertstatus", "pk": 1, "fields": {"name": "Alerta enviada"}}, {"model": "<PERSON><PERSON><PERSON>alertstatus", "pk": 2, "fields": {"name": "Ayuda enviada"}}, {"model": "<PERSON><PERSON><PERSON>alertstatus", "pk": 3, "fields": {"name": "<PERSON><PERSON>a resuelta"}}, {"model": "About.datapolicie", "pk": 1, "fields": {"title": "What is <PERSON><PERSON>?", "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.", "created_at": "2023-01-05T19:40:47.306Z", "updated_at": "2023-01-05T19:40:47.306Z"}}, {"model": "About.termsandcondition", "pk": 1, "fields": {"title": "What is <PERSON><PERSON>?", "description": "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.", "created_at": "2023-01-05T19:41:04.642Z", "updated_at": "2023-01-05T19:41:04.642Z"}}]