from django.urls import path
from . import views as admin_views
from Account import views as account_views

# router = routers.DefaultRouter()
# router.register('promo', admin_url.promo_codes)
urlpatterns = [
    path("promo-code/<int:pk>/", admin_views.promo_code_detail, name="promo_code_detail"),  # patch and delete
    path('adminregister/', admin_views.AdminRegisterApi.as_view(), name='admin-register'),
    path('adminlogin/', admin_views.AdminLoginApi.as_view(), name='admin-login'),
    path('admin-password-recovery/', admin_views.AdminPasswordRecovery.as_view(), name='admin-password-recovery'),
    path("promo-code/", admin_views.promo_code, name="promo_code"),  # get and post
    path("all-admin/", admin_views.GetadminUsers.as_view()),
    path('all-promocodes/', admin_views.AllCompanyPromoCodes.as_view()),
    path('change_password/', admin_views.ChangePassword.as_view(), name='auth_change_password'),
    path('fcm-device-token/', account_views.FcmDeviceToken.as_view(), name='fcm-device-token'),
]
