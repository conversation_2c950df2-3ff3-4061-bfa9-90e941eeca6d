# Generated by Django 5.2.3 on 2025-06-14 11:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, null=True)),
                ('is_included', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, null=True)),
                ('price', models.FloatField(default=0.0, null=True)),
                ('short_description', models.TextField(help_text='You can add html as well', max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('features', models.ManyToManyField(related_name='package_features', to='Account.feature')),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('firebase_uid', models.CharField(blank=True, max_length=100, null=True)),
                ('ui_id', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('full_name', models.CharField(blank=True, max_length=60, null=True)),
                ('identification_card', models.CharField(blank=True, max_length=60, null=True)),
                ('birth_date', models.DateField(blank=True, null=True)),
                ('role', models.CharField(blank=True, choices=[('User', 'User'), ('Admin', 'Admin')], default='User', max_length=30, null=True)),
                ('lat', models.CharField(blank=True, max_length=40, null=True)),
                ('long', models.CharField(blank=True, max_length=40, null=True)),
                ('verification_code', models.IntegerField(blank=True, null=True)),
                ('email_verified', models.BooleanField(blank=True, default=False, null=True)),
                ('enable_location', models.BooleanField(default=False)),
                ('image', models.ImageField(blank=True, null=True, upload_to='profile_images')),
                ('user_type', models.CharField(choices=[('Individual', 'Individual'), ('Corporate', 'Corporate')], default='Individual', max_length=100, null=True)),
                ('suspend', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='FcmDeviceRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.CharField(blank=True, max_length=1000, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
    ]
