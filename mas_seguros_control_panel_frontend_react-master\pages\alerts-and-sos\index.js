import React from "react";
import Admin from "@/components/layouts/Admin";
import SamplePagination from "@/components/SamplePagination";
import AlertsSOSTable from "@/components/alerts-and-sos/AlertsSOSTable";
import FilterDropDownBtn from "@/components/utility/FilterDropDownBtn";
import InputGroup from "@/components/utility/InputGroup";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import useTableData from "@/hooks/useTableData";
import Pagination from "@/components/Pagination";

const pageSize = 10;

export default function AlertsAndSOS() {
  const {
    search,
    setSearch,
    currentTableData,
    tempFilters,
    setTempFilters,
    applyFilters,
    isLoading,
    isError,
    error,
    sort,
    setSort,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess,
    resetPage
  } = useTableData({
    baseURL: process.env.NEXT_PUBLIC_BACKEND_URL_2,
    dataUrl: "/adminside/api/alert/alertall/",
    pageSize: pageSize,
    noAuth: false,
    queryKeys: ["alerts-and-sos-table-data"],
    dataCallback: (response) => {
      if (!response?.data?.data) return [];

      // Use the optimized combined data if available, otherwise fallback to separate arrays
      const combinedData = response.data.data.combined;
      if (combinedData && Array.isArray(combinedData)) {
        return combinedData.map(item => ({
          ...item,
          // Ensure consistent data structure
          userprofile: item.userprofile || item.sender || {},
          evidence_number: item.evidence_number || (item.type === 'sos' ? `S${item.id}` : `A${item.id}`),
          category: item.category || (item.type === 'sos' ? 'SOS' : 'Alert'),
          status: item.status || item.status_name || "",
        }));
      }

      // Fallback to original transformation logic
      const alerts = response.data.data.alerts || [];
      const sos = response.data.data.sos || [];

      // Transform alerts data with optimized structure
      const transformedAlerts = alerts.map(alertItem => ({
        ...alertItem,
        type: "Alert",
        category: alertItem.category || "",
        status: alertItem.status_name || "",
        alert_date: alertItem.alert_date || alertItem.alert_datetime?.split('T')[0] || new Date().toISOString().split('T')[0],
        alert_time: alertItem.alert_time || alertItem.alert_datetime?.split('T')[1]?.split('.')[0] || new Date().toTimeString().split(' ')[0],
        evidence_url: alertItem.evidence_url || null,
        evidence_number: alertItem.evidence_number || `A${alertItem.id}`,
        rating: alertItem.rating || null,
        rating_description: alertItem.rating_description || "",
        description: alertItem.description || "No hay comentarios disponibles",
        userprofile: {
          ...alertItem.userprofile,
          full_name: alertItem.userprofile?.full_name || "Usuario",
          user: alertItem.userprofile?.user || { id: alertItem.id }
        },
        lat: alertItem.lat || alertItem.userprofile?.lat,
        long: alertItem.long || alertItem.userprofile?.long
      }));

      // Transform SOS data with optimized structure
      const transformedSos = sos.map(sosItem => ({
        ...sosItem,
        type: "SOS",
        category: 'SOS',
        status: sosItem.status || sosItem.status_name || "",
        alert_date: sosItem.alert_date || sosItem.alert_datetime?.split('T')[0] || new Date().toISOString().split('T')[0],
        alert_time: sosItem.alert_time || sosItem.alert_datetime?.split('T')[1]?.split('.')[0] || new Date().toTimeString().split(' ')[0],
        userprofile: sosItem.sender || {},
        evidence_number: `S${sosItem.id || Math.random().toString(36).substring(2, 9)}`,
        evidence_url: sosItem.evidence_url || sosItem.evidence || null,
        rating: sosItem.rating || null,
        rating_description: sosItem.rating_description || "",
        lat: sosItem.lat || sosItem.sender?.lat,
        long: sosItem.long || sosItem.sender?.long,
        description: sosItem.description || "No hay comentarios disponibles para este SOS"
      }));

      // Combine and sort by created_at for better performance
      const combined = [...transformedSos, ...transformedAlerts];
      return combined.sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0));
    }
  });

  return (
    <Admin pageTitle="Alertas y SOS" headerTitle="Alertas y SOS">
      <div className="bg-neutral">
        <div className="container-padding items-center gap-3 space-y-2 py-2.5 lg:flex lg:space-y-0">
          <div className="w-full flex-shrink-0 sm:w-auto">
            <InputGroup className=" relative">
              <div className="absolute inset-y-0 left-0 flex w-9 items-center justify-center p-1 px-1.5 pl-3 text-secondary">
                <MagnifyingGlassIcon className="aspect-square w-full" />
              </div>
              <InputGroup.Input
                value={search}
                onChange={e => {
                  // Ensure we're setting the raw value without any special handling
                  // The trimming will be handled in the useTableData hook
                  setSearch(e.target.value);
                  resetPage();
                }}
                id="search"
                type="search"
                name="search"
                className="pl-9"
                placeholder="Buscar"
              />
            </InputGroup>
          </div>

          <div className="flex flex-grow items-center gap-3">
            <FilterDropDownBtn.Primary
              filters={tempFilters}
              setFilters={setTempFilters}
              onApply={applyFilters}
              groups={[
                {
                  id: 1,
                  title: "Type of Alert",
                  options: [
                    {
                      id: 1,
                      label: "SOS",
                      name: "category",
                      value: "sos",
                    },
                    {
                      id: 2,
                      label: "Alert",
                      name: "category",
                      value: "alert",
                    },
                    {
                      id: 3,
                      label: "Alert - Police",
                      name: "category",
                      value: "alert-police",
                    },
                  ],
                },
                {
                  id: 2,
                  title: "Status",
                  options: [
                    {
                      id: 1,
                      label: "Pendiente",
                      name: "status",
                      value: "pendiente",
                    },
                    {
                      id: 2,
                      label: "Ayuda enviada",
                      name: "status",
                      value: "ayuda enviada",
                    },
                    {
                      id: 3,
                      label: "Resuelto",
                      name: "status",
                      value: "resuelto",
                    },
                  ],
                },
              ]}
            />
          </div>
        </div>
      </div>

      <div className="container-padding">
        <AlertsSOSTable
          alerts={currentTableData}
          isSuccess={isSuccess}
          isLoading={isLoading}
          isError={isError}
          error={error}
          sort={sort}
          setSort={setSort}
        />
        {isSuccess && allData.length > 0 && (
          <div className="mt-3.5">
            <Pagination
              totalCount={allData.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </Admin>
  );
}
