from django.shortcuts import HttpResponse
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from django.http import JsonResponse
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from . import models as company_models, serializers as company_serializer
from Membership import models as membership_models
from AdminSide.AdminAPi import models as admin_models


# Create your views here.


class AllCompanies(APIView):
    # permission_classes = (IsAuthenticated,)

    def get(self, request):
        all_companies = company_models.CompanyProfileModel.objects.all()
        serializer = company_serializer.CompanySerializer(all_companies, many=True)
        return JsonResponse(serializer.data, safe=False)
        # backend_utils.success_response(status_code=status.HTTP_200_OK,
        #                                data=serializer.data,
        #                                msg='All companies'), status.HTTP_200_OK)


class CompanyShields(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = company_serializer.GetCompanyIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        company = company_models.CompanyProfileModel.objects.filter(id=id).last()
        if company:
            company: company_models.CompanyProfileModel
            all_shields = company.shields.all()
            serializer = company_serializer.ShieldsSerializer(all_shields, many=True)
            return JsonResponse(serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=serializer.data,
            #                                msg='All companies'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Company Found'), status.HTTP_400_BAD_REQUEST)


class CompanyMembers(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = company_serializer.GetCompanyIDSerializer
    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        company = company_models.CompanyProfileModel.objects.filter(id=id).last()
        if company:
            company: company_models.CompanyProfileModel
            all_members = company.users.all()
            serializer = company_serializer.CompanyMemberSerializer(all_members, many=True)
            # return JsonResponse(serializer.data, safe=False)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='All Members'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Company Found'), status.HTTP_400_BAD_REQUEST)




class CompanyPromoCodes(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = company_serializer.GetCompanyIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        company = company_models.CompanyProfileModel.objects.filter(id=id).last()
        if company:
            company: company_models.CompanyProfileModel
            all_promo_codes = admin_models.PromoCode.objects.filter(company=company)
            serializer = company_serializer.PromoCodeSerializer(all_promo_codes, many=True)
            # return JsonResponse(serializer.data, safe=False)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='All Promo Codes'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Company Found'), status.HTTP_400_BAD_REQUEST)


class membershipCompany(APIView):
    serializer_class = company_serializer.GetCompanyIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        company = company_models.CompanyProfileModel.objects.filter(id=id).last()
        if company:
            company: company_models.CompanyProfileModel
            membership = membership_models.MembershipModel.objects.filter(companyprofile=company)
            print("these are the membners====", membership)
            serializer = company_serializer.MembershipSerializer(membership, many=True)
            return JsonResponse(
                {'status_code': status.HTTP_200_OK, 'data': serializer.data, 'msg': 'Company All Memberships'},
                safe=False)

        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Company Found'), status.HTTP_400_BAD_REQUEST)


# get signle company.
@csrf_exempt
def update_company(request, pk):
    try:
        photographer_id = company_models.CompanyProfileModel.objects.get(pk=pk)
    except photographer_id.DoesNotExist:
        return HttpResponse(status=404)
    if request.method == "PATCH":
        data = JSONParser().parse(request)
        serializer = company_serializer.CompanySerializer(photographer_id, data=data)
        if serializer.is_valid():
            serializer.save()
            return JsonResponse(serializer.data)
        return JsonResponse(serializer.errors, status=400)


class company_get_single(APIView):
    def get(self, request, id=None):
        ...

        if id:
            item = company_models.CompanyProfileModel.objects.get(id=id)
            serializer = company_serializer.CompanySerializer(item)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)
