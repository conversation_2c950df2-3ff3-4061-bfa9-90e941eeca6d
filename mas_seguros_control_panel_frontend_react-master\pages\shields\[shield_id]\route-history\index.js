import React, { useState, useEffect } from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import Table from "@/components/Table";
import SectionHeading from "@/components/SectionHeading";
import InputGroup from "@/components/utility/InputGroup";
import { ArrowDownTrayIcon, DocumentTextIcon } from "@heroicons/react/24/outline";
import DownloadRoutesBtn from "@/components/shields/shield/DownloadRoutesBtn";
import RouteDetailsModalBtn from "@/components/shields/shield/RouteDetailsModalBtn";
import { useRouter } from "next/router";
import useAxios from "@/hooks/useAxios";
import { useQuery, useQueryClient } from "react-query";
import Badge from "@/components/Badge";
import { format, isValid, parse } from "date-fns";
import { toast } from "react-hot-toast";

export default function index() {
  const { axios } = useAxios()
  const router = useRouter();
  const queryClient = useQueryClient();
  const [memberId, setMemberId] = useState('')
  const [searchDate, setSearchDate] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [appliedSearchDate, setAppliedSearchDate] = useState('')

  const { shield_id } = router.query;

  const fetchData = () => {
    return axios.get(`adminside/api/shield/shield-members/?id=${shield_id}`);
  }

  // React-query for data fetching
  const { isLoading, isError, refetch, isRefetching, isSuccess, data: response, error } = useQuery(
    `shield-${shield_id}-members`,
    fetchData,
    {
      refetchOnWindowFocus: false,
      enabled: !!shield_id
    }
  );

  const members = Array.isArray(response?.data?.data) ? response?.data?.data : []

  // Handle search functionality
  const handleDateSearch = () => {
    if (!searchDate) {
      toast.error('Por favor seleccione una fecha');
      return;
    }

    // Validate date format
    const parsedDate = parse(searchDate, 'yyyy-MM-dd', new Date());
    if (!isValid(parsedDate)) {
      toast.error('Formato de fecha inválido');
      return;
    }

    setIsSearching(true);
    try {
      // Apply the search date which will trigger RouteHistoryTable to refetch
      setAppliedSearchDate(searchDate);

      // Invalidate the routes query to force a refetch with new search parameters
      if (memberId) {
        queryClient.invalidateQueries(`shield-${shield_id}-member-${memberId}-routes`);
      }

      toast.success('Búsqueda aplicada correctamente');
    } catch (error) {
      console.error('Error searching by date:', error);
      toast.error('Error al aplicar la búsqueda');
    } finally {
      setIsSearching(false);
    }
  };

  const clearSearch = () => {
    setSearchDate('');
    setAppliedSearchDate('');

    // Invalidate the routes query to refresh data without search filter
    if (memberId) {
      queryClient.invalidateQueries(`shield-${shield_id}-member-${memberId}-routes`);
    }

    toast.success('Filtros limpiados');
  };

  // Reset search when member changes
  useEffect(() => {
    if (memberId) {
      setSearchDate('');
      setAppliedSearchDate('');
    }
  }, [memberId]);

  // Auto-select first member when members are loaded
  useEffect(() => {
    if (members.length > 0 && !memberId) {
      const firstMember = members[0].member || members[0];
      setMemberId(firstMember?.user?.id);
    }
  }, [members, memberId]);

  return (
    <ShieldLayout pageTitle="Escudos" headerTitle="Escudos">
      <div className="mt-4 flex flex-col gap-5 xl:flex-row">
        <div className="h-[800px] w-full xl:max-w-md">
          <Table wrapperClassName="h-full no-scrollbar" className="relative">
            <Table.Thead className="sticky top-0 bg-accent">
              <Table.Tr>
                <Table.Th className="w-1/2 pl-5">Nombre</Table.Th>
                <Table.Th className="w-1/2 pr-5">Jerarquía</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {isLoading ? (
                <Table.Tr>
                  <Table.Td colSpan={2} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <span className="ml-2">Cargando...</span>
                    </div>
                  </Table.Td>
                </Table.Tr>
              ) : members.length > 0 ? (
                members.map((memberData) => {
                  const member = memberData.member || memberData;
                  return (
                  <Table.Tr key={member?.user?.id}>
                    <Table.Td className="pl-5">
                      <div className="flex gap-3">
                        <dl>
                          <dd className="capitalize">{member.full_name}</dd>
                          <dd>ID-{member?.user?.id}</dd>
                        </dl>
                      </div>
                    </Table.Td>
                    <Table.Td className="pr-5 flex items-center justify-between gap-4">
                      <Badge.Md text={memberData.hierarchy || member.hierarchy || "Miembro"} className="bg-warning bg-opacity-20 text-warning" />
                      <label>
                        <input checked={memberId == member?.user?.id} onChange={() => setMemberId(member?.user?.id)} type="radio" name="radio" className="h-4 w-4" />
                      </label>
                    </Table.Td>
                  </Table.Tr>
                  );
                })
              ) : (
                <Table.Tr>
                  <Table.Td colSpan={2} className="text-center py-8">
                    No se encontraron resultados
                  </Table.Td>
                </Table.Tr>
              )}
            </Table.Tbody>
          </Table>
        </div>

        <div className="flex h-[800px] flex-grow flex-col space-y-3 bg-white p-5">
          <SectionHeading>Historial de Rutas</SectionHeading>
          <div className="flex items-center justify-end gap-4">
            <div className="flex items-center justify-end gap-2 text-sm">
              <span>Buscar</span>
              <div>
                <InputGroup>
                  <InputGroup.Input
                    type="date"
                    className="bg-accent"
                    value={searchDate}
                    onChange={(e) => setSearchDate(e.target.value)}
                  />
                </InputGroup>
              </div>
              <button
                className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2 disabled:opacity-50"
                onClick={handleDateSearch}
                disabled={isSearching || !searchDate}
              >
                {isSearching ? 'Buscando...' : 'Buscar'}
              </button>
              {(searchDate || appliedSearchDate) && (
                <button
                  className="self-stretch rounded bg-gray-500 px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-gray-600"
                  onClick={clearSearch}
                >
                  Limpiar
                </button>
              )}
            </div>
            <DownloadRoutesBtn
              className="inline-flex gap-2.5 rounded border bg-accent px-4 py-2.5 text-sm"
              defaultMemberId={memberId}
              members={members}
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              <span className="font-medium">Descargar Rutas</span>
            </DownloadRoutesBtn>
          </div>
          <div className="relative flex-grow bg-accent">
            <RouteHistoryTable memberId={memberId} searchDate={appliedSearchDate} />
          </div>
        </div>
      </div>
    </ShieldLayout>
  );
}

const RouteHistoryTable = ({ memberId, searchDate }) => {
  const { axios } = useAxios()
  const router = useRouter();

  const { shield_id } = router.query;

  const fetchData = () => {
    // Include search date in the API call if available
    let url = `adminside/api/shield/shield-members-routes/?shield_id=${shield_id}&member_id=${memberId}`;
    if (searchDate) {
      url += `&date=${searchDate}`;
    }
    return axios.get(url);
  }

  // React-query for data fetching - include searchDate in query key for proper caching
  const { data: response, isLoading: routesLoading, isError: routesError, error: routesErrorDetails } = useQuery(
    [`shield-${shield_id}-member-${memberId}-routes`, searchDate],
    fetchData,
    {
      refetchOnWindowFocus: false,
      enabled: (!!shield_id) && (!!memberId),
      retry: 2,
      onError: (error) => {
        console.error('Error fetching routes:', error);
        toast.error('Error al cargar las rutas');
      }
    }
  );

  const allRoutes = Array.isArray(response?.data) ? response?.data : []

  // Filter routes based on search date (client-side fallback if backend doesn't support date filtering)
  const routes = searchDate
    ? allRoutes.filter(route => {
        if (!route.route_date) return false;
        try {
          const routeDate = format(new Date(route.route_date), 'yyyy-MM-dd');
          return routeDate === searchDate;
        } catch (error) {
          console.error('Error parsing route date:', error);
          return false;
        }
      })
    : allRoutes;



  return (
    <div className="h-full w-full">
      <Table
        wrapperClassName="absolute inset-0 w-full h-full px-4 pb-4 overflow-auto"
        className="relative"
      >
        <Table.Thead className="sticky top-0 bg-accent">
          <Table.Tr>
            <Table.Th>ID Ruta</Table.Th>
            <Table.Th>Velocidad</Table.Th>
            <Table.Th>Detalles de Ruta</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {!memberId ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8">
                Selecciona un miembro para ver su historial de rutas
              </Table.Td>
            </Table.Tr>
          ) : routesLoading ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2">{searchDate ? 'Buscando rutas...' : 'Cargando...'}</span>
                </div>
              </Table.Td>
            </Table.Tr>
          ) : routesError ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8">
                <div className="text-red-600">
                  <p>Error al cargar las rutas</p>
                  <p className="text-sm text-gray-500 mt-1">
                    {routesErrorDetails?.message || 'Intenta nuevamente'}
                  </p>
                </div>
              </Table.Td>
            </Table.Tr>
          ) : routes?.length > 0 ? (
            routes.map((route) => (
              <Table.Tr key={route.route_id || route.id}>
                <Table.Td className="font-semibold">
                  Ruta #{route.route_id || route.id}
                </Table.Td>
                <Table.Td>
                  <dd>{route.route_date ? format(new Date(route.route_date), 'dd/MM/yy') : 'N/A'}</dd>
                </Table.Td>
                <Table.Td>
                  <RouteDetailsModalBtn route={route} className="font-semibold text-primary hover:underline">
                    Ver Detalles
                  </RouteDetailsModalBtn>
                </Table.Td>
              </Table.Tr>
            ))
          ) : (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8">
                <div className="text-gray-600">
                  {searchDate
                    ? `No hay rutas para la fecha ${format(new Date(searchDate), 'dd/MM/yyyy')}`
                    : '¡No hay datos en el período de tiempo seleccionado!'
                  }
                </div>
              </Table.Td>
            </Table.Tr>
          )}
        </Table.Tbody>
      </Table>
    </div>
  )
}
