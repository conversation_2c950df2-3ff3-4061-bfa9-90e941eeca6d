from django.shortcuts import render
from Shield import models as shield_models
from . import models as sos_models
from . import serializer as sos_serializers
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from Account import models as account_models
from rest_framework import status
from django.contrib.auth import authenticate
from django.db.models import Q
# Create your views here.
from Account import fcm as fcm_file


class CreateSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = sos_serializers.CreateSosRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('shield_id')
        lat = request.data.get('lat')
        long = request.data.get('long')
        location = request.data.get('location')
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_obj = shield_obj.members.filter(id=request.user.id).last()
            if member_obj:
                sos_obj = sos_models.Sos.objects.create(sender=request.user.userprofile, shield=shield_obj,
                                                        lat=lat, long=long, location=location)
                sos_serializer = sos_serializers.CreateSosSerializer(sos_obj)
                user_registration_id = account_models.UserProfile.objects.filter(user__is_staff=True).last()
                registration_id = account_models.FcmDeviceRegistration.objects.filter(
                    userprofile=user_registration_id).last()
                # Only send FCM notification if a valid device registration exists
                if registration_id and registration_id.device_id:
                    fcm_file.send_fcm_notification_for_generating_alert(registration_id.device_id, 'New Alert SOS', 'Null')
                return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                               data=sos_serializer.data,
                                                               msg='New Sos Created'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='Sender is not a member of this shield'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class EvidenceSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = sos_serializers.CreateSosEvidenceSerializer
    get_serializer_class = sos_serializers.SosSerializer

    def get(self, request):
        serializer = self.get_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        sos_id = request.query_params.get('sos_id')
        sos_obj = sos_models.Sos.objects.filter(id=sos_id).last()
        if sos_obj:
            sos_evidence_objs = sos_models.SosEvidence.objects.filter(sos_id=sos_id)
            sos_serializer = sos_serializers.SosEvidenceSerializer(sos_evidence_objs, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=sos_serializer.data,
                                               msg='All Evidences of Sos'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Sos of this ID found'), status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sos_id = request.data.get('sos_id')
        evidence = request.data.get('evidence')
        sos_obj = sos_models.Sos.objects.filter(id=sos_id).last()
        if sos_obj:
            sos_obj = sos_models.SosEvidence.objects.create(sender=request.user.userprofile, sos=sos_obj,
                                                            evidence=evidence)
            sos_serializer = sos_serializers.CreateSosEvidenceSerializer(sos_obj)
            return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                           data=sos_serializer.data,
                                                           msg='New Sos Evidence Created'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Sos of this ID found'), status.HTTP_400_BAD_REQUEST)


class CancelSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = sos_serializers.CancelSosRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        sos_id = request.data.get('sos_id')
        password = request.data.get('password')
        sos_obj = sos_models.Sos.objects.filter(id=sos_id).last()
        if sos_obj:
            if sos_obj.active == True:
                user_authenticated = authenticate(username=request.user.username, password=password)
                if user_authenticated is not None:
                    updated_obj = sos_models.Sos.objects.filter(id=sos_id).update(active=False)
                    sos_obj = sos_models.Sos.objects.filter(id=sos_id).last()
                    sos_serializer = sos_serializers.CreateSosSerializer(sos_obj)
                    return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                                   data=sos_serializer.data,
                                                                   msg='Sos Deactivated'), status.HTTP_200_OK)
                else:
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg='Invalid User password'), status.HTTP_400_BAD_REQUEST)

            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='This Sos is already Deactivated'), status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Sos of this ID found'), status.HTTP_400_BAD_REQUEST)


class NotificationsSos(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = sos_serializers.ShieldSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        user = request.user.userprofile
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_obj = shield_obj.members.filter(id=request.user.id).last()
            if member_obj:
                sos_receiver_objs = sos_models.Sos.objects.filter(shield=shield_obj).filter(active=True)
                if sos_receiver_objs:
                    sos_serializer = sos_serializers.CreateSosSerializer(sos_receiver_objs, many=True)
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data=sos_serializer.data,
                                                       msg='All Sos Notifications for the user'), status.HTTP_200_OK)
                else:
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data={},
                                                       msg='All Sos Notifications for the user'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='User is not a member of this shield'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class GetUserSos(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            user_id = request.query_params.get('user_id')
            if not user_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='user_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get user profile
            try:
                user_profile = account_models.UserProfile.objects.get(user_id=user_id)
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='User not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get SOS records for this user with optimized queries
            sos_records = sos_models.Sos.objects.filter(sender=user_profile).select_related(
                'shield'
            ).prefetch_related(
                'soscomment_set',
                'sosevidence_set'
            ).order_by('-created_at')
            sos_serializer = sos_serializers.SosDetailSerializer(sos_records, many=True)

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=sos_serializer.data,
                    msg='User SOS records retrieved successfully'
                )
            )

        except Exception as e:
            print(f"Error in GetUserSos: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving user SOS records: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )