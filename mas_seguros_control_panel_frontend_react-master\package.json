{"name": "<PERSON>gu<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/react-wrapper": "^1.2.0", "@headlessui/react": "^1.7.6", "@heroicons/react": "^2.0.13", "@reduxjs/toolkit": "^1.9.1", "@tinymce/tinymce-react": "^3.13.1", "axios": "^1.2.2", "chart.js": "^4.1.1", "classnames": "^2.3.2", "date-fns": "^2.29.3", "eslint": "8.29.0", "eslint-config-next": "13.0.6", "faker": "5.5.3", "firebase": "^11.7.3", "fuse.js": "^6.6.2", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.28", "lodash": "^4.17.21", "next": "13.0.6", "react": "18.2.0", "react-animate-height": "^3.1.0", "react-chartjs-2": "^5.1.0", "react-dom": "18.2.0", "react-hook-form": "^7.41.3", "react-hot-toast": "^2.5.2", "react-icons": "^4.7.1", "react-query": "^3.39.2", "react-redux": "^8.0.5", "react-spinners": "^0.13.7", "react-use": "^17.4.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "autoprefixer": "^10.4.13", "postcss": "^8.4.20", "prettier": "^2.8.1", "prettier-plugin-tailwindcss": "^0.2.1", "tailwindcss": "^3.2.4"}}