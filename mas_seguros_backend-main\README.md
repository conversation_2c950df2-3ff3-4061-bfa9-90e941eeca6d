# 🛡️ Mas Seguros Backend

[![Django](https://img.shields.io/badge/Django-5.2.3-green.svg)](https://www.djangoproject.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Ready-blue.svg)](https://www.postgresql.org/)
[![Production Ready](https://img.shields.io/badge/Production-Ready-brightgreen.svg)](#production-deployment)

A comprehensive Django-based backend system for the Mas Seguros application, providing advanced security and safety management features with real-time tracking, emergency response, and organizational management capabilities.

## 🎯 Project Overview

Mas Seguros is a cutting-edge security management platform designed for personal safety, organizational protection, and emergency response. The system provides:

### 🔐 **Core Security Features**
- **User Account Management** - Secure authentication with JWT tokens
- **Shield Management** - Group/organization security coordination
- **Emergency SOS System** - Real-time emergency alerts and response
- **Alert Management** - Comprehensive alerting system with categories
- **Biometric Tracking** - Advanced user verification and tracking

### 📊 **Management & Support**
- **Membership System** - Subscription and tier management
- **Support Tickets** - Integrated customer support system
- **FAQ Management** - Dynamic frequently asked questions
- **Admin Dashboard** - Comprehensive administrative interface
- **Company Profiles** - Multi-tenant organization management

### 🚀 **Recent Updates (June 2025)**
- ✅ **Complete Migration System Overhaul** - All apps now have proper migrations
- ✅ **Production-Ready Configuration** - Optimized for deployment
- ✅ **Enhanced Security** - Firebase credentials and sensitive data properly secured
- ✅ **Improved Development Tools** - Auto-activating reset scripts
- ✅ **Comprehensive Documentation** - Complete guides for development and deployment

## 🛠️ Technology Stack

### **Backend Framework**
- **Django 5.2.3** - Modern Python web framework
- **Django REST Framework** - Powerful API development
- **PostgreSQL** - Production database (SQLite for development)
- **WhiteNoise** - Static file serving

### **Authentication & Security**
- **JWT Tokens** - Secure authentication
- **Django Auth** - Built-in user management
- **CORS Headers** - Cross-origin resource sharing
- **Firebase Admin SDK** - Cloud services integration

### **Development & Deployment**
- **Python 3.8+** - Modern Python features
- **Virtual Environment** - Isolated dependencies
- **Git** - Version control with optimized .gitignore
- **Gunicorn** - Production WSGI server

### **Additional Features**
- **File Upload** - Media handling with organized storage
- **Email Integration** - SMTP with SendGrid support
- **Real-time Features** - WebSocket-ready architecture
- **API Documentation** - Comprehensive endpoint documentation

## 🚀 Quick Start

### **Prerequisites**
- **Python 3.8+** (3.11+ recommended)
- **pip** (latest version)
- **Git** (for version control)
- **PostgreSQL** (for production) or SQLite (for development)

### **Development Setup**

#### 1. **Clone the Repository**
```bash
git clone <repository-url>
cd mas_backend
```

#### 2. **Set Up Virtual Environment**
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows (PowerShell):
.\venv\Scripts\Activate.ps1

# Windows (Command Prompt):
venv\Scripts\activate

# Linux/macOS:
source venv/bin/activate
```

#### 3. **Install Dependencies**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### 4. **Environment Configuration**
Create a `.env` file in the project root:
```env
# Database Configuration
DB_NAME=masbackend
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
SENDGRID_API_KEY=your_sendgrid_key

# Security
SECRET_KEY=your_secret_key_here
DEBUG=True

# Firebase (place your credentials file in Account/mas-seguros-cred.json)
```

#### 5. **Database Setup**
```bash
# Apply migrations (all migrations are ready!)
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data (optional)
python manage.py loaddata dummy_database/sample_data.json
```

#### 6. **Run Development Server**
```bash
python manage.py runserver
```

🎉 **Your server is now running at** `http://127.0.0.1:8000/`

## 📁 Project Structure

The project follows Django best practices with modular app architecture:

```
mas_backend/
├── 🔐 Account/              # User authentication & profiles
│   ├── models.py           # UserProfile, Feature, Package, FcmDeviceRegistration
│   ├── views.py            # Authentication APIs, profile management
│   ├── migrations/         # ✅ Ready for production
│   └── mas-seguros-cred.json # 🔒 Firebase credentials (ignored in git)
│
├── 🛡️ Shield/               # Security group management
│   ├── models.py           # ShieldModel, Route, Location, Biometric
│   ├── views.py            # Shield creation, member management
│   └── migrations/         # ✅ Ready for production
│
├── 🚨 Alert/                # Alert management system
│   ├── models.py           # AlertModel, AlertCategories, AlertStatus
│   ├── views.py            # Alert creation, status management
│   └── migrations/         # ✅ Ready for production
│
├── 🆘 Sos/                  # Emergency SOS system
│   ├── models.py           # Sos, SosComment, SosEvidence
│   ├── views.py            # Emergency alert handling
│   └── migrations/         # ✅ Ready for production
│
├── 💳 Membership/           # Subscription management
│   ├── models.py           # MembershipModel
│   ├── views.py            # Membership tiers, billing
│   └── migrations/         # ✅ Ready for production
│
├── 🎫 Ticket/               # Support ticket system
│   ├── models.py           # Ticket, TicketSubject, TicketMessage
│   ├── views.py            # Support ticket management
│   └── migrations/         # ✅ Ready for production
│
├── ❓ Faq/                  # FAQ management
│   ├── models.py           # FaqCategory, FaqQuestion
│   ├── views.py            # FAQ CRUD operations
│   └── migrations/         # ✅ Ready for production
│
├── ℹ️ About/                # App information
│   ├── models.py           # DataPolicie, TermsAndCondition
│   ├── views.py            # Legal content management
│   └── migrations/         # ✅ Ready for production
│
├── 👨‍💼 AdminSide/            # Admin panel & dashboard
│   ├── models.py           # PromoCode, SuperAdmin, CompanyProfileModel
│   ├── views.py            # Admin operations, analytics
│   ├── migrations/         # ✅ Ready for production
│   └── [subdirectories]/   # Organized admin modules
│
├── 🔧 mas_seguros_backend/  # Project configuration
│   ├── settings.py         # Django settings
│   ├── urls.py             # URL routing
│   └── wsgi.py             # WSGI configuration
│
├── 📚 Documentation/
│   ├── MIGRATION_GUIDE.md           # Complete migration management
│   ├── MIGRATION_FIX_SUMMARY.md     # Recent fixes summary
│   ├── PRODUCTION_READINESS_CHECKLIST.md # Deployment checklist
│   └── README.md                    # This file
│
├── 🛠️ Scripts/
│   ├── reset-migrations.ps1         # Windows migration reset
│   ├── reset-migrations.sh          # Linux/macOS migration reset
│   └── dummy_database/              # Sample data setup
│
├── 📦 Dependencies/
│   ├── requirements.txt             # Python dependencies
│   ├── .env                        # Environment variables (create this)
│   └── .gitignore                  # ✅ Production-ready git ignore
│
└── 📁 Media & Static/
    ├── media/                      # User uploaded files
    ├── staticfiles/                # Collected static files
    └── static/                     # App static files
```

### **🎯 App Responsibilities**

| App | Primary Function | Key Models | API Endpoints |
|-----|------------------|------------|---------------|
| **Account** | User management, authentication | UserProfile, Feature, Package | `/api/account/` |
| **Shield** | Security groups, tracking | ShieldModel, Route, Biometric | `/api/shield/` |
| **Alert** | Alert system, notifications | AlertModel, AlertCategories | `/api/alert/` |
| **Sos** | Emergency response | Sos, SosComment, SosEvidence | `/api/sos/` |
| **Membership** | Subscriptions, billing | MembershipModel | `/api/Membership/` |
| **Ticket** | Customer support | Ticket, TicketMessage | `/api/ticket/` |
| **Faq** | Help documentation | FaqCategory, FaqQuestion | `/api/faq/` |
| **About** | Legal content | DataPolicie, TermsAndCondition | `/api/about/` |
| **AdminSide** | Administration | SuperAdmin, CompanyProfile | `/adminside/` |

## 🌐 API Endpoints

### **Core API Structure**
All APIs follow RESTful conventions with consistent response formats:

```
Base URL: http://127.0.0.1:8000/ (development)
Base URL: https://your-domain.com/ (production)
```

### **Authentication Endpoints**
```
POST   /api/account/register/              # User registration
POST   /api/account/login/                 # User login (returns JWT)
POST   /api/account/logout/                # User logout
POST   /api/account/sendverificationcode/  # Send verification code
POST   /api/account/forgetpasswordverifycode/ # Password reset verification
```

### **User Management**
```
GET    /api/account/accounts/              # Get user profiles
PUT    /api/account/editprofile/           # Update user profile
POST   /api/account/changephone/           # Change phone number
POST   /api/account/changepassword/        # Change password
DELETE /api/account/deleteaccount/         # Delete user account
```

### **Shield Management**
```
GET    /api/shield/shields/                # List shields
POST   /api/shield/create/                 # Create new shield
GET    /api/shield/routes/                 # Get routes
POST   /api/shield/join-request/           # Request to join shield
GET    /api/shield/biometric/              # Biometric data
```

### **Alert System**
```
GET    /api/alert/                         # List alerts
POST   /api/alert/                         # Create alert
GET    /api/alert/getallalerts/            # Get all alerts
GET    /api/alert/alertcategories/         # Alert categories
GET    /api/alert/alertstatuses/           # Alert statuses
POST   /api/alert/alertreviews/            # Submit alert review
```

### **Emergency SOS**
```
GET    /api/sos/                           # List SOS alerts
POST   /api/sos/                           # Create SOS alert
PUT    /api/sos/update-status/             # Update SOS status
POST   /api/sos/add-comment/               # Add SOS comment
```

### **Support System**
```
GET    /api/ticket/                        # List tickets
POST   /api/ticket/create/                 # Create support ticket
GET    /api/ticket/messages/               # Get ticket messages
POST   /api/ticket/reply/                  # Reply to ticket
```

### **Admin Panel**
```
GET    /adminside/dashboard/               # Admin dashboard
GET    /adminside/users/                   # Manage users
GET    /adminside/alerts/                  # Manage alerts
GET    /adminside/reports/                 # Generate reports
POST   /adminside/promocodes/              # Create promo codes
```

### **Content Management**
```
GET    /api/faq/                           # FAQ list
GET    /api/about/                         # About information
GET    /api/Membership/                    # Membership plans
```

### **Response Format**
All API responses follow this structure:
```json
{
    "success": true,
    "message": "Operation successful",
    "data": { ... },
    "errors": null
}
```

## 🚀 Production Deployment

### **Pre-Deployment Checklist**
✅ All migrations are ready and committed
✅ Environment variables configured
✅ Static files collected
✅ Database credentials secured
✅ Firebase credentials properly ignored
✅ SSL certificates configured

### **Deployment Steps**

#### 1. **Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and PostgreSQL
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib nginx

# Create application user
sudo useradd --system --shell /bin/bash --home /opt/mas_seguros mas_seguros
```

#### 2. **Application Deployment**
```bash
# Clone repository
cd /opt/mas_seguros
sudo -u mas_seguros git clone <repository-url> .

# Set up virtual environment
sudo -u mas_seguros python3 -m venv venv
sudo -u mas_seguros ./venv/bin/pip install -r requirements.txt

# Configure environment
sudo -u mas_seguros cp .env.example .env
# Edit .env with production values
```

#### 3. **Database Setup**
```bash
# Create PostgreSQL database
sudo -u postgres createdb masbackend
sudo -u postgres createuser mas_seguros
sudo -u postgres psql -c "ALTER USER mas_seguros WITH PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE masbackend TO mas_seguros;"

# Run migrations
sudo -u mas_seguros ./venv/bin/python manage.py migrate
sudo -u mas_seguros ./venv/bin/python manage.py collectstatic --noinput
```

#### 4. **Gunicorn Configuration**
```bash
# Create Gunicorn service
sudo tee /etc/systemd/system/mas_seguros.service > /dev/null <<EOF
[Unit]
Description=Mas Seguros Backend
After=network.target

[Service]
User=mas_seguros
Group=mas_seguros
WorkingDirectory=/opt/mas_seguros
Environment="PATH=/opt/mas_seguros/venv/bin"
ExecStart=/opt/mas_seguros/venv/bin/gunicorn --workers 3 --bind unix:/opt/mas_seguros/mas_seguros.sock mas_seguros_backend.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Start and enable service
sudo systemctl start mas_seguros
sudo systemctl enable mas_seguros
```

#### 5. **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location = /favicon.ico { access_log off; log_not_found off; }

    location /static/ {
        root /opt/mas_seguros;
    }

    location /media/ {
        root /opt/mas_seguros;
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/opt/mas_seguros/mas_seguros.sock;
    }
}
```

### **Environment Variables (Production)**
```env
DEBUG=False
SECRET_KEY=your_very_secure_secret_key_here
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database
DB_NAME=masbackend
DB_USER=mas_seguros
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432

# Email
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
SENDGRID_API_KEY=your_sendgrid_api_key

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### **Quick Deployment Commands**
```bash
# Pull latest changes
git pull origin main

# Apply new migrations
./venv/bin/python manage.py migrate

# Collect static files
./venv/bin/python manage.py collectstatic --noinput

# Restart services
sudo systemctl restart mas_seguros
sudo systemctl reload nginx
```

## 🔄 Migration Management

### **✅ Migration Status: PRODUCTION READY**
All apps now have complete, tested migrations ready for deployment!

| App | Migration Status | Files |
|-----|------------------|-------|
| Account | ✅ Ready | `0001_initial.py` |
| Alert | ✅ Ready | `0001_initial.py` |
| Shield | ✅ Ready | `0001_initial.py` |
| Membership | ✅ Ready | `0001_initial.py` |
| Faq | ✅ Ready | `0001_initial.py` |
| About | ✅ Ready | `0001_initial.py` |
| Ticket | ✅ Ready | `0001_initial.py` |
| Sos | ✅ Ready | `0001_initial.py` |
| AdminSide | ✅ Ready | `0001_initial.py`, `0002_initial.py` |

### **Common Migration Commands**
```bash
# Check migration status
python manage.py showmigrations

# Create new migrations (when models change)
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Check for issues
python manage.py check
```

### **Reset Migration Scripts**
Enhanced scripts with automatic virtual environment activation:

#### **Windows (PowerShell)**
```powershell
# Full reset with backup (recommended)
.\reset-migrations.ps1

# Quick reset without backup
.\reset-migrations.ps1 -SkipBackup

# Automated reset (no prompts)
.\reset-migrations.ps1 -Force

# Show help and options
.\reset-migrations.ps1 -Help
```

#### **Linux/macOS (Bash)**
```bash
# Make executable (first time only)
chmod +x reset-migrations.sh

# Full reset with backup (recommended)
./reset-migrations.sh

# Quick reset without backup
./reset-migrations.sh --skip-backup

# Automated reset (no prompts)
./reset-migrations.sh --force

# Reset with new virtual environment
./reset-migrations.sh --recreate-venv

# Show help and options
./reset-migrations.sh --help
```

### **Migration Best Practices**
- ✅ Always backup before major changes
- ✅ Test migrations in development first
- ✅ Use `--dry-run` flags to preview changes
- ✅ Keep migration files in version control
- ✅ Review generated migrations before applying

## 📚 Documentation

### **Complete Guides**
- **[MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)** - Comprehensive migration management
- **[MIGRATION_FIX_SUMMARY.md](MIGRATION_FIX_SUMMARY.md)** - Recent fixes and improvements
- **[PRODUCTION_READINESS_CHECKLIST.md](PRODUCTION_READINESS_CHECKLIST.md)** - Deployment checklist

### **API Documentation**
- **Postman Collection** - `Mas_Seguros_Backend_API.postman_collection.json`
- **Environment Setup** - `Mas_Seguros_Environments.postman_environment.json`
- **Import Guide** - `POSTMAN_IMPORT_GUIDE.md`

### **Development Tools**
- **Reset Scripts** - Automated migration reset with backup
- **Sample Data** - `dummy_database/complete_databse_setup.py`
- **Validation** - `validate_postman_collection.py`

## 🛡️ Security Features

### **Authentication & Authorization**
- JWT token-based authentication
- Role-based access control (User/Admin)
- Multi-factor authentication support
- Session management

### **Data Protection**
- Firebase credentials properly secured
- Environment variables for sensitive data
- CORS configuration for API security
- Input validation and sanitization

### **Production Security**
- HTTPS enforcement
- HSTS headers
- Secure cookie settings
- CSRF protection

## 🚀 Performance Features

### **Database Optimization**
- Indexed fields for fast queries
- Optimized foreign key relationships
- Efficient pagination
- Query optimization

### **Caching & Static Files**
- WhiteNoise for static file serving
- Compressed static files
- Media file organization
- CDN-ready configuration

## 🧪 Testing

### **Run Tests**
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test Account

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

### **Test Data**
```bash
# Load sample data
python manage.py loaddata dummy_database/sample_data.json

# Create test superuser
python manage.py createsuperuser --username admin --email <EMAIL>
```

## 📞 Support & Contact

### **Development Team**
- **Backend Development** - Django REST Framework
- **Database Design** - PostgreSQL optimization
- **Security Implementation** - Authentication & authorization
- **API Documentation** - Comprehensive endpoint guides

### **Getting Help**
1. Check the documentation files in the project
2. Review the migration guides for common issues
3. Use the reset scripts for clean development setup
4. Refer to the production readiness checklist for deployment

### **Contributing**
1. Fork the repository
2. Create a feature branch
3. Make your changes with proper tests
4. Update documentation as needed
5. Submit a pull request

---

## 📄 License

This project is proprietary software. All rights reserved.

## 🎯 Project Status

**Current Version**: Production Ready (June 2025)
**Migration Status**: ✅ Complete
**Security Status**: ✅ Secured
**Documentation**: ✅ Comprehensive
**Deployment**: ✅ Ready

---

*Last Updated: June 14, 2025 - Complete migration system overhaul and production readiness achieved* 🎉
