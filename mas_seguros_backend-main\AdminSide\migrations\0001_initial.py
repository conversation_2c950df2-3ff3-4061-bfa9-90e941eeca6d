# Generated by Django 5.2.3 on 2025-06-14 11:14

import AdminSide.AdminAPi.models
import AdminSide.CompanyDashboard.models
import datetime
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PromoCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('label', models.CharField(blank=True, max_length=150, null=True)),
                ('promo_code', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('code_id', models.CharField(default=AdminSide.AdminAPi.models.generate_promo_code, max_length=10)),
                ('start_duration', models.DateField(default=datetime.date.today)),
                ('end_duration', models.DateField(null=True)),
                ('stocks', models.IntegerField()),
                ('discount', models.IntegerField(null=True)),
                ('Etiquette', models.CharField(max_length=100, null=True)),
                ('state', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('membership', models.CharField(blank=True, choices=[('level 1', 'level 1'), ('level 2', 'level 2'), ('level 3', 'level 3'), ('level 4', 'level 4')], max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='SuperAdmin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
        ),
        migrations.CreateModel(
            name='CompanyProfileModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='company_images')),
                ('company_code', models.CharField(blank=True, default=AdminSide.CompanyDashboard.models.generate_company_code, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('suspended', models.BooleanField(blank=True, default=False, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('admin', models.ManyToManyField(blank=True, related_name='admin_to_users', to='Account.userprofile')),
            ],
        ),
    ]
