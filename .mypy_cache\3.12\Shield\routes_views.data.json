{".class": "MypyFile", "_fullname": "Shield.routes_views", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.APIView", "name": "APIView", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.APIView", "source_any": null, "type_of_any": 3}}}, "DownloadRouteHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.DownloadRouteHistory", "name": "DownloadRouteHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.DownloadRouteHistory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.DownloadRouteHistory", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.DownloadRouteHistory.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.DownloadRouteHistory.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.DownloadRouteHistory.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.DownloadRouteHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.DownloadRouteHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IsAuthenticated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.IsAuthenticated", "name": "IsAuthenticated", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}}}, "MemberLocation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.MemberLocation", "name": "MemberLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.MemberLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.MemberLocation", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.MemberLocation.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.MemberLocation.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.MemberLocation.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldMemberLocationSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.MemberLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.MemberLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MemberRoutesHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.MemberRoutesHistory", "name": "MemberRoutesHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.MemberRoutesHistory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.MemberRoutesHistory", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.MemberRoutesHistory.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.MemberRoutesHistory.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.MemberRoutesHistory.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.MemberRoutesHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.MemberRoutesHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.Response", "name": "Response", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.Response", "source_any": null, "type_of_any": 3}}}, "RouteDetail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.RouteDetail", "name": "RouteDetail", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.RouteDetail", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.RouteDetail", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.RouteDetail.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.RouteDetail.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.RouteDetail.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetRouteDetailSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.RouteDetail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.RouteDetail", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocationsBattery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.ShieldMemberLocationsBattery", "name": "ShieldMemberLocationsBattery", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.ShieldMemberLocationsBattery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.ShieldMemberLocationsBattery", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.ShieldMemberLocationsBattery.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.ShieldMemberLocationsBattery.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.ShieldMemberLocationsBattery.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.ShieldMemberLocationsBattery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.ShieldMemberLocationsBattery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserRouteProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.routes_views.UserRouteProfile", "name": "UserRouteProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.routes_views.UserRouteProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.routes_views", "mro": ["Shield.routes_views.UserRouteProfile", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.routes_views.UserRouteProfile.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.UserRouteProfile.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.routes_views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.routes_views.UserRouteProfile.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldUserRouteProfileSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.routes_views.UserRouteProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.routes_views.UserRouteProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.routes_views.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "account_models": {".class": "SymbolTableNode", "cross_ref": "Account.models", "kind": "Gdef"}, "backend_utils": {".class": "SymbolTableNode", "cross_ref": "mas_seguros_backend.utils", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_object_or_404": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.get_object_or_404", "name": "get_object_or_404", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.get_object_or_404", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "shield_models": {".class": "SymbolTableNode", "cross_ref": "Shield.models", "kind": "Gdef"}, "shield_serialziers": {".class": "SymbolTableNode", "cross_ref": "Shield.serializers", "kind": "Gdef"}, "shield_utils": {".class": "SymbolTableNode", "cross_ref": "Shield.utils", "kind": "Gdef"}, "status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.status", "source_any": null, "type_of_any": 3}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.routes_views.timezone", "name": "timezone", "type": {".class": "AnyType", "missing_import_name": "Shield.routes_views.timezone", "source_any": null, "type_of_any": 3}}}}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\routes_views.py"}