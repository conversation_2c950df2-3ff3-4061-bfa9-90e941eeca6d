import datetime
import random

from django.utils.crypto import get_random_string
from django.db import models
from Account import models as account_models
from mas_seguros_backend import settings as backend_setting

# Create your models here.

ALERT_SENT = 'Alerta enviada'
HELP_SENT = 'Ayuda enviada'
ALERT_SOLVED = 'Alerta resuelta'

alert_status = [
    (ALERT_SENT, ALERT_SENT),
    (HELP_SENT, HELP_SENT),
    (ALERT_SOLVED, ALERT_SOLVED)
]
HEALTH = 'Health'
POLICE = 'Police'
TRAFFIC = 'Traffic'
FIREFIGHTER = 'Firefighter'
ASSISTANCE = '24/7 Assistance'

alert_category = [
    (HEALTH, HEALTH),
    (POLICE, POLICE),
    (TRAFFIC, TRAFFIC),
    (FIREFIGHTER, FIREFIGHTER),
    (ASSISTANCE, ASSISTANCE),
]




class AlertCategories(models.Model):
    name = models.CharField(max_length=200, null=True, blank=True)
    image = models.ImageField(null=True, blank=True, upload_to='alert_categories/')

    @property
    def image_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.image.url)
        except:
            return None

    def __str__(self):
        return self.name


class AlertStatus(models.Model):
    name = models.CharField(max_length=200, choices=alert_status, null=True, blank=True)

    def __str__(self):
        return self.name


class AlertModel(models.Model):
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    category = models.ForeignKey(AlertCategories, on_delete=models.SET_NULL, null=True, blank=True, db_index=True)
    num = models.CharField(max_length=50, null=True, blank=True)
    status = models.ForeignKey(AlertStatus, on_delete=models.SET_NULL, null=True, blank=True, db_index=True)
    date = models.DateField(null=True, blank=True, auto_now_add=True)
    time = models.TimeField(null=True, blank=True, auto_now_add=True)
    rating = models.CharField(max_length=10, null=True, blank=True)
    evidence = models.FileField(null=True, blank=True, upload_to='alert_evidence')
    thumbnail = models.FileField(null=True, blank=True, upload_to='alert_evidence')
    video_url = models.CharField(max_length=1000, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    address = models.CharField(max_length=200, null=True, blank=True)
    lat = models.CharField(max_length=30, null=True, blank=True)
    long = models.CharField(max_length=30, null=True, blank=True)
    current_speed = models.CharField(max_length=50, null=True, blank=True)
    phone_battery = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    evidence_number = models.CharField(max_length=50)
    alert_opened = models.BooleanField(null=True, blank=True, default=False)
    alert_seen = models.BooleanField(null=True, blank=True, default=False)
    rating_description = models.TextField(blank=True, null=True)

    def get_status_or_default(self):
        """Get the alert status or return default if null"""
        if self.status:
            return self.status
        # Return default status if null
        default_status = AlertStatus.objects.filter(name=ALERT_SENT).first()
        return default_status

    def get_status_name(self):
        """Get the status name with fallback"""
        status = self.get_status_or_default()
        return status.name if status else "Sin estado"

    def save(self, *args, **kwargs):
        """Override save to ensure status is always set"""
        if not self.status:
            self.status = AlertStatus.objects.filter(name=ALERT_SENT).first()
        super().save(*args, **kwargs)

    class Meta:
        indexes = [
            models.Index(fields=['userprofile', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['category', '-created_at']),
            models.Index(fields=['userprofile', 'status', '-created_at']),
            models.Index(fields=['category', 'status', '-created_at']),
            models.Index(fields=['-date', '-time']),
        ]

    @property
    def alert_time(self):
        return self.time.strftime("%H:%M:%S")

    @property
    def evidence_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.evidence.url)
        except:
            return None

    @property
    def thumbnail_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.thumbnail.url)
        except:
            return None

    @property
    def alert_date(self):
        return self.date.strftime("%m/%d/%Y")

    def __str__(self):
        return "{}".format(self.num)


class AlertModifyHistory(models.Model):
    alert = models.ForeignKey(AlertModel, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=50, choices=alert_status, null=True, blank=True)
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class AlertComment(models.Model):
    alert = models.ForeignKey(AlertModel, on_delete=models.SET_NULL, null=True, blank=True)
    comment = models.TextField(null=True, blank=True)
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.alert.userprofile.user.get_full_name()
