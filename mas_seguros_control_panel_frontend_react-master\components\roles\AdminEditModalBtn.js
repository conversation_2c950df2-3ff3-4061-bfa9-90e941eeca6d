import React, { createElement, useState } from "react";
import AdminFormModal from "../admin/AdminFormModal";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const AdminEditModalBtn = ({ as = "button", className = "", currentAdmin, onUpdate, ...props }) => {
  const [open, setOpen] = useState(false);
  const [wasRecentlySuccessful, setWasRecentlySuccessful] = useState(false);
  const { axios } = useAxios();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const close = () => {
    setOpen(false);
  };

  const submit = async (data, selectedImage = null) => {
    if (!currentAdmin) {
      toast.error('No se pudo identificar el administrador');
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare FormData for file upload
      const formData = new FormData();

      // Add text fields
      formData.append('admin_id', currentAdmin.id);
      formData.append('first_name', data.first_name);
      formData.append('last_name', data.last_name);
      formData.append('phone', data.phone);
      formData.append('identification_card', typeof data.identification_card === 'string' ? data.identification_card : (data.identification_card ? String(data.identification_card) : ''));

      // Convert birth_date from YYYY-MM-DD to DD/MM/YYYY before sending
      let birthDateFormatted = data.birth_date;
      if (birthDateFormatted && /^\d{4}-\d{2}-\d{2}$/.test(birthDateFormatted)) {
        const [yyyy, mm, dd] = birthDateFormatted.split('-');
        birthDateFormatted = `${dd}/${mm}/${yyyy}`;
      }
      // Only append if valid date
      if (birthDateFormatted && /^\d{2}\/\d{2}\/\d{4}$/.test(birthDateFormatted)) {
        formData.append('birth_date', birthDateFormatted);
      }

      // Add permission fields
      formData.append('users_access', data.users_access || false);
      formData.append('shields_access', data.shields_access || false);
      formData.append('alerts_sos_access', data.alerts_sos_access || false);
      formData.append('payment_history_access', data.payment_history_access || false);
      formData.append('support_access', data.support_access || false);
      formData.append('roles_access', data.roles_access || false);
      formData.append('full_access', data.full_access || false);

      // Add image if selected
      if (selectedImage) {
        formData.append('image', selectedImage);
      }

      const response = await axios.post('/adminside/api/roles/admin/update/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        toast.success(response.data.message);
        setWasRecentlySuccessful(true);
        setOpen(false);

        // Call the callback to refresh data and pass updated admin
        if (onUpdate) {
          const updatedAdmin = {
            ...currentAdmin,
            ...data,
            identification_card: data.identification_card,
          };
          onUpdate(updatedAdmin);
        }
      } else {
        toast.error(response.data.message || 'Error updating administrator');
      }
    } catch (error) {
      console.error('Error updating admin:', error);

      // Handle validation errors
      if (error?.response?.data?.errors) {
        const errors = error.response.data.errors;

        // Show specific field errors
        Object.keys(errors).forEach(field => {
          const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
          fieldErrors.forEach(errorMsg => {
            toast.error(`${field}: ${errorMsg}`);
          });
        });
      } else {
        // Show general error message
        const errorMessage = error?.response?.data?.message ||
                           error?.response?.data?.msg ||
                           'Error actualizando administrador';
        toast.error(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <AdminFormModal
        open={open}
        close={close}
        mode="edit"
        submit={submit}
        wasRecentlySuccessful={wasRecentlySuccessful}
        currentAdmin={currentAdmin}
        isSubmitting={isSubmitting}
      />
      {createElement(as, {
        type: "button",
        onClick: () => setOpen(true),
        className: className,
        ...props,
      })}
    </>
  );
};

export default AdminEditModalBtn;
