import React, { useState, useEffect, useRef } from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import DividerText from "@/components/utility/DividerText";
import ChatBoxWrapper from "@/components/shields/shield/chat/ChatBoxWrapper";
import ChatBox from "@/components/shields/shield/chat/ChatBox";
import InputGroup from "@/components/utility/InputGroup";
import { useRouter } from "next/router";
import { format, isSameDay } from "date-fns";
import { toast } from "react-hot-toast";
import FirebaseService from "@/services/firebaseService";

export default function index() {
  const router = useRouter();
  const { shield_id } = router.query;
  const messagesEndRef = useRef(null);
  const [messages, setMessages] = useState([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [searchDate, setSearchDate] = useState("");
  const [filteredMessages, setFilteredMessages] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Subscribe to real-time updates for shield messages
  useEffect(() => {
    if (!shield_id) return;

    console.log(`Setting up message subscription for shield ${shield_id}`);

    // Reset messages and set loading state when shield changes
    setMessages([]);
    setFilteredMessages([]);
    setIsLoadingMessages(true);

    // Variable to store the unsubscribe function
    let unsubscribeFunc = () => {};

    // Subscribe to real-time updates with Firebase
    console.log(`Subscribing to Firebase updates for shield ${shield_id}`);

    FirebaseService.subscribeToShieldMessages(
      shield_id.toString(),
      (changes, fullSnapshotMessages) => {
        console.log(`Received Firestore changes for shield ${shield_id}`, changes);

        setMessages(prevMessages => {
          if (prevMessages.length === 0 && fullSnapshotMessages.length > 0 || changes.length > prevMessages.length * 0.5) {
             console.log('Replacing messages state due to initial load or significant changes for shield chat');
             return fullSnapshotMessages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          }

          let newMessages = [...prevMessages];
          changes.forEach(change => {
            const normalizedDoc = change.doc;
            if (!normalizedDoc) return;

            if (change.type === 'added') {
              const insertIndex = newMessages.findIndex(msg => new Date(msg.createdAt) > new Date(normalizedDoc.createdAt));
              if (insertIndex === -1) {
                newMessages.push(normalizedDoc);
              } else {
                newMessages.splice(insertIndex, 0, normalizedDoc);
              }
            } else if (change.type === 'modified') {
              const index = newMessages.findIndex(msg => msg.id === normalizedDoc.id);
              if (index !== -1) {
                newMessages[index] = normalizedDoc;
              }
            } else if (change.type === 'removed') {
              newMessages = newMessages.filter(msg => msg.id !== normalizedDoc.id);
            }
          });

          console.log(`Updated messages state for shield ${shield_id}. New count: ${newMessages.length}`);
          return newMessages;
        });

        if (fullSnapshotMessages && fullSnapshotMessages.length > 0) {
            setTimeout(scrollToBottom, 100);
        }

        setIsLoadingMessages(false);
      }
    ).then(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribeFunc = unsubscribe;
      }
      setTimeout(() => {
        setIsLoadingMessages(false);
      }, 2000);
    }).catch(error => {
      console.error(`Error setting up subscription for shield ${shield_id}:`, error);
      setIsLoadingMessages(false);
    });

    return () => {
      console.log(`Cleaning up subscription for shield ${shield_id}`);
      if (typeof unsubscribeFunc === 'function') {
        unsubscribeFunc();
      }
      setMessages([]);
      setFilteredMessages([]);
    };
  }, [shield_id]);

  // Handle search date change
  const handleSearchDateChange = (e) => {
    setSearchDate(e.target.value);
  };

  

  // Handle search
  const handleSearch = () => {
    if (!searchDate) {
      toast.error('Por favor seleccione una fecha');
      return;
    }

    setIsSearching(true);
    try {
      const selectedDate = new Date(searchDate);
      const filtered = messages.filter(msg => {
        if (!msg.createdAt) return false;
        const msgDate = new Date(msg.createdAt);
        return isSameDay(msgDate, selectedDate);
      });

      setFilteredMessages(filtered);
      if (filtered.length === 0) {
        toast.info('No hay mensajes para la fecha seleccionada');
      }
    } catch (error) {
      console.error('Error filtering messages:', error);
      toast.error('Error al filtrar mensajes');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <ShieldLayout pageTitle="Chat de Escudo" headerTitle="Chat de Escudo">
      <div className="mt-4 grid grid-cols-1 gap-5 lg:grid-cols-2">
        {/* Left side - Live Chat */}
        <ChatBoxWrapper>
          <h3 className="text-lg font-semibold">Chat</h3>
          <ChatBox>
            {isLoadingMessages ? (
              <div className="flex items-center justify-center h-full">
                <p>Cargando mensajes...</p>
              </div>
            ) : messages.length > 0 ? (
              <div className="space-y-4">
                {messages.map((msg, index) => {
                  if (!msg || typeof msg !== 'object') return null;
                  const { id, type, content, sender, createdAt } = msg;
                  let messageBody = null;
                  let badge = null;

                  switch (type) {
                    case 'voice':
                      messageBody = content ? (
                        <audio controls src={content} className="w-full mt-2">
                          Tu navegador no soporta el elemento de audio.
                        </audio>
                      ) : (
                        <span className="italic text-gray-400">Audio no disponible</span>
                      );
                      badge = <span className="ml-2 px-2 py-1 rounded bg-blue-200 text-blue-800 text-xs">Audio</span>;
                      break;
                    case 'sos':
                      messageBody = (
                        <div className=" ">
                          <span className="font-bold text-white">SOS:</span> {content?.description || 'Mensaje SOS recibido'}
                        </div>
                      );
                      badge = <span className="ml-2 px-2 py-1 rounded bg-red-200 text-red-800 text-xs">SOS</span>;
                      break;
                    case 'alert':
                      messageBody = (
                        <div >
                          <span className="font-bold text-white">Alerta:</span> {content?.description || 'Mensaje de alerta recibido'}
                        </div>
                      );
                      badge = <span className="ml-2 px-2 py-1 rounded bg-yellow-200 text-yellow-800 text-xs">Alerta</span>;
                      break;
                    case 'text':
                    default:
                      messageBody = <span>{content}</span>;
                      break;
                  }

                  return (
                    <div
                      key={id || `msg-${index}`}
                      className={`p-4 rounded w-fit ${sender.role === 'Admin' ? 'bg-blue-50 ml-12' : 'bg-white mr-12'} 
                       ${ (type === "sos" || type === "alert") && "bg-red-500 text-white"}
                      `}
                    >
                      <div className={`flex flex-col justify-between  items-start ${(type === "sos" || type === "alert") && "text-white"}`}>
                        <div className="flex items-center justify-center gap-2">
                            <span className={`font-semibold text-sm  ${(type === "sos" || type === "alert") ? "text-white" : "text-blue-500"}`}>{sender.name}</span>
                            <span className={`${(type === "sos" || type === "alert") ? "text-white" : "text-gray-500"} text-sm`}>(ID-{id})</span>
                        </div>
                        {/* {badge} */}
                          {messageBody}
                      </div>
                        <span className="text-xs  ml-auto">
                          {(() => {
                            try {
                              if (!createdAt) return "Fecha desconocida";
                              const date = new Date(createdAt);
                              if (isNaN(date.getTime())) return "Fecha desconocida";
                              return format(date, "hh:mm a, dd/MM/yy");
                            } catch {
                              return "Fecha desconocida";
                            }
                          })()}
                        </span>
                    </div>
                  );
                })}
                <div ref={messagesEndRef} />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p>No hay mensajes para mostrar</p>
              </div>
            )}
          </ChatBox>
        </ChatBoxWrapper>

        {/* Right side - Search Messages */}
        <ChatBoxWrapper>
          <h3 className="text-lg font-semibold">Buscar mensajes</h3>

          <div className="flex items-center gap-2 text-sm">
            <span>Buscar</span>
            <div>
              <InputGroup>
                <InputGroup.Input
                  type="date"
                  className="!border-none bg-accent"
                  value={searchDate}
                  onChange={handleSearchDateChange}
                />
              </InputGroup>
            </div>
            <button
              className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2 disabled:opacity-50"
              onClick={handleSearch}
              disabled={isSearching}
            >
              {isSearching ? 'Buscando...' : 'Buscar'}
            </button>
          </div>

          <ChatBox>
            {searchDate ? (
              filteredMessages.length > 0 ? (
                <div className="space-y-4">
                  <DividerText textClassName="bg-accent" text={`Resultados para: ${searchDate}`} />
                  {filteredMessages.map((msg, index) => {
                    if (!msg || typeof msg !== 'object') return null;
                    const { id, type, content, sender, createdAt } = msg;
                    let messageBody = null;
                    let badge = null;

                    switch (type) {
                      case 'voice':
                        messageBody = content ? (
                          <audio controls src={content} className="w-full mt-2">
                            Tu navegador no soporta el elemento de audio.
                          </audio>
                        ) : (
                          <span className="italic text-gray-400">Audio no disponible</span>
                        );
                        badge = <span className="ml-2 px-2 py-1 rounded bg-blue-200 text-blue-800 text-xs">Audio</span>;
                        break;
                      case 'sos':
                        messageBody = (
                          <div >
                            <span className="font-bold text-white">SOS:</span> {content?.description || 'Mensaje SOS recibido'}
                          </div>
                        );
                        // badge = <span className="ml-2 px-2 py-1 rounded bg-red-200 text-red-800 text-xs">SOS</span>;
                        break;
                      case 'alert':
                        messageBody = (
                          <div >
                            <span className="font-bold text-white">Alerta:</span> {content?.description || 'Mensaje de alerta recibido'}
                          </div>
                        );
                        // badge = <span className="ml-2 px-2 py-1 rounded bg-yellow-200 text-yellow-800 text-xs">Alerta</span>;
                        break;
                      case 'text':
                      default:
                        messageBody = <span>{content}</span>;
                        break;
                    }

                    return (
                      <div
                        key={id || `search-msg-${index}`}
                        className={`p-4 w-fit rounded bg-white  ${ (type === "sos" || type === "alert") && "bg-red-500 text-white"}`}
                      >
                        <div className="flex flex-col justify-between mb-2 items-start">
                          <div className="flex items-center justify-center gap-2">
                            <span className={`font-semibold  ${(type === "sos" || type === "alert") ? "text-white" : "text-blue-500"}`}>{sender.name}</span>
                            <span className={`${(type === "sos" || type === "alert") ? "text-white" : "text-gray-500"}`}>(ID-{id})</span>
                        </div>
                          {/* {badge} */}
                          {messageBody}
                          <span className={`"text-xs ${(type === "sos" || type === "alert") ?"text-white" : "text-black"} mr-auto"`}>
                            {(() => {
                              try {
                                if (!createdAt) return "Fecha desconocida";
                                const date = new Date(createdAt);
                                if (isNaN(date.getTime())) return "Fecha desconocida";
                                return format(date, "hh:mm a, dd/MM/yy");
                              } catch {
                                return "Fecha desconocida";
                              }
                            })()}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-gray-500">No se encontraron mensajes para la fecha seleccionada</p>
                </div>
              )
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">Selecciona una fecha en el calendario para filtrar los mensajes del chat</p>
              </div>
            )}
          </ChatBox>
        </ChatBoxWrapper>
      </div>
    </ShieldLayout>
  );
}
