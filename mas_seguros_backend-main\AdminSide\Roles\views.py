from django.shortcuts import render
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import JSONParser
from django.http import HttpResponse, JsonResponse
from rest_framework.generics import get_object_or_404
from django.contrib.auth.models import User
from django.contrib.auth.hashers import check_password
from datetime import datetime
from datetime import datetime

from Account.models import UserProfile
from Account import models as account_models
from . import serializers as roles_serializers
from AdminSide import models as adminside_models
from mas_seguros_backend import utils as backend_utils
from Account import utils as account_utils
from Shield import models as shield_models


# Create your views here.


class GetUserProfile(APIView):
    """Get current admin profile"""
    permission_classes = []  # Allow unauthenticated access for admin panel
    serializer_class = roles_serializers.GetUserIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                              msg='Invalid request parameters',
                                              errors=serializer.errors),
                status.HTTP_400_BAD_REQUEST)

        id = request.query_params.get('id')
        try:
            user = account_models.UserProfile.objects.filter(user_id=id).last()
            if user:
                # Get actual admin permissions
                try:
                    permissions = user.admin_permissions
                    admin_permissions = {
                        'users_access': permissions.users_access,
                        'shields_access': permissions.shields_access,
                        'alerts_sos_access': permissions.alerts_sos_access,
                        'payment_history_access': permissions.payment_history_access,
                        'support_access': permissions.support_access,
                        'roles_access': permissions.roles_access,
                        'full_access': permissions.full_access,
                    }
                except adminside_models.AdminPermission.DoesNotExist:
                    # Default permissions if none exist
                    admin_permissions = {
                        'users_access': False,
                        'shields_access': False,
                        'alerts_sos_access': False,
                        'payment_history_access': False,
                        'support_access': False,
                        'roles_access': False,
                        'full_access': False,
                    }

                # Return data structure consistent with GetAllAdmins
                admin_data = {
                    'id': user.id,
                    'full_name': user.full_name,
                    'phone': user.phone,
                    'suspend': user.suspend,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'user': {
                        'email': user.user.email if user.user else None,
                        'first_name': user.user.first_name if user.user else '',
                        'last_name': user.user.last_name if user.user else '',
                        'last_login': user.user.last_login.isoformat() if user.user and user.user.last_login else None
                    },
                    'image_url': user.image_url,
                    'identification_card': user.identification_card,
                    'last_activity': user.user.last_login.strftime('%d/%m/%Y, %H:%M hrs') if user.user and user.user.last_login else 'Nunca',
                    'admin_permissions': admin_permissions,
                    'birth_date': user.birth_date.strftime('%Y-%m-%d') if user.birth_date else None,  # For date input compatibility
                }

                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=admin_data,
                                                   msg='User data'), status.HTTP_200_OK)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=f'No user found with ID: {id}'), status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            print(f"Error in GetUserProfile: {e}")  # Debug print
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                               msg=f'Error retrieving user profile: {str(e)}'), status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetAllAdmins(APIView):
    """Get all administrators"""
    permission_classes = []  # Allow unauthenticated access for admin panel

    def get(self, request):
        try:
            # Get all admin users with their permissions
            admins = account_models.UserProfile.objects.filter(role=account_models.web_admin).select_related('user').prefetch_related('admin_permissions')

            admin_list = []
            for admin in admins:
                # Get admin permissions
                try:
                    permissions = admin.admin_permissions
                    admin_permissions = {
                        'users_access': permissions.users_access,
                        'shields_access': permissions.shields_access,
                        'alerts_sos_access': permissions.alerts_sos_access,
                        'payment_history_access': permissions.payment_history_access,
                        'support_access': permissions.support_access,
                        'roles_access': permissions.roles_access,
                        'full_access': permissions.full_access,
                    }
                except adminside_models.AdminPermission.DoesNotExist:
                    # Default permissions if none exist
                    admin_permissions = {
                        'users_access': False,
                        'shields_access': False,
                        'alerts_sos_access': False,
                        'payment_history_access': False,
                        'support_access': False,
                        'roles_access': False,
                        'full_access': False,
                    }

                # Determine role type - only show SuperAdmin for users with full_access
                role_type = "SuperAdmin" if admin_permissions.get('full_access', False) else "Admin"

                admin_data = {
                    'id': admin.id,
                    'full_name': admin.full_name,
                    'phone': admin.phone,
                    'suspend': admin.suspend,
                    'created_at': admin.created_at.isoformat() if admin.created_at else None,
                    'user': {
                        'email': admin.user.email if admin.user else None,
                        'last_login': admin.user.last_login.isoformat() if admin.user and admin.user.last_login else None
                    },
                    'image_url': admin.image_url,
                    'last_activity': admin.user.last_login.strftime('%d/%m/%Y, %H:%M hrs') if admin.user and admin.user.last_login else 'Nunca',
                    'admin_permissions': admin_permissions,
                    'role_type': role_type,
                    'identification_card': admin.identification_card,  # <-- Added field
                    'birth_date': admin.birth_date.strftime('%Y-%m-%d') if admin.birth_date else None,  # For date input compatibility
                }

                admin_list.append(admin_data)

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=admin_list,
                    msg='Todos los administradores obtenidos exitosamente'
                ),
                status.HTTP_200_OK
            )
        except Exception as e:
            print(f"Error in GetAllAdmins: {e}")  # Debug print
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error obteniendo administradores: {str(e)}'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateAdmin(APIView):
    """Create new administrator"""
    permission_classes = []  # Allow unauthenticated access for admin panel
    serializer_class = roles_serializers.AdminCreateSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)

        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Error guardando datos: campos requeridos vacíos',
                    errors=serializer.errors
                ),
                status.HTTP_400_BAD_REQUEST
            )

        # Extra duplicate check for email and phone
        email = serializer.validated_data['email'].lower()
        phone = serializer.validated_data['phone']
        if User.objects.filter(email=email).exists():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Ya existe un usuario con este correo electrónico.'
                ),
                status.HTTP_400_BAD_REQUEST
            )
        if account_models.UserProfile.objects.filter(phone=phone).exists():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Ya existe un usuario con este número de teléfono.'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            # Create Django User
            user = User.objects.create_user(
                email=email,
                username=email,
                first_name=serializer.validated_data['first_name'],
                last_name=serializer.validated_data['last_name'],
            )
            user.is_active = True
            user.set_password(serializer.validated_data['password'])
            user.save()

            # Create UserProfile
            full_name = f"{serializer.validated_data['first_name']} {serializer.validated_data['last_name']}"
            user_profile = account_models.UserProfile.objects.create(
                user=user,
                phone=phone,
                full_name=full_name,
                identification_card=serializer.validated_data.get('identification_card', ''),
                role=account_models.web_admin,
                email_verified=True,
                verification_code=account_utils.random_digits()
            )

            # Handle image upload if provided
            if 'image' in request.FILES:
                user_profile.image = request.FILES['image']
                user_profile.save()

            # Create AdminPermission with proper permissions
            admin_permission = adminside_models.AdminPermission.objects.create(
                admin=user_profile,
                users_access=serializer.validated_data.get('users_access', False),
                shields_access=serializer.validated_data.get('shields_access', False),
                alerts_sos_access=serializer.validated_data.get('alerts_sos_access', False),
                payment_history_access=serializer.validated_data.get('payment_history_access', False),
                support_access=serializer.validated_data.get('support_access', False),
                roles_access=serializer.validated_data.get('roles_access', False),
                full_access=serializer.validated_data.get('full_access', False)
            )

            # Return created admin data with proper serialization
            response_serializer = roles_serializers.UserProfileSerializer(user_profile)
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_201_CREATED,
                    data=response_serializer.data,
                    msg='Administrador creado exitosamente'
                ),
                status.HTTP_201_CREATED
            )

        except Exception as e:
            # Clean up if user was created but profile failed
            if 'user' in locals() and user.pk:
                user.delete()
            print(f"Error creating admin: {e}")  # Debug print
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error creando administrador: {str(e)}'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UpdateAdmin(APIView):
    """Update administrator profile and permissions"""
    permission_classes = []  # Allow unauthenticated access for admin panel
    serializer_class = roles_serializers.AdminUpdateSerializer

    def post(self, request):
        admin_id = request.data.get('admin_id')
        if not admin_id:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='ID del administrador es requerido'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            admin_profile = account_models.UserProfile.objects.get(id=admin_id, role=account_models.web_admin)
        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    msg='Administrador no encontrado'
                ),
                status.HTTP_404_NOT_FOUND
            )

        # Add admin profile to context for validation
        request.user_profile = admin_profile
        serializer = self.serializer_class(data=request.data, context={'request': request})

        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Error en la validación: complete todos los campos requeridos',
                    errors=serializer.errors
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            # Update User
            user = admin_profile.user
            user.first_name = serializer.validated_data['first_name']
            user.last_name = serializer.validated_data['last_name']
            user.save()

            # Update UserProfile
            full_name = f"{serializer.validated_data['first_name']} {serializer.validated_data['last_name']}"
            admin_profile.full_name = full_name
            admin_profile.phone = serializer.validated_data['phone']
            admin_profile.identification_card = serializer.validated_data.get('identification_card', '')
            # Save birth_date if provided
            if 'birth_date' in serializer.validated_data:
                admin_profile.birth_date = serializer.validated_data['birth_date']

            # Handle image upload if provided
            if 'image' in request.FILES:
                admin_profile.image = request.FILES['image']

            admin_profile.save()

            # Update or create AdminPermission
            admin_permission, created = adminside_models.AdminPermission.objects.get_or_create(
                admin=admin_profile,
                defaults={
                    'users_access': serializer.validated_data.get('users_access', False),
                    'shields_access': serializer.validated_data.get('shields_access', False),
                    'alerts_sos_access': serializer.validated_data.get('alerts_sos_access', False),
                    'payment_history_access': serializer.validated_data.get('payment_history_access', False),
                    'support_access': serializer.validated_data.get('support_access', False),
                    'roles_access': serializer.validated_data.get('roles_access', False),
                    'full_access': serializer.validated_data.get('full_access', False)
                }
            )

            if not created:
                # Update existing permissions
                admin_permission.users_access = serializer.validated_data.get('users_access', False)
                admin_permission.shields_access = serializer.validated_data.get('shields_access', False)
                admin_permission.alerts_sos_access = serializer.validated_data.get('alerts_sos_access', False)
                admin_permission.payment_history_access = serializer.validated_data.get('payment_history_access', False)
                admin_permission.support_access = serializer.validated_data.get('support_access', False)
                admin_permission.roles_access = serializer.validated_data.get('roles_access', False)
                admin_permission.full_access = serializer.validated_data.get('full_access', False)
                admin_permission.save()

            # Return updated admin data
            response_serializer = roles_serializers.UserProfileSerializer(admin_profile)
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=response_serializer.data,
                    msg='Cambios guardados exitosamente'
                ),
                status.HTTP_200_OK
            )

        except Exception as e:
            print(f"Error updating admin: {e}")  # Debug print
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error actualizando administrador: {str(e)}'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SuspendAdmin(APIView):
    """Suspend or unsuspend administrator"""
    permission_classes = []  # Allow unauthenticated access for admin panel

    def post(self, request):
        admin_id = request.data.get('admin_id')
        suspend = request.data.get('suspend', True)  # Default to suspend

        if not admin_id:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Admin ID is required'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            admin_profile = account_models.UserProfile.objects.get(id=admin_id, role=account_models.web_admin)
            admin_profile.suspend = suspend
            admin_profile.save()

            action = "suspendido" if suspend else "reactivado"
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data={'suspended': suspend},
                    msg=f'Administrador {action} exitosamente'
                ),
                status.HTTP_200_OK
            )

        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    msg='Administrator not found'
                ),
                status.HTTP_404_NOT_FOUND
            )


class DeleteAdmin(APIView):
    """Delete administrator"""
    permission_classes = []  # Allow unauthenticated access for admin panel

    def post(self, request):
        admin_id = request.data.get('admin_id')

        if not admin_id:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Admin ID is required'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            admin_profile = account_models.UserProfile.objects.get(id=admin_id, role=account_models.web_admin)
            user = admin_profile.user

            # Delete the user (this will cascade delete the profile)
            user.delete()

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=None,
                    msg='Administrador eliminado exitosamente'
                ),
                status.HTTP_200_OK
            )

        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    msg='Administrator not found'
                ),
                status.HTTP_404_NOT_FOUND
            )


class ChangeAdminPassword(APIView):
    """Change administrator password"""
    permission_classes = []  # Allow unauthenticated access for admin panel
    serializer_class = roles_serializers.ChangePasswordSerializer

    def post(self, request):
        admin_id = request.data.get('admin_id')

        if not admin_id:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Admin ID is required'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            admin_profile = account_models.UserProfile.objects.get(id=admin_id, role=account_models.web_admin)
        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    msg='Administrator not found'
                ),
                status.HTTP_404_NOT_FOUND
            )

        serializer = self.serializer_class(data=request.data)

        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Validation failed',
                    errors=serializer.errors
                ),
                status.HTTP_400_BAD_REQUEST
            )

        # Verify current password
        if not check_password(serializer.validated_data['current_password'], admin_profile.user.password):
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Contraseña inválida o no coincide'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        # Check if new password is different from current
        if check_password(serializer.validated_data['new_password'], admin_profile.user.password):
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='La nueva contraseña no puede ser la contraseña anterior'
                ),
                status.HTTP_400_BAD_REQUEST
            )

        try:
            # Update password
            admin_profile.user.set_password(serializer.validated_data['new_password'])
            admin_profile.user.save()

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=None,
                    msg='Contraseña actualizada exitosamente'
                ),
                status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg='Error updating password'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EditUserProfile(APIView):
    """Legacy view for backward compatibility"""
    permission_classes = []
    serializer_class = roles_serializers.EditProfileSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Validation failed',
                    errors=serializer.errors
                ),
                status.HTTP_400_BAD_REQUEST
            )

        # Update the user profile
        try:
            user_id = request.data.get('id')
            if not user_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='ID de usuario requerido'
                    ),
                    status.HTTP_400_BAD_REQUEST
                )
            user_profile = account_models.UserProfile.objects.get(id=user_id)
            # Update fields
            user_profile.full_name = serializer.validated_data.get('full_name', user_profile.full_name)
            user_profile.phone = serializer.validated_data.get('phone', user_profile.phone)
            user_profile.identification_card = serializer.validated_data.get('identification_card', user_profile.identification_card)
            # Optionally update birth_date if present
            if 'birth_date' in serializer.validated_data:
                user_profile.birth_date = serializer.validated_data['birth_date']
            user_profile.save()
            # Optionally update User email if present
            if 'email' in serializer.validated_data:
                user = user_profile.user
                user.email = serializer.validated_data['email']
                user.save()
            # Return updated profile
            response_serializer = roles_serializers.UserProfileSerializer(user_profile)
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=response_serializer.data,
                    msg='Perfil actualizado exitosamente'
                ),
                status.HTTP_200_OK
            )
        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_404_NOT_FOUND,
                    msg='Usuario no encontrado'
                ),
                status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error actualizando el perfil: {str(e)}'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GetAdminStatistics(APIView):
    """Get statistics for admin dashboard"""
    permission_classes = []  # Allow unauthenticated access for admin panel

    def get(self, request):
        try:
            # Get total shields count
            total_shields = shield_models.ShieldModel.objects.count()

            # Get total members count (all user profiles except admins)
            total_members = account_models.UserProfile.objects.exclude(
                role=account_models.web_admin
            ).count()

            # Get active shields count
            active_shields = shield_models.ShieldModel.objects.filter(
                condition=True, suspend=False
            ).count()

            # Get total users registered this month
            current_month = datetime.now().month
            current_year = datetime.now().year
            monthly_users = account_models.UserProfile.objects.filter(
                created_at__month=current_month,
                created_at__year=current_year
            ).count()

            statistics_data = {
                'total_shields': total_shields,
                'total_members': total_members,
                'active_shields': active_shields,
                'monthly_users': monthly_users,
                'shields_display': total_shields,  # For "Escudos de Empresa"
                'members_display': total_members,  # For "Miembros creados"
            }

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=statistics_data,
                    msg='Statistics retrieved successfully'
                ),
                status.HTTP_200_OK
            )

        except Exception as e:
            print(f"Error in GetAdminStatistics: {e}")  # Debug print
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving statistics: {str(e)}'
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )
