import zipfile
import xml.etree.ElementTree as ET
import re

def extract_text_from_docx(docx_path):
    """Extract text from a .docx file"""
    try:
        # Open the docx file as a zip
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            # Read the main document XML
            doc_xml = zip_file.read('word/document.xml')
            
            # Parse the XML
            root = ET.fromstring(doc_xml)
            
            # Extract text from all text elements
            text_content = []
            
            # Define namespace
            namespace = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
            
            # Find all text elements
            for text_elem in root.iter():
                if text_elem.text and text_elem.text.strip():
                    text_content.append(text_elem.text.strip())
            
            # Join all text with spaces
            full_text = ' '.join(text_content)
            
            # Clean up extra whitespace
            full_text = re.sub(r'\s+', ' ', full_text)
            
            return full_text
            
    except Exception as e:
        print(f"Error extracting text: {e}")
        return None

if __name__ == "__main__":
    docx_file = "admin_panel.docx"
    extracted_text = extract_text_from_docx(docx_file)
    
    if extracted_text:
        # Save to text file
        with open("admin_panel_extracted.txt", "w", encoding="utf-8") as f:
            f.write(extracted_text)
        print("Text extracted successfully!")
        print(f"First 500 characters:\n{extracted_text[:500]}...")
    else:
        print("Failed to extract text")
