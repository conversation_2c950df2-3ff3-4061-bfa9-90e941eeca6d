from django.shortcuts import HttpResponse
from rest_framework import status
from AdminSide.AdminAPi import models as admin_model
from django.http import JsonResponse
from rest_framework.parsers import JSONParser
from django.views.decorators.csrf import csrf_exempt
from Account import models as account_models, serializers as account_user_auth_serializers, utils as account_utils
from mas_seguros_backend import utils as backend_utils
from django.contrib.auth.models import User
from rest_framework.generics import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.permissions import AllowAny
from django.contrib.auth import authenticate
from AdminSide.AdminAPi import serializers as admin_serilaizers, models as admin_models
from AdminSide.CompanyDashboard import models as company_models
from django.contrib.auth.hashers import check_password
from AdminSide import models as adminside_models


@csrf_exempt
def promo_code(request):
    if request.method == "GET":  # get request to fetch the data
        # company_id = request.GET.get('company_id', None)
        # if company_id:
        try:
            company = admin_model.PromoCode.objects.all()
            serializer = admin_serilaizers.PromoCodeSerializer(company, many=True)
            return JsonResponse(serializer.data, safe=False)
        except Exception as e:
            print(f"Error fetching promo codes: {e}")
            return JsonResponse({"error": "Failed to fetch promo codes"}, safe=False)
        # else:
        #     return JsonResponse("Please add 'company_id' to get promo codes", safe=False)
    # post  request post the data
    elif request.method == 'POST':
        data = JSONParser().parse(request)
        serializer = admin_serilaizers.PromoCodeSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return JsonResponse(serializer.data, status=201)
        return JsonResponse(serializer.errors, status=400)


@csrf_exempt
def promo_code_detail(request, pk):
    try:
        id = admin_model.PromoCode.objects.get(pk=pk)
    except id.DoesNotExist:
        return HttpResponse(status=404)
    if request.method == "PATCH":
        data = JSONParser().parse(request)
        serializer = admin_serilaizers.PromoCodeSerializer(id, data=data)
        if serializer.is_valid():
            serializer.save()
            return JsonResponse(serializer.data)
        return JsonResponse(serializer.errors, status=400)

    elif request.method == "DELETE":
        id.delete()
        return HttpResponse("data delete", status=204)


class AdminRegisterApi(APIView):
    serializer_class = account_user_auth_serializers.RegistrationSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        full_name = request.data.get('full_name')
        full_name = full_name.split(' ')
        first_name = full_name[0]
        last_name = full_name[1]
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        check_email_and_phone = account_utils.validate_email_phone(request.data.get('email'), request.data.get('phone'))
        if check_email_and_phone[0]:
            user = User.objects.create_user(email=request.data.get('email'),
                                            username=request.data.get('email'),
                                            first_name=first_name,
                                            last_name=last_name,
                                            )
            user.is_active = True
            user.set_password(request.data.get('password'))
            user.save()
            user_profile = account_models.UserProfile.objects.create(user=user, identification_card=request.data.get(
                'identification_card'), phone=request.data.get('phone'),
                                                                     full_name=request.data.get('full_name'),
                                                                     verification_code=account_utils.random_digits(),
                                                                     role=account_models.web_admin, email_verified=True)

            user_profile.save()

            # Create AdminPermission object for the new user
            admin_permission = admin_models.AdminPermission.objects.create(
                admin=user_profile,
                users_access=False,
                shields_access=False,
                alerts_sos_access=False,
                payment_history_access=False,
                support_access=False,
                roles_access=False,
                full_access=False
            )

            token = Token.objects.get_or_create(user=user)[0].key
            get_user = get_object_or_404(User, email=user.username)
            user_profile_serializer = admin_serilaizers.AminUserProfileSerializer(user_profile)
            user_profile_serializer = user_profile_serializer.data
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=user_profile_serializer,
                                               msg='Admin Registered Successfully'), status=status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=check_email_and_phone[1]), status=status.HTTP_400_BAD_REQUEST)


class AdminLoginApi(APIView):
    permission_classes = (AllowAny,)
    serializer_class = admin_serilaizers.AdminLoginSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            return self.on_valid_request_data(serializer.validated_data, request)
        else:
            # Extract the first error message for better user experience
            error_message = "Error de validación"
            if serializer.errors:
                # Get the first error from any field
                for field, errors in serializer.errors.items():
                    if isinstance(errors, list) and errors:
                        error_message = errors[0]
                        break
                    elif isinstance(errors, str):
                        error_message = errors
                        break

            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg=error_message
                ),
                status=status.HTTP_400_BAD_REQUEST
            )

    def on_valid_request_data(self, data, request):
        email = data.get('email_address')
        password = data.get('password')

        user = authenticate(request=request, username=email, password=password)

        if user is not None:
            try:
                user_profile = account_models.UserProfile.objects.filter(user=user).last()
                if not user_profile.role == account_models.web_admin and not user.is_superuser:
                    return Response(
                        backend_utils.failure_response(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            msg="¡Admin no encontrado!"
                        ),
                        status.HTTP_400_BAD_REQUEST
                    )

                # Check if admin is suspended
                if user_profile.suspend:
                    return Response(
                        backend_utils.failure_response(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            msg='Cuenta suspendida. Contacte al administrador.'
                        ),
                        status.HTTP_401_UNAUTHORIZED
                    )

                # Fix unused variables and improve exception handling
                admin_permissions = {}
                if user.is_superuser:
                    admin_permissions = {
                        'users_access': True,
                        'shields_access': True,
                        'alerts_sos_access': True,
                        'payment_history_access': True,
                        'support_access': True,
                        'roles_access': True,
                        'full_access': True,
                    }
                else:
                    try:
                        permissions, created = adminside_models.AdminPermission.objects.get_or_create(admin=user_profile,
                            defaults={
                                'users_access': False,
                                'shields_access': False,
                                'alerts_sos_access': False,
                                'payment_history_access': False,
                                'support_access': False,
                                'roles_access': False,
                                'full_access': False,
                            }
                        )
                        admin_permissions = {
                            'users_access': permissions.users_access,
                            'shields_access': permissions.shields_access,
                            'alerts_sos_access': permissions.alerts_sos_access,
                            'payment_history_access': permissions.payment_history_access,
                            'support_access': permissions.support_access,
                            'roles_access': permissions.roles_access,
                            'full_access': permissions.full_access,
                        }
                    except Exception as e:
                        return Response(
                            backend_utils.failure_response(
                                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                msg="Error al obtener permisos de administrador"
                            ),
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR
                        )

                user_profile_serializer = account_user_auth_serializers.UserProfileSerializer(user_profile)
                user_profile_serializer = user_profile_serializer.data
                user_profile_serializer['admin_permissions'] = admin_permissions

                token = Token.objects.get_or_create(user=user)[0].key
                response = {
                    'token': token,
                    'user_profile': user_profile_serializer
                }
                return Response(
                    backend_utils.success_response(
                        status_code=status.HTTP_200_OK,
                        data=response,
                        msg='El administrador inició sesión correctamente'
                    )
                )
            except account_models.UserProfile.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg="Correo electrónico o contraseña incorrectos"
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            except Exception as e:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        msg="Error interno del servidor"
                    ),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return Response(
            backend_utils.failure_response(
                status_code=status.HTTP_400_BAD_REQUEST,
                msg="Correo electrónico o contraseña incorrectos"
            ),
            status.HTTP_400_BAD_REQUEST
        )


# get all admins

class GetadminUsers(APIView):
    # TODO: This APi is for DJango Super Admin to get all the comapanies Register in the system.
    # permission_classes = (IsAuthenticated,)
    permission_classes = (AllowAny,)

    def get(self, request):
        data = company_models.CompanyProfileModel.objects.all()
        user_profile_serializer = admin_serilaizers.AdminSerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=user_profile_serializer.data,
                                           msg='All admins'), status.HTTP_200_OK)


# change password


class AllCompanyPromoCodes(APIView):
    # permission_classes = (IsAuthenticated,)

    def get(self, request):
        company: company_models.CompanyProfileModel
        all_promo_codes = admin_models.PromoCode.objects.all()
        serializer = admin_serilaizers.PromoCodeSerializer(all_promo_codes, many=True)
        # return JsonResponse(serializer.data, safe=False)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=serializer.data,
                                           msg='All Promo Codes'), status.HTTP_200_OK)


class ChangePassword(APIView):
    permission_classes = (AllowAny,)
    serializer_class = account_user_auth_serializers.SetNewPasswordSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        password = request.data.get('new_password')
        email = request.data.get('email')
        try:
            user_profile = account_utils.get_user_profile(email)
            matchcheck = check_password(password, user_profile.user.password)
            if not matchcheck:
                user_profile.user.set_password(password)
                user_profile.user.save()
                user_profile.verification_code = account_utils.random_digits()
                user_profile.save()
                message = "Password Reset Successfully!"
                return Response(
                    backend_utils.success_response(
                        status_code=status.HTTP_200_OK,
                        data=None,
                        msg=message
                    )
                )
            else:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='La nueva contraseña no puede ser la contraseña anterior'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )
        except account_models.UserProfile.DoesNotExist:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='Usuario no encontrado'
                ),
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg='Error interno del servidor'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminPasswordRecovery(APIView):
    permission_classes = (AllowAny,)
    serializer_class = admin_serilaizers.AdminPasswordRecoverySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            email = serializer.validated_data.get('email')
            try:
                user = User.objects.get(email__iexact=email)
                user_profile = user.userprofile

                # Check if admin is suspended
                if user_profile.suspend:
                    return Response(
                        backend_utils.failure_response(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            msg='Cuenta suspendida. Contacte al administrador.'
                        ),
                        status.HTTP_401_UNAUTHORIZED
                    )

                # Generate a random password
                new_password = account_utils.generate_random_password()

                # Set the new password for the user
                user.set_password(new_password)
                user.save()

                # Update verification code (for compatibility with existing code)
                user_profile.verification_code = account_utils.random_digits()
                user_profile.save()

                # Try to send email with the new password
                email_sent = backend_utils.send_email(
                    "Nueva contraseña para Mas Seguros - Panel Administrativo",
                    {
                        'first_name': user.first_name or 'Administrador',
                        'username': user.username,
                        'message': 'Tu nueva contraseña para acceder al panel de administración.'
                    },
                    user,
                    new_password=new_password
                )

                if email_sent:
                    return Response(backend_utils.success_response(
                        msg='Enviamos la nueva contraseña a tu correo'
                    ))
                else:
                    return Response(backend_utils.success_response(
                        msg='La nueva contraseña ha sido generada. Contacta al soporte si no recibes el correo.'
                    ))

            except User.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg="Correo electrónico no encontrado"
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg="Error al procesar la solicitud"
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # Extract the first error message for better user experience
            error_message = "Error de validación"
            if serializer.errors:
                for field, errors in serializer.errors.items():
                    if isinstance(errors, list) and errors:
                        error_message = errors[0]
                        break
                    elif isinstance(errors, str):
                        error_message = errors
                        break

            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg=error_message
                ),
                status=status.HTTP_400_BAD_REQUEST
            )