import { createElement, useState } from "react";
import ProfilePicture from "./ProfilePicture";
import Modal from "./utility/Modal";

const ViewPhotoBtn = ({
  as = "button",
  headerTitle = "",
  user = {},
  dp = '',
  evidenceImage = null, // New prop for biometric evidence
  ...props
}) => {
  const [open, setOpen] = useState(false);
  const [imageError, setImageError] = useState(false);

  const close = () => {
    setOpen(false);
    setImageError(false);
  };

  // Construct proper profile picture URL
  const getProfilePictureUrl = () => {
    if (user.dp) return user.dp;
    if (dp) return dp;
    if (user.userprofile?.image_url) {
      // If it's already a full URL, use it directly
      if (user.userprofile.image_url.startsWith('http')) {
        return user.userprofile.image_url;
      }
      // Otherwise, construct the full URL
      return `${process.env.NEXT_PUBLIC_BACKEND_URL}${user.userprofile.image_url}`;
    }
    return '/assets/img/default-profile-pic-1.jpg';
  };

  // Get the evidence image (for biometric photos)
  const getEvidenceImage = () => {
    if (evidenceImage) return evidenceImage;
    if (user.avatar) return user.avatar;
    return null;
  };

  const profilePicture = getProfilePictureUrl();
  const evidencePhoto = getEvidenceImage();

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-md overflow-hidden rounded bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">{headerTitle}</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="space-y-7 py-5">
            <div className="flex gap-3 text-sm">
              <ProfilePicture
                src={profilePicture}
                className="inline-block h-11 w-11 rounded-full"
              />
              <div>
                <dd className="font-semibold">{user.name || user.full_name || 'Usuario'}</dd>
                <dd className="text-gray-600">ID: {user.id || user.user_id || 'N/A'}</dd>
                {user.biometric_code && (
                  <dd className="text-sm text-gray-500">Código: {user.biometric_code}</dd>
                )}
              </div>
            </div>
            <div className="flex justify-center">
              {imageError || !evidencePhoto ? (
                <div className="flex aspect-square w-full items-center justify-center bg-gray-100 text-center text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                  <div className="text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p className="mt-2 text-sm">No hay imagen disponible</p>
                    <p className="text-xs text-gray-400">La evidencia no pudo ser cargada</p>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <img
                    src={evidencePhoto}
                    className="block max-h-96 w-auto object-contain rounded-lg shadow-lg"
                    onError={() => setImageError(true)}
                    onLoad={() => console.log("Evidence image loaded successfully")}
                    alt="Evidencia Biométrica"
                  />
                  <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    Evidencia Biométrica
                  </div>
                </div>
              )}
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-black text-white">
              Cerrar
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        ...props,
        onClick: () => setOpen(true),
      })}
    </>
  );
};

export default ViewPhotoBtn;
