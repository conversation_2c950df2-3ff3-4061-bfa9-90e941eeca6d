import React from "react";
import Table from "../Table";
import Link from "next/link";
import { format } from "date-fns";
import classNames from "classnames";

const PaymentMembershipsTable = ({
  memberships = [],
  isLoading,
  isError,
  error,
  sort,
  setSort
}) => {

  return (
    <Table
      dataCount={memberships.length}
      isLoading={isLoading}
      isError={isError}
      error={error}
    >
      <Table.Thead>
        <Table.Tr>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="order_id">ID Orden</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="date">Fecha</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="transaction_id">ID transacción</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="membership">Membresía</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="total_amount">Monto</Table.Th>
          <Table.Th>ID usuario</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable={true} name="conditions">Estado</Table.Th>
          <Table.Th>Acción</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {!isLoading && !isError && memberships && memberships.length > 0 &&
          memberships.map((membership) => (
            <Row membership={membership} key={membership.id} />
          ))
        }
      </Table.Tbody>
    </Table>
  );
};
const Row = ({ membership }) => {
  // Add safety check for membership data
  if (!membership) {
    console.warn("Membership data is missing");
    return null;
  }

  // Use the new user_id field from serializer or fallback to nested lookup
  const userId = membership.user_id ||
                 membership.userprofile?.user?.id ||
                 membership.userprofile?.id ||
                 "N/A";

  // Use display fields from serializer if available
  const membershipDisplay = membership.membership_display || membership.membership || "N/A";
  const conditionsDisplay = membership.conditions_display || membership.conditions || "N/A";
  const formattedDate = membership.formatted_date ||
                       (membership.date ? format(new Date(membership.date), "d/MM/yy") : "N/A");

  return (
    <Table.Tr>
      <Table.Td>{membership.order_id || "N/A"}</Table.Td>
      <Table.Td>{formattedDate}</Table.Td>
      <Table.Td>{membership.transaction_id || "N/A"}</Table.Td>
      <Table.Td>{membershipDisplay}</Table.Td>
      <Table.Td>$ {membership.total_amount || 0}</Table.Td>
      <Table.Td>{userId}</Table.Td>
      <Table.Td className={classNames(
        "font-semibold",
        (membership.conditions?.toLowerCase() === 'effected' || membership.conditions?.toLowerCase() === 'efectuado') && "text-success",
        (membership.conditions?.toLowerCase() === 'failed' || membership.conditions?.toLowerCase() === 'fallido') && 'text-danger'
      )}>
        {conditionsDisplay}
      </Table.Td>
      <Table.Td className="font-semibold">
        <Link
          href={`/payment-memberships/${membership.id}`}
          className="font-semibold text-primary hover:underline"
        >
          Ver detalles
        </Link>
      </Table.Td>
    </Table.Tr>
  );
};

export default PaymentMembershipsTable;
