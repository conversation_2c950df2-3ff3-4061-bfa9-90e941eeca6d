from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from Account import models as account_models

class MultiAppAuthenticationBackend(ModelBackend):
    """
    Custom authentication backend that supports both admin panel and mobile app authentication
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        # Check if email_address is provided (admin panel login)
        email_address = kwargs.get('email_address')
        if email_address:
            try:
                user = User.objects.get(email=email_address)
                if user.check_password(password):
                    return user
            except User.DoesNotExist:
                return None
                
        # Check if phone is provided (mobile app login)
        phone = kwargs.get('phone')
        if phone:
            try:
                user_profile = account_models.UserProfile.objects.get(phone=phone)
                user = user_profile.user
                if user.check_password(password):
                    return user
            except account_models.UserProfile.DoesNotExist:
                return None
                
        # Standard Django authentication
        if username:
            try:
                # Try with email first (since we're using email as username)
                if '@' in username:
                    user = User.objects.get(email=username)
                else:
                    user = User.objects.get(username=username)
                
                if user.check_password(password):
                    return user
            except User.DoesNotExist:
                return None
                
        return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None