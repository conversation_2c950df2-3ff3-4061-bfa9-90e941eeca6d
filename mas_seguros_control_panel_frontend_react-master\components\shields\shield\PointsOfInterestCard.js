import React, { useMemo } from "react";
import GoogleMap from "@/components/maps/GoogleMap";

// Custom map component that displays multiple POI markers using Google Maps
const MultiPOIMap = ({ markers, center }) => {
  if (!center || !center.lat || !center.lng) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-secondary">
        <div className="text-4xl mb-2">📍</div>
        <p className="text-center text-sm">No hay puntos de interés con coordenadas válidas</p>
      </div>
    );
  }

  // Prepare markers for Google Maps
  const googleMarkers = markers && markers.length > 0 ? markers.map((marker, index) => ({
    lat: marker.lat,
    lng: marker.lng,
    title: marker.title || `Punto ${index + 1}`,
    infoContent: `
      <div>
        <strong>${marker.title || `Punto ${index + 1}`}</strong><br>
        ${marker.address || 'Dirección no disponible'}
        ${marker.isSelected ? '<br><em style="color: #2563eb;">Punto seleccionado</em>' : ''}
      </div>
    `,
    icon: {
      url: marker.isSelected
        ? 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
        : 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
      scaledSize: { width: 32, height: 32 }
    }
  })) : [];

  // Create external map URL for Google Maps
  const externalMapUrl = `https://www.google.com/maps?q=${center.lat},${center.lng}&z=13`;

  return (
    <div className="relative w-full h-full">
      <GoogleMap
        lat={center.lat}
        lng={center.lng}
        zoom={14}
        className="w-full h-full"
        markers={googleMarkers}
        showMarker={false} // Don't show the main marker since we have POI markers
      />

      {/* Info overlay */}
      <div className="absolute bottom-2 left-2 bg-white bg-opacity-90 rounded px-2 py-1 text-xs text-gray-700">
        {markers.length} punto{markers.length !== 1 ? 's' : ''} de interés
      </div>

      {/* Link to open in external map */}
      <a
        href={externalMapUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors"
      >
        Ver en Google Maps
      </a>
    </div>
  );
};

const PointsOfInterestCard = ({ items, selectedPointId, onPointSelect }) => {
  // Ensure items is an array
  const safeItems = Array.isArray(items) ? items : [];

  // Process POI data for map display
  const mapData = useMemo(() => {
    const validPOIs = safeItems.filter(item =>
      item?.poi_lat &&
      item?.poi_long &&
      !isNaN(parseFloat(item.poi_lat)) &&
      !isNaN(parseFloat(item.poi_long)) &&
      parseFloat(item.poi_lat) >= -90 &&
      parseFloat(item.poi_lat) <= 90 &&
      parseFloat(item.poi_long) >= -180 &&
      parseFloat(item.poi_long) <= 180
    );

    if (validPOIs.length === 0) {
      return { markers: [], center: null, hasValidData: false };
    }

    const markers = validPOIs.map(item => ({
      id: item.id,
      lat: parseFloat(item.poi_lat),
      lng: parseFloat(item.poi_long),
      title: item.poi_tag_name || `Punto ${item.id}`,
      address: item.poi_address || 'Dirección no disponible',
      isSelected: selectedPointId === item.id
    }));

    // Calculate center point
    const avgLat = markers.reduce((sum, marker) => sum + marker.lat, 0) / markers.length;
    const avgLng = markers.reduce((sum, marker) => sum + marker.lng, 0) / markers.length;

    return {
      markers,
      center: { lat: avgLat, lng: avgLng },
      hasValidData: true
    };
  }, [safeItems, selectedPointId]);

  return (
    <div className="flex h-[800px] flex-col bg-white">
      {/* Map Section */}
      <div className="aspect-[570/420] bg-accent relative overflow-hidden">
        {mapData.hasValidData ? (
          <MultiPOIMap
            markers={mapData.markers}
            center={mapData.center}
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-secondary">
            <div className="text-4xl mb-2">📍</div>
            <p className="text-center text-sm">No hay puntos de interés con coordenadas válidas</p>
          </div>
        )}
      </div>

      {/* POI List Section */}
      <div className="flex flex-grow flex-col gap-3.5 p-5">
        <h3 className="text-lg font-medium">Puntos de Interés</h3>
        <ul className="h-0 flex-grow space-y-1.5 overflow-auto bg-accent p-3">
          {safeItems.length > 0 ? (
            safeItems.map((item) => (
              <li
                key={item?.id || Math.random()}
                className={`bg-white px-4 py-3 rounded cursor-pointer transition-colors ${
                  selectedPointId === item?.id ? 'ring-2 ring-primary bg-blue-50' : 'hover:bg-gray-50'
                }`}
                onClick={() => onPointSelect && onPointSelect(item?.id)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-1">
                    <h5 className="text-sm font-medium">
                      {item?.poi_tag_name || `Punto ${item?.id || 'N/A'}`}
                    </h5>
                    <p className="text-xs text-secondary mt-1">
                      {item?.poi_address || 'Dirección no disponible'}
                    </p>
                    {item?.poi_lat && item?.poi_long && (
                      <p className="text-xs text-gray-500 mt-1">
                        {parseFloat(item.poi_lat).toFixed(6)}, {parseFloat(item.poi_long).toFixed(6)}
                      </p>
                    )}
                  </div>
                  {item?.poi_lat && item?.poi_long && (
                    <a
                      href={`https://www.google.com/maps?q=${item.poi_lat},${item.poi_long}&z=15`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-primary hover:underline ml-2 flex-shrink-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      Ver en Google Maps
                    </a>
                  )}
                  <input
                    type="radio"
                    name="selectedPoint"
                    value={item?.id}
                    checked={selectedPointId === item?.id}
                    onChange={() => onPointSelect && onPointSelect(item?.id)}
                    className="mt-1 text-primary focus:ring-primary"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </li>
            ))
          ) : (
            <li className="bg-white px-4 py-3 text-center rounded">
              {safeItems.length === 0 ? "Este escudo no tiene puntos de interés definidos." : "¡No hay datos en el período de tiempo seleccionado!"}
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default PointsOfInterestCard;
