import io
from django.contrib.auth.decorators import user_passes_test
from mas_seguros_backend import settings as backend_setting
from django.db.models import Prefetch, Q
from django.shortcuts import HttpResponse
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from mas_seguros_backend.decorator import check_member_in_shield
from rest_framework import status
from rest_framework.generics import get_object_or_404
from Account import models as account_models
from Membership import models as membership_models
from . import models as shield_models, serializers as shield_serialziers, utils as shield_utils
from Alert import models as alert_models
from django.db.models import Sum
from rest_framework.parsers import JSONParser
from django.views.decorators.csrf import csrf_exempt
from Ticket import models as ticket_models
from django.http import JsonResponse
import random
import string
import csv
from django.http import FileResponse
from reportlab.pdfgen import canvas
from django.core.files.storage import default_storage
from django.http import HttpResponse
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle


# Create your views here.

class ShieldBiometricsReport(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldBiometricsReportSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('shield_id')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        report_type = request.data.get('report_type')
        member_ids = request.data.get('member_ids')
        my_member = [int(x) for x in member_ids]

        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            biometric_objs = shield_models.Biometric.objects.filter(shield=shield_obj).filter(
                userprofile__user_id__in=my_member).filter(created_at__gte=start_date).filter(
                created_at__lte=end_date).order_by('userprofile')
            if biometric_objs:
                if report_type == 'csv':
                    file_name = 'media/biometric_reports/mobileside/{}'.format(
                        ''.join(random.choices(string.ascii_uppercase +
                                               string.digits,
                                               k=7)) + '.csv')
                    with open(file_name, 'w', newline='') as file:
                        writer = csv.writer(file)
                        writer.writerow(
                            ["Shield_name", "Member_name", "Biometric_code", "Image", "lat", "long", "address", "Type"])
                        for biometric in biometric_objs:
                            writer.writerow(
                                [shield_obj.shield_name, biometric.userprofile.name, biometric.biometric_code,
                                 biometric.image_url, biometric.lat, biometric.long, biometric.address, biometric.type])
                    new_file = '/' + file_name
                    done_file = backend_setting.Base_url_path.format(url=new_file)
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data=done_file,
                                                       msg='All Reports of Biometric'), status.HTTP_200_OK)
                elif report_type == 'pdf':
                    queryset = biometric_objs
                    buffer = io.BytesIO()
                    doc = SimpleDocTemplate(buffer, pagesize=letter)
                    styles = getSampleStyleSheet()
                    style_heading = styles["Heading1"]
                    style_body = styles["Normal"]
                    data = []
                    headings = ['User', 'Biometric Code', 'Shield', 'Address', 'Type']
                    data.append(headings)
                    for obj in queryset:
                        row = []
                        row.append(obj.userprofile.user.get_full_name())
                        row.append(obj.biometric_code)
                        row.append(obj.shield.shield_name)
                        row.append(obj.address)
                        row.append(obj.type)
                        data.append(row)
                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), '#c8c8c8'),
                        ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
                        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 14),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
                        ('TEXTCOLOR', (0, 1), (-1, -1), '#000000'),
                        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 12),
                        ('BOTTOMPADDING', (0, 1), (-1, -1), 10),
                    ]))

                    # Add the table to the PDF
                    elements = []
                    elements.append(Paragraph("Biometric Report", style_heading))
                    elements.append(Spacer(1, 0.25 * inch))
                    elements.append(table)
                    doc.build(elements)
                    file_name = 'biometric_reports/mobileside/{}'.format(''.join(random.choices(string.ascii_uppercase +
                                                                                                string.digits,
                                                                                                k=7)) + '.pdf')
                    default_storage.save(file_name, buffer)
                    file_url = default_storage.url(file_name)
                    new_url = backend_setting.Base_url_path.format(url=file_url)
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data=new_url,
                                                       msg='All Reports of Biometric'), status.HTTP_200_OK)
                else:
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                       msg='Invalid Report Type sent'), status.HTTP_400_BAD_REQUEST)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg='no biometric found'), status.HTTP_400_BAD_REQUEST)


        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMemberLocationsBattery(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_objs = shield_obj.members.all()
            result = []
            for member in member_objs:
                member_detail = {}
                member_detail['name'] = member.full_name
                member_last_location = shield_models.Location.objects.filter(userprofile=member).last()
                member_last_location_lat = member_last_location.lat
                member_last_location_long = member_last_location.long
                shields_poi = shield_models.ShieldModel.objects.filter(id=shield_id).last().prefetch_related(
                    Prefetch('location'),
                    queryset=shield_models.PointsOfInterest.objects.filter(poi_lat=member_last_location_lat).filter(
                        poi_long=member_last_location_long))
                if shields_poi.location.all():
                    member_detail['user_location'] = 'at ' + shields_poi.location.poi_tag_name
                else:
                    member_detail['user_location'] = 'on Route'
                member_detail['time'] = member_last_location.created_at
                member_detail['battery'] = shield_models.Route.objects.filter(member=member).last()
                result.append(member_detail)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=result,
                                               msg='All members details'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldPending(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        shield_requester_objs = shield_models.ShieldJoinRequest.objects.filter(
            shield_requester=request.user.userprofile, status=shield_models.pending)
        result = list()
        for shield_obj in shield_requester_objs:
            shield_details = {}
            shield_details['id'] = shield_obj.shield.id
            shield_details['name'] = shield_obj.shield.shield_name
            shield_details['shield_code'] = shield_obj.shield.shield_code
            shield_details['status'] = shield_obj.status
            shield_details['image'] = shield_obj.shield.logo_url
            result.append(shield_details)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=result,
                                           msg='Requesters to join this shield'), status.HTTP_200_OK)


class ShieldUpdateJoiningStatus(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldUpdateJoiningStatusSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('shield_id')
        requester_id = request.data.get('requester_id')
        new_status = request.data.get('status')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        update_obj = shield_models.ShieldJoinRequest.objects.filter(shield_id=shield_id).filter(
            shield_requester_id=requester_id, shield_super_admin=request.user.userprofile,
            status=shield_models.pending).last()

        if update_obj:
            if new_status.lower() == 'reject':
                print("====REJECTED===")
                update_obj.status = shield_models.rejected
                update_obj.save()
            if new_status.lower() == 'approved':
                update_obj.status = shield_models.approved
                update_obj.save()
                shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
                member_obj = account_models.UserProfile.objects.filter(id=requester_id).last()
                walkie_talkie_obj = shield_models.WalkieTalkie.objects.create(shield=shield_obj, member=member_obj)
                shield_obj.members.add(member_obj)
                shield_utils.add_hierarchy_of_member(shield_obj, member_obj)
            return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                           msg='status updated'), status.HTTP_200_OK)
        else:
            return Response(backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                           msg='Incorrect information provided for updating status'),
                            status.HTTP_400_BAD_REQUEST)


class ShieldRequesters(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield_requester_objs = shield_models.ShieldJoinRequest.objects.filter(shield_id=shield_id).filter(
            shield_super_admin=request.user.userprofile).filter(status=shield_models.pending)
        result = list()
        for requester in shield_requester_objs:
            requester_details = {}
            requester_details['id'] = requester.shield_requester.id
            requester_details['name'] = requester.shield_requester.full_name
            requester_details['phone'] = requester.shield_requester.phone
            requester_details['image'] = str(requester.shield_requester.image)
            result.append(requester_details)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=result,
                                           msg='Requesters to join this shield'), status.HTTP_200_OK)


class ShieldDetailsAgainstJoiningCode(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldJoiningCodeSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        code = request.query_params.get('shield_joining_code')
        shield_obj = shield_models.ShieldModel.objects.filter(shield_joining_code=code).last()
        if shield_obj:
            details = {}
            details["id"] = shield_obj.id
            details["shield_name"] = shield_obj.shield_name
            details["code"] = shield_obj.shield_code
            details["shield_super_admin"] = shield_obj.shield_super_admin.full_name
            return Response(details, status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No shield of this code found'), status.HTTP_400_BAD_REQUEST)


class ShieldPointOfInterest(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer
    serializer_class_post = shield_serialziers.AddPointOfInterestShieldSerializer
    serializer_class_put = shield_serialziers.EditPointOfInterestShieldSerializer
    serializer_class_delete = shield_serialziers.GetPointOfInterestIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        check_shield_exists = shield_utils.check_shield_exist(id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        if shield:
            point_of_interest = shield.locations.all()

            point_of_interest_serializer = shield_serialziers.PointOfInterestSerializer(point_of_interest, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=point_of_interest_serializer.data,
                                               msg='All point of Interests of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        # Add Point of Interest in shield
        serializer = self.serializer_class_post(data=request.data)
        serializer.is_valid(raise_exception=True)
        check_shield_exists = shield_utils.check_shield_exist(request.data.get('id'))
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(request.data.get('id'), request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        id = request.data.get('id')
        location = request.data.get('point_of_interest_location')
        lat = request.data.get('point_of_interest_lat')
        long = request.data.get('point_of_interest_long')
        tag = request.data.get('point_of_interest_tag')
        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        if shield:
            check_point_of_interest_exists = shield_utils.check_point_of_interest_exists(shield, request)
            print("this is final resposen====", check_point_of_interest_exists)
            if not check_point_of_interest_exists[0]:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg=check_point_of_interest_exists[1]), status.HTTP_400_BAD_REQUEST)
            point_of_interest = shield_models.PointsOfInterest.objects.create(poi_address=location,
                                                                              poi_tag_name=tag,
                                                                              poi_lat=lat,
                                                                              poi_long=long)
            shield.locations.add(point_of_interest)
            point_of_interest = shield.locations.all()

            point_of_interest_serializer = shield_serialziers.PointOfInterestSerializer(point_of_interest, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=point_of_interest_serializer.data,
                                               msg=' point of Interests of this shield Added Successfully'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        # Update Point of Interest in shield
        serializer = self.serializer_class_put(data=request.data)
        serializer.is_valid(raise_exception=True)
        check_shield_exists = shield_utils.check_shield_exist(request.data.get('id'))
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(request.data.get('id'), request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        id = request.data.get('poi_id')
        location = request.data.get('point_of_interest_location')
        lat = request.data.get('point_of_interest_lat')
        long = request.data.get('point_of_interest_long')
        tag = request.data.get('point_of_interest_tag')
        point_of_interest = shield_models.PointsOfInterest.objects.filter(id=id)
        point_of_interest1 = point_of_interest.last()
        shield = shield_models.ShieldModel.objects.filter(id=request.data.get('id')).last()
        if point_of_interest:
            check_point_of_interest_exists = shield_utils.check_point_of_interest_exists_edit(shield, request,
                                                                                              point_of_interest1)
            if not check_point_of_interest_exists[0]:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg=check_point_of_interest_exists[1]), status.HTTP_400_BAD_REQUEST)
            point_of_interest.update(poi_address=location, poi_tag_name=tag, poi_lat=lat, poi_long=long)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg=' point of Interests of this shield Updated Successfully'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        # Update Point of Interest in shield
        serializer = self.serializer_class_delete(data=request.data)
        serializer.is_valid(raise_exception=True)

        check_shield_exists = shield_utils.check_shield_exist(request.data.get('id'))
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(request.data.get('id'), request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        id = request.data.get('poi_id')
        point_of_interest = shield_models.PointsOfInterest.objects.filter(id=id).last()
        if point_of_interest:
            shield_models.PointsOfInterest.objects.filter(id=id).last().delete()

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg=' point of Interests of this shield Deleted Successfully'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class PointOfInterestVisitHistory(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            poi_id = request.query_params.get('poi_id')
            date_filter = request.query_params.get('date')

            if not poi_id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='poi_id parameter is required'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get the point of interest
            try:
                poi = shield_models.PointsOfInterest.objects.get(id=poi_id)
            except shield_models.PointsOfInterest.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='Point of interest not found'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get visit history for this POI
            visits = shield_models.Location.objects.filter(
                point_of_interest=poi
            ).select_related('userprofile', 'userprofile__user').order_by('-created_at')

            # Apply date filter if provided
            if date_filter:
                try:
                    from datetime import datetime
                    filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                    visits = visits.filter(created_at__date=filter_date)
                except ValueError:
                    return Response(
                        backend_utils.failure_response(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            msg='Invalid date format. Use YYYY-MM-DD'
                        ),
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Serialize the visit data
            visit_data = []
            for visit in visits:
                visit_data.append({
                    'id': visit.id,
                    'member_name': visit.userprofile.full_name if visit.userprofile else 'Unknown',
                    'member_id': visit.userprofile.id if visit.userprofile else None,
                    'date': visit.created_at.strftime('%d/%m/%Y') if visit.created_at else '',
                    'time': visit.created_at.strftime('%H:%M') if visit.created_at else '',
                    'created_at': visit.created_at.isoformat() if visit.created_at else None,
                })

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=visit_data,
                    msg='Point of interest visit history retrieved successfully'
                )
            )

        except Exception as e:
            print(f"Error in PointOfInterestVisitHistory: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f'Error retrieving visit history: {str(e)}'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ShieldMembers(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        id = request.data.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        if shield:
            members = shield.members.all()
            members_serializer = shield_serialziers.UserProfileSerializerForDashboard(members, many=True)
            return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                           data=members_serializer.data,
                                                           msg='All Members of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMemberLocations(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetMemberIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        # shield_id = request.data.get('shield_id')
        member_id = request.query_params.get('member_id')
        # shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if member_id:
            locations = shield_models.Location.objects.filter(userprofile__user_id=member_id)
            # routes = routes.filter(member_id=member_id)
            members_serializer = shield_serialziers.ShieldMemberLocationSerializer(locations, many=True)
            return JsonResponse(members_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=members_serializer.data,
            #                                msg='All location of this member'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Member of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMemberRoutes(APIView):
    permission_classes = (IsAuthenticated,)
    # serializer_class = shield_serialziers.GetShieldIdAndMemberIDSerializer
    serializer_class = shield_serialziers.GetMemberIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        # shield_id = request.query_params.get('shield_id')
        member_id = request.query_params.get('member_id')
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        shield = shield_models.ShieldModel.objects.filter(id=member_id, date__month=month, date__year=year).last()
        if shield:
            routes = shield.route.all()
            # routes = routes.filter(member__user_id=member_id)
            routes_serializer = shield_serialziers.ShieldMemberRouteSerializer(routes, many=True)
            return JsonResponse(routes_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=routes_serializer.data,
            #                                msg='All routes of this member in this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldAlertAndSos(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            alerts = shield.alert.all()
            routes_serializer = shield_serialziers.ShieldAlertSerializer(alerts, many=True)
            return JsonResponse(routes_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=routes_serializer.data,
            #                                msg='All alerts of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldBiometrics(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer
    post_serializer = shield_serialziers.BiometricUserShieldSerializer

    def post(self, request):
        serializer = self.post_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('shield_id')
        user_id = request.user.id
        lat = request.data.get('lat')
        long = request.data.get('long')
        user_pic = request.data.get('user_pic')
        address = request.data.get('address')
        biometric_type = request.data.get('type', 'SALIDA')  # Default to SALIDA if not provided
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        user_obj = account_models.UserProfile.objects.filter(user_id=user_id).last()
        if shield_obj:
            if user_obj:
                user_present_in_shield = shield_models.ShieldModel.objects.filter(id=shield_id).filter(members=user_obj)
                if user_present_in_shield:
                    biometric_obj = shield_models.Biometric.objects.create(userprofile=user_obj,
                                                                           shield=shield_obj, lat=lat,
                                                                           long=long, address=address,
                                                                           image=user_pic, type=biometric_type)
                    biometric_serializer = shield_serialziers.BiometricSerializer(biometric_obj)
                    return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                                   data=biometric_serializer.data,
                                                                   msg='New Biometric Details'), status.HTTP_200_OK)
                else:
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                       msg='User is not a member of this shield'),
                        status.HTTP_400_BAD_REQUEST)

            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg='No user with this ID found'), status.HTTP_400_BAD_REQUEST)


        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            biometrics = shield_models.Biometric.objects.filter(shield=shield)
            routes_serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, msg="all biometrics of this shield",
                                               data=routes_serializer.data), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class sheild_membership(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            biometrics = membership_models.MembershipModel.objects.filter(shield=shield)
            print("this is the list of routes===", biometrics)
            routes_serializer = shield_serialziers.sheildMembershipSerializer(biometrics, many=True)
            return JsonResponse(routes_serializer.data, safe=False)
            # backend_utils.success_response(status_code=status.HTTP_200_OK,
            #                                data=routes_serializer.data,
            #                                msg='All biometrics of this shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ChangeShieldName(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ChangeShieldNameSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        id = request.data.get('id')

        check_shield_exists = shield_utils.check_shield_exist(request.data.get('id'))
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        name = request.data.get('name')
        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        if shield:
            shield.shield_name = name
            shield.save()
            shield_serializer = shield_serialziers.ShieldsSerializerForDashboard(shield)
            return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                           data=shield_serializer.data,
                                                           msg='New Shield Details'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class CreateShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.CreateShieldSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        # try:
        users_id = request.data.get('users_id', None)
        shield_name = request.data.get('shield_name')
        tag_name = request.data.get('tag_name')
        locations = request.data.get("locations")
        point_of_interest = shield_models.PointsOfInterest.objects.create(poi_address=locations['location'],
                                                                          poi_lat=locations['lat'],
                                                                          poi_long=locations['long'],
                                                                          poi_tag_name=tag_name)

        shield = shield_models.ShieldModel.objects.create(shield_name=shield_name)
        if users_id:
            for user in users_id:
                user = account_models.User.objects.filter(id=user).last()
                userprofile = account_models.UserProfile.objects.filter(user=user).last()
                shield.members.add(userprofile if userprofile else None)
                #         Populate Hierarchies table
                shield_utils.add_hierarchy_of_member(shield, userprofile)

        shield.members.add(request.user.userprofile)
        shield.locations.add(point_of_interest)
        shield.admin.add(request.user.userprofile)
        shield.shield_super_admin = request.user.userprofile
        shield_utils.populate_hierarchies_tables(shield, request.user.userprofile)
        shield_utils.populate_walkie_talkie_tables(shield, request.user.userprofile)
        shield.save()
        code = shield_utils.check_shield_code(shield)
        shield.shield_joining_code = str(code)
        shield.save()
        # shield_utils.check_shield_code(shield)

        context = {'request': request}
        shield_serializer = shield_serialziers.ShieldsSerializerInCreateShield(shield, context=context)
        return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       data=shield_serializer.data,
                                                       msg='New Shield Details'), status.HTTP_200_OK)


class GetShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        shield = shield_models.ShieldModel.objects.filter(id=request.query_params.get('shield_id')).last()

        context = {'request': request}

        shield_serializer = shield_serialziers.ShieldsSerializerInCreateShield(shield, context=context)
        return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       data=shield_serializer.data,
                                                       msg='Shield Details'), status.HTTP_200_OK)


class GetUserShields(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            # Check if user has a userprofile
            if not hasattr(request.user, 'userprofile') or request.user.userprofile is None:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                  msg='No User Profile Found'), status.HTTP_400_BAD_REQUEST)

            user = request.user.userprofile
            # Use prefetch_related to optimize the query and reduce database hits
            shields = shield_models.ShieldModel.objects.filter(members=user).prefetch_related(
                'members', 'admin', 'locations'
            )

            # Create context with request for proper URL resolution
            context = {'request': request}

            # Serialize the data with proper context
            serializer = shield_serialziers.ShieldsSerializerInCreateShield(shields, many=True, context=context)

            # Log the serialized data structure for debugging
            serialized_data = serializer.data
            print("Serialized shield data structure:")
            for shield in serialized_data:
                print(f"Shield ID: {shield.get('id')}")
                print(f"  locations type: {type(shield.get('locations'))}")
                print(f"  shield_type: {shield.get('shield_type')} (type: {type(shield.get('shield_type'))})")
                print(f"  shield_code: {shield.get('shield_code')} (type: {type(shield.get('shield_code'))})")
                print(f"  shield_name: {shield.get('shield_name')} (type: {type(shield.get('shield_name'))})")
                print(f"  shield_joining_code: {shield.get('shield_joining_code')} (type: {type(shield.get('shield_joining_code'))})")

            # Ensure all string fields that should be null are properly handled
            for shield in serialized_data:
                # Convert empty strings to null/None for fields that might cause issues
                for field in ['shield_type', 'shield_code', 'shield_name', 'shield_joining_code']:
                    if shield.get(field) == "":
                        shield[field] = None

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                              data=serialized_data,
                                              msg='All Shields of the user'), status.HTTP_200_OK)
        except Exception as e:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                              msg=f'Error retrieving shields: {str(e)}'),
                status.HTTP_500_INTERNAL_SERVER_ERROR)


class AssignShieldAdmin(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.AssignAdminShieldSerializer
    get_request_serializer_class = shield_serialziers.GetShieldIdSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        shield_id = request.data.get('shield_id')
        users_id = request.data.get('users_id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            check_admin = shield.admin.filter(user=user).last()
            if check_admin:
                single_user = request.data.get('single_user', None)
                if not single_user == 1:
                    current_admins = shield.admin.all()
                    for admin in current_admins:
                        shield.admin.remove(admin)
                    shield.admin.add(user.userprofile)
                    for user_id in users_id:
                        userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
                        shield.admin.add(userprofile) if userprofile else None
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       msg='Assigned these members as admin'), status.HTTP_200_OK)
                else:
                    for user_id in users_id:
                        userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
                        already_admin = shield.admin.filter(user=userprofile.user).exists()
                        if already_admin:
                            shield.admin.remove(userprofile) if userprofile else None
                        else:
                            shield.admin.add(userprofile) if userprofile else None
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       msg='Assigned  member as admin'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='User is not Admin of this shield'), status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        serializer = self.get_request_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.query_params.get('id')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            serializer = shield_serialziers.ShieldAdminSerializer(shield)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='All Admins of the Shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class ChangeShieldImage(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldImageChangeSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('id')
        user = request.user

        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)

        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        image = request.data.get('image')
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            shield.logo = image
            shield.save()
            serializer = shield_serialziers.ShieldsSerializerForDashboard(shield)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='All Shields of the user'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class ChangeShieldCode(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.data.get('id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            print("=======this is shield====1", shield.shield_joining_code)
            shield.shield_joining_code = shield_utils.create_shield_join_code()
            code = shield_utils.check_shield_code(shield)
            print("=======code====1", code)
            shield.shield_joining_code = code
            print("=======code====2", shield.shield_joining_code)
            shield.save()
            serializer = shield_serialziers.ShieldsSerializerInCreateShield(shield)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='Shield Code changed Successfully'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class ExitShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.data.get('id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            check_user_in_shield = shield.members.filter(user=user).last()
            check_admin = shield.admin.filter(user=user).last()
            check_shield_super_admin = None

            if check_admin:
                length_of_admins = shield.admin.all().count()
                if length_of_admins < 2:
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg='no puedes dejar el escudo sin asignar otro administrador'),
                        status.HTTP_400_BAD_REQUEST)
                shield.admin.remove(user.userprofile)
                shield.save()
            try:
                if shield.shield_super_admin == request.user.userprofile:
                    shield.shield_super_admin = None
                    shield_utils.assign_another_super_admin(shield, request)
                    shield.admin.add(shield.shield_super_admin)
            except:
                pass
            if check_user_in_shield:
                shield.members.remove(user.userprofile)
                shield.save()
                try:
                    shield_utils.remove_member_from_hierarchy(shield, user.userprofile)
                    shield_models.WalkieTalkie.objects.filter(shield=shield, member=user.userprofile).last().delete()
                except:
                    pass

                if shield.members_count == 0:
                    shield.delete()
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='No user in this shield'), status.HTTP_400_BAD_REQUEST)
            try:
                request_obj = shield_models.ShieldJoinRequest.objects.filter(shield=shield,
                                                                             shield_requester=user.userprofile).last().delete()
            except:
                pass
            serializer = shield_serialziers.ShieldsSerializerForDashboard(shield)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=None,
                                               msg='Member Successfully Removed From Shield'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class DeleteShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.data.get('id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            check_admin = shield.admin.filter(user=user).last()
            if check_admin:
                shield_models.ShieldModel.objects.get(id=shield_id).delete()
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                   msg='Shields Deleted Successfully'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='Solo el administrador puede eliminar el escudo'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class AddShieldMembersInAddMemberViewMobile(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.AddMemberToShieldSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.data.get('id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            users_id = request.data.get('users_id')
            for user in users_id:
                userprofile = account_models.UserProfile.objects.filter(user__id=user).last()
                shield.members.add(userprofile if userprofile else None)
                shield_utils.add_hierarchy_of_member(shield, userprofile)
                shield_utils.populate_walkie_talkie_tables(shield, request.user.userprofile)
                serializer = shield_serialziers.ShieldsSerializerInCreateShield(shield)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Shields Member Added Successfully'), status.HTTP_200_OK)

        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class RemoveShieldMembersInAddMemberViewMobile(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.RemoveMemberToShieldSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user
        shield_id = request.data.get('id')
        user_id = request.data.get('user_id')
        check_shield_exists = shield_utils.check_shield_exist(shield_id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(shield_id, user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            check_admin = shield.admin.filter(user=user).last()
            if check_admin:
                userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
                if userprofile:
                    shield.members.remove(userprofile)
                    # when a member will be removed from a shield, his hierarchy will be removed too
                    hierarchy = shield_models.Hierarchie.objects.filter(shield=shield, member=userprofile).last()
                    walkie_talkie = shield_models.WalkieTalkie.objects.filter(shield=shield, member=userprofile).last()
                    shield_request = shield_models.ShieldJoinRequest.objects.filter(shield=shield,
                                                                                    shield_requester=userprofile).last()
                    if shield_request:
                        shield_request.delete()
                    if hierarchy:
                        hierarchy.delete()
                    if walkie_talkie:
                        walkie_talkie.delete()
                    check_user_is_admin = shield.admin.filter(user=userprofile.user).last()
                    if check_user_is_admin:
                        shield.admin.remove(userprofile)
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       msg='User Removed Successfully'), status.HTTP_200_OK)
                else:

                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                       msg='No users found to delete'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='User is not Admin of this shield'), status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Shield Found'), status.HTTP_400_BAD_REQUEST)


class ShieldHierarchies(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('id')
        check_shield_exists = shield_utils.check_shield_exist(id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        hierarchy = shield_models.Hierarchie.objects.filter(shield=shield).order_by('member__user__id')
        if hierarchy:
            hierarchy_serializer = shield_serialziers.HierarchySerializerShield(hierarchy, many=True)
            data = hierarchy_serializer.data
            # data.append({'shield_id': id})
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=data,
                                               msg='All Members with hierarchies of this shield'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No members found'),
                status.HTTP_400_BAD_REQUEST)


class ChangeShieldMemberHierarchies(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetShieldIdAndMemberIDHierarchySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        id = request.data.get('shield_id')
        member_id = request.data.get('member_id')
        check_shield_exists = shield_utils.check_shield_exist(id)
        if not check_shield_exists:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='El escudo actual fue eliminado'), status.HTTP_400_BAD_REQUEST)
        check_user_in_shield = shield_utils.check_user_in_shield(id, request.user)
        if not check_user_in_shield:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No perteneces a este escudo'), status.HTTP_400_BAD_REQUEST)

        hierarchy = request.data.get('hierarchy_name').capitalize()
        shield = shield_models.ShieldModel.objects.filter(id=id).last()
        member = shield_models.Hierarchie.objects.filter(shield=shield, member__user_id=member_id).last()
        if shield:
            if member:
                try:
                    member: shield_models.Hierarchie
                    check = shield_utils.check_hierarchy_exist(hierarchy)
                    if check:
                        member.hierarchy = hierarchy
                        member.save()
                        return Response(
                            backend_utils.success_response(status_code=status.HTTP_200_OK, data=None,
                                                           msg='Hierarchy of member Changed Successfully'),
                            status.HTTP_200_OK)
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg=f'Please enter correct spelling of Hierarchy you entered '
                                                           f'you entered {hierarchy}! available options '
                                                           f'are {shield_models.Colaborativo} , '
                                                           f'{shield_models.Solitario} and {shield_models.Fantasma} '),
                        status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    print("exception===", e)
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg=f'Please enter correct spelling of Hierarchy you entered '
                                                           f'you entered {hierarchy}! available options '
                                                           f'are {shield_models.Colaborativo} , '
                                                           f'{shield_models.Solitario} and {shield_models.Fantasma} '),
                        status.HTTP_400_BAD_REQUEST)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='No Member with this ID found'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield with this ID found'),
                status.HTTP_400_BAD_REQUEST)


class ShieldJoiningRequest(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        shield_id = request.data.get('shield_id')
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            shield_admin = shield_obj.shield_super_admin
            req_obj = shield_models.ShieldJoinRequest.objects.filter(shield=shield_obj).filter(
                shield_super_admin=shield_admin).filter(shield_requester=request.user.userprofile).filter(
                Q(status=shield_models.approved) | Q(status=shield_models.pending))
            if req_obj:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='The user has already requested for this shield'),
                    status.HTTP_400_BAD_REQUEST)
            else:
                request_shield_obj = shield_models.ShieldJoinRequest.objects.create(shield=shield_obj,
                                                                                    shield_super_admin=shield_admin,
                                                                                    shield_requester=request.user.userprofile,
                                                                                    status=shield_models.pending)
                shield_serializer = shield_serialziers.RequestShieldSerialzier(request_shield_obj)
                return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                               data=shield_serializer.data,
                                                               msg='Request to join sent'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class GetOneTimeShieldStatus(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        shield_requester_objs = shield_models.ShieldJoinRequest.objects.filter(
            Q(status=shield_models.approved) | Q(status=shield_models.rejected)).filter(
            shield_requester=request.user.userprofile, already_used_as_notification=False)

        # serializer = shield_serialziers.RequestShieldSerialzierForOneTimeAPi(shield_requester_objs, many=True)
        # for shield_obj in shield_requester_objs:
        #     shield_obj.already_used_as_notification = True
        #     shield_obj.save()
        result = list()
        for shield_obj in shield_requester_objs:
            shield_details = {}
            shield_details['id'] = shield_obj.shield.id
            shield_details['name'] = shield_obj.shield.shield_name
            shield_details['shield_code'] = shield_obj.shield.shield_code
            shield_details['status'] = shield_obj.status
            shield_details['image'] = shield_obj.shield.logo_url
            shield_obj.already_used_as_notification = True
            shield_obj.save()
            result.append(shield_details)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=result,
                                           msg='Requesters to join this shield'), status.HTTP_200_OK)


class MemberAudioStatus(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.MemberAudioStatusSerializer

    def put(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        new_status = request.data.get('status')
        shield_id = request.data.get('shield_id')
        member_id = request.user.id
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_obj = shield_obj.members.filter(user_id=member_id).last()
            if member_obj:
                walkie_obj = shield_models.WalkieTalkie.objects.filter(shield=shield_obj).filter(
                    member=member_obj).last()
                if walkie_obj:
                    shield_models.WalkieTalkie.objects.filter(shield=shield_obj).filter(member=member_obj).update(
                        listen_audio=new_status)
                else:
                    shield_models.WalkieTalkie.objects.create(shield=shield_obj, member=member_obj,
                                                              listen_audio=new_status)
                walkie_talkie_obj = shield_models.WalkieTalkie.objects.filter(shield=shield_obj).filter(
                    member=member_obj).last()
                serializer = shield_serialziers.WalkieTalkieSerializer(walkie_talkie_obj)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Status of this shield member Updated Successfully'),
                    status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg='This user is not the member of this shield'),
                    status.HTTP_400_BAD_REQUEST)

        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class WalkieTalkieReceivers(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        sender_id = request.user.id

        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_obj = shield_obj.members.filter(id=sender_id).last()
            if member_obj:
                walkie_objs = shield_models.WalkieTalkie.objects.filter(shield=shield_obj).values_list('member_id')
                exclude_members = [request.user.id]
                for obj in walkie_objs:
                    exclude_members.append(obj[0])
                member_objs = shield_obj.members.exclude(id__in=exclude_members)
                biometrics_serializer = shield_serialziers.UserProfileSerializer(member_objs, many=True)
                return JsonResponse(biometrics_serializer.data, safe=False)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg='This user is not the member of this shield'),
                    status.HTTP_400_BAD_REQUEST)

        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class ShieldMemberHierarchies(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetMemberIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        member_id = request.query_params.get('member_id')

        if member_id:
            hierarchies = shield_models.Hierarchie.objects.filter(member__user_id=member_id)
            if hierarchies:
                hierarchy_serializer = shield_serialziers.HierarchySerializerShield(hierarchies, many=True)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=hierarchy_serializer.data,
                                                msg='All hierarchies of this member'),
                    status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                msg='No hierarchies found for this member'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                            msg='Member ID is required'),
                status.HTTP_400_BAD_REQUEST)


class CheckUserInShield(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        sender_id = request.user.id

        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id, ).last()
        if not shield_obj:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No shield Exist'), status.HTTP_400_BAD_REQUEST)
        check_member = shield_obj.members.filter(user=request.user).last()
        if check_member:
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg='User Exists'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='Not exists'), status.HTTP_400_BAD_REQUEST)
