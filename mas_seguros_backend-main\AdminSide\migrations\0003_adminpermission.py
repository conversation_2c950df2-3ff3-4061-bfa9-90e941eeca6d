# Generated by Django 5.2.1 on 2025-06-15 08:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Account', '0001_initial'),
        ('AdminSide', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('users_access', models.BooleanField(default=False, help_text='Access to Users section')),
                ('shields_access', models.BooleanField(default=False, help_text='Access to Shields section')),
                ('alerts_sos_access', models.BooleanField(default=False, help_text='Access to Alerts and SOS section')),
                ('payment_history_access', models.BooleanField(default=False, help_text='Access to Payment History section')),
                ('support_access', models.<PERSON>oleanField(default=False, help_text='Access to Support section')),
                ('roles_access', models.Bo<PERSON>anField(default=False, help_text='Access to Roles section')),
                ('full_access', models.<PERSON>oleanField(default=False, help_text='Full access to all sections')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admin', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='admin_permissions', to='Account.userprofile')),
            ],
        ),
    ]
