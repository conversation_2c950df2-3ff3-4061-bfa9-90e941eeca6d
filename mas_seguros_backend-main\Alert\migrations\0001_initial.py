# Generated by Django 5.2.3 on 2025-06-14 11:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlertCategories',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='alert_categories/')),
            ],
        ),
        migrations.CreateModel(
            name='AlertStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, choices=[('Alerta enviada', 'Alerta enviada'), ('Ayuda enviada', 'Ayuda enviada'), ('Alerta resuelta', 'Alerta resuelta')], max_length=200, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='AlertModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('num', models.CharField(blank=True, max_length=50, null=True)),
                ('date', models.DateField(auto_now_add=True, null=True)),
                ('time', models.TimeField(auto_now_add=True, null=True)),
                ('rating', models.CharField(blank=True, max_length=10, null=True)),
                ('evidence', models.FileField(blank=True, null=True, upload_to='alert_evidence')),
                ('thumbnail', models.FileField(blank=True, null=True, upload_to='alert_evidence')),
                ('video_url', models.CharField(blank=True, max_length=1000, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('address', models.CharField(blank=True, max_length=200, null=True)),
                ('lat', models.CharField(blank=True, max_length=30, null=True)),
                ('long', models.CharField(blank=True, max_length=30, null=True)),
                ('current_speed', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_battery', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('evidence_number', models.CharField(max_length=50)),
                ('alert_opened', models.BooleanField(blank=True, default=False, null=True)),
                ('alert_seen', models.BooleanField(blank=True, default=False, null=True)),
                ('rating_description', models.TextField(blank=True, null=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Alert.alertcategories')),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('status', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Alert.alertstatus')),
            ],
        ),
        migrations.CreateModel(
            name='AlertComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('alert', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Alert.alertmodel')),
            ],
        ),
        migrations.CreateModel(
            name='AlertModifyHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(blank=True, choices=[('Alerta enviada', 'Alerta enviada'), ('Ayuda enviada', 'Ayuda enviada'), ('Alerta resuelta', 'Alerta resuelta')], max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('alert', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Alert.alertmodel')),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['userprofile', '-created_at'], name='Alert_alert_userpro_ce4926_idx'),
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['status', '-created_at'], name='Alert_alert_status__13a3f8_idx'),
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['category', '-created_at'], name='Alert_alert_categor_77056f_idx'),
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['userprofile', 'status', '-created_at'], name='Alert_alert_userpro_78c7b9_idx'),
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['category', 'status', '-created_at'], name='Alert_alert_categor_3bc6fe_idx'),
        ),
        migrations.AddIndex(
            model_name='alertmodel',
            index=models.Index(fields=['-date', '-time'], name='Alert_alert_date_a7eb20_idx'),
        ),
    ]
