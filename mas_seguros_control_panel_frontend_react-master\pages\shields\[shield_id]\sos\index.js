import React, { useState, useEffect } from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import Table from "@/components/Table";
import InputGroup from "@/components/utility/InputGroup";
import EvidenceModalBtn from "@/components/shields/EvidenceModalBtn";
import useTableData from "@/hooks/useTableData";
import { useRouter } from "next/router";
import Pagination from "@/components/Pagination";
import { toast } from "react-hot-toast";


const pageSize = 10

export default function index() {
  const router = useRouter();
  const { shield_id } = router.query;

  const [selectedDate, setSelectedDate] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const {
    setSearch,
    currentTableData,
    isLoading: isTableLoading,
    isError,
    error,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess
  } = useTableData({
    dataUrl: `adminside/api/shield/shield-alert-and-sos/?id=${shield_id}`,
    pageSize: pageSize,
    enabled: !!shield_id,
    queryKeys: [`shield-${shield_id}-alerts-table-data`],
    dataCallback: (resp) => {
      if (!resp?.data?.data) return [];

      // Handle combined alerts and SOS data
      const alerts = resp.data.data.alerts || [];
      const sos = resp.data.data.sos || [];

      // Transform SOS data to match alert format
      const transformedSos = sos.map(sosItem => ({
        ...sosItem,
        type: "SOS",
        category: { name: 'SOS', id: sosItem.id },
        status: sosItem.status || sosItem.status_name || "",
        alert_date: sosItem.alert_date || sosItem.alert_datetime?.split('T')[0] || new Date().toISOString().split('T')[0],
        alert_time: sosItem.alert_time || sosItem.alert_datetime?.split('T')[1]?.split('.')[0] || new Date().toTimeString().split(' ')[0],
        userprofile: sosItem.userprofile || sosItem.sender || {},
        evidence_number: sosItem.evidence_number || `S${sosItem.id || Math.random().toString(36).substring(2, 9)}`,
        evidence_url: sosItem.evidence_url || sosItem.evidence || null,
        lat: sosItem.lat || sosItem.sender?.lat,
        long: sosItem.long || sosItem.sender?.long,
        description: sosItem.description || "No hay comentarios disponibles para este SOS",
        created_at: sosItem.created_at ? new Date(sosItem.created_at).toISOString() : new Date().toISOString()
      }));

      // Transform alerts to ensure created_at is in correct format
      const transformedAlerts = alerts.map(alert => ({
        ...alert,
        created_at: alert.created_at ? new Date(alert.created_at).toISOString() : new Date().toISOString()
      }));

      // Combine alerts and transformed SOS records
      return [...transformedSos, ...transformedAlerts];
    }
  })

  // Debug function to log data
  useEffect(() => {
    if (isSuccess && allData) {
      console.log('SOS Page - All Data:', allData);
      console.log('SOS Page - Current Table Data:', currentTableData);
    }
  }, [isSuccess, allData, currentTableData]);

  const handleDateSearch = () => {
    if (!selectedDate) {
      toast.error('Por favor seleccione una fecha');
      return;
    }

    try {
      setIsLoading(true);

      // Use the search functionality from useTableData
      // Format the date for search (DD/MM/YYYY format)
      const [year, month, day] = selectedDate.split('-');
      const formattedDate = `${day}/${month}/${year}`;

      setSearch(formattedDate);
      setCurrentPage(1);

      toast.success('Búsqueda aplicada correctamente');
    } catch (error) {
      console.error('Error searching by date:', error);
      toast.error('Error al aplicar la búsqueda');
    } finally {
      setIsLoading(false);
    }
  };

  const clearSearch = () => {
    setSelectedDate('');
    setSearch('');
    setCurrentPage(1);
    toast.success('Filtros limpiados');
  };

  return (
    <ShieldLayout pageTitle="Escudos" headerTitle="Escudos">
      <div className="mt-5 space-y-6">
        <div className="flex items-center gap-2 text-sm">
          <span>Buscar</span>
          <div>
            <InputGroup>
              <InputGroup.Input
                type="date"
                className="!border-none bg-accent"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </InputGroup>
          </div>
          <button
            className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2 disabled:opacity-50"
            onClick={handleDateSearch}
            disabled={isLoading || isTableLoading}
          >
            {isLoading ? 'Buscando...' : 'Buscar'}
          </button>
          {selectedDate && (
            <button
              className="self-stretch rounded bg-gray-500 px-3 font-medium text-white ring-offset-2 focus:ring-2 disabled:opacity-50"
              onClick={clearSearch}
              disabled={isLoading || isTableLoading}
            >
              Limpiar
            </button>
          )}
        </div>

        <Table
          dataCount={currentTableData?.length || 0}
          isLoading={isLoading || isTableLoading}
          isError={isError}
          error={error}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Tipo</Table.Th>
              <Table.Th>Ubicación</Table.Th>
              <Table.Th>Horario</Table.Th>
              <Table.Th>Evidencia</Table.Th>
              <Table.Th>Miembro</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {isError ? (
              <Table.Tr>
                <Table.Td colSpan={5} className="text-center py-4 text-red-500">
                  Error al cargar datos: {error?.message || 'Error desconocido'}
                </Table.Td>
              </Table.Tr>
            ) : (isLoading || isTableLoading) ? null : currentTableData && currentTableData.length > 0 ? (
              currentTableData.map((alert) => (
                <Table.Tr key={alert.id}>
                  <Table.Td>
                    {alert.type === 'SOS' ? (
                      <div>
                        <p className="font-semibold text-danger">SOS</p>
                        <p>SOS#{alert.id}</p>
                      </div>
                    ) : (
                      <div>
                        <p className="font-semibold capitalize">
                          {alert.category?.name || 'Alerta'}
                        </p>
                        <p>
                          <span className="capitalize">
                            {alert.category?.name || 'Alerta'}
                          </span>
                          #{alert.id}
                        </p>
                      </div>
                    )}
                  </Table.Td>
                  <Table.Td className="font-semibold">
                    {alert.lat && alert.long ? `${alert.lat}, ${alert.long}` : '-'}
                  </Table.Td>
                  <Table.Td>
                    <dd>{alert.alert_date}</dd>
                    <dd>{alert.alert_time}</dd>
                  </Table.Td>
                  <Table.Td>
                    {(() => {
                      const evidenceNumber = alert.evidence_number || `${alert.type === 'SOS' ? 'S' : 'A'}${alert.id}`;

                      // Always show as clickable - modal will handle missing evidence as per CA0104
                      return (
                        <EvidenceModalBtn alert={alert} className="font-semibold text-primary hover:underline">
                          {evidenceNumber}
                        </EvidenceModalBtn>
                      );
                    })()}
                  </Table.Td>
                  <Table.Td>
                    {alert.userprofile?.full_name || 'Usuario'}
                  </Table.Td>
                </Table.Tr>
              ))
            ) : (
              <Table.Tr>
                <Table.Td colSpan={5} className="text-center py-4">
                  {selectedDate ? 'No hay datos para la fecha seleccionada' : 'No hay datos disponibles'}
                </Table.Td>
              </Table.Tr>
            )}
          </Table.Tbody>
        </Table>
        {isSuccess && allData && allData.length > 0 && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </ShieldLayout>
  );
}
