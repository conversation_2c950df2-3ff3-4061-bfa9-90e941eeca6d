import random

from rest_framework.serializers import ModelSerializer
from Alert import models as alert_models
from rest_framework import serializers, viewsets
from django.contrib.auth.models import User
from Account.models import UserProfile
from Account import serializers as account_seri, models as account_models
from datetime import datetime
from Sos import models as sos_models


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = account_models.User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    birth_date = serializers.SerializerMethodField()

    # image_url = serializers.SerializerMethodField()
    def get_birth_date(self, obj):
        try:
            old_date = datetime.strptime(str(obj.birth_date), '%Y-%m-%d')
            return old_date
        except:
            return None

    class Meta:
        model = account_models.UserProfile
        fields = ['user',
                  'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role', 'lat',
                  'long',
                  'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend', 'created_at',
                  'updated_at', 'image_url']


class GetAlertSerializer(serializers.ModelSerializer):
    category = serializers.SerializerMethodField()
    status_name = serializers.SerializerMethodField()
    alert_date = serializers.SerializerMethodField()
    alert_datetime = serializers.SerializerMethodField()
    userprofile = UserProfileSerializer()
    shield = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()

    def get_type(self, obj):
        return "Alert"

    def get_alert_date(self, obj):
        try:
            old_date = datetime.strptime(obj.alert_date, '%m/%d/%Y')
            return old_date.strftime('%d/%m/%Y')
        except (AttributeError, ValueError, TypeError):
            return None

    def get_alert_datetime(self, obj):
        try:
            old_date = datetime.strptime(obj.alert_date, '%m/%d/%Y').strftime("%Y-%m-%d")
            newdate = '{}T{}'.format(old_date, obj.alert_time)
            return newdate
        except (AttributeError, ValueError, TypeError):
            return None

    def get_category(self, obj):
        if obj.category is None:
            return ''
        return obj.category.name

    def get_status_name(self, obj):
        if obj.status is None:
            return ''
        return '{}'.format(obj.status.name)

    def get_shield(self, obj):
        # Optimized shield lookup using prefetched data
        try:
            # Use prefetched shield_alerts to avoid additional queries
            shields = getattr(obj, '_prefetched_objects_cache', {}).get('shield_alerts', None)
            if shields is not None:
                shield = shields[0] if shields else None
            else:
                shield = obj.shield_alerts.first()

            if shield:
                return {
                    'id': shield.id,
                    'shield_code': shield.shield_code,
                    'shield_name': shield.shield_name
                }
        except Exception as e:
            print(f"Error getting shield for alert {obj.id}: {e}")
        return None

    class Meta:
        model = alert_models.AlertModel
        fields = ['userprofile', 'category', 'num', 'status', 'status_name', 'alert_date', 'alert_time',
                  'alert_datetime', 'evidence_url', 'description', 'lat', 'long',
                  'address',
                  'current_speed',
                  'phone_battery', 'evidence_number', 'rating_description', 'rating', 'id', 'evidence_number', 'type', 'shield']


# to update the alerts
class getallAlertSerilizer(serializers.ModelSerializer):
    category = serializers.CharField(required=False, max_length=50)
    status = serializers.CharField(required=False, max_length=50)
    evidence = serializers.FileField(required=False)
    description = serializers.CharField(required=False)
    current_speed = serializers.CharField(required=False)
    phone_battery = serializers.CharField(required=False)

    class Meta:
        model = alert_models.AlertModel
        fields = ['category', 'num', 'status', 'alert_date', 'alert_time', 'evidence', 'description', 'address',
                  'current_speed',
                  'phone_battery', 'evidence_number', 'rating_description', 'rating', 'id', 'userprofile']

    def to_representation(self, instance):
        response = super().to_representation(instance)
        if instance.userprofile is not None:
            response['userprofile'] = UserProfileSerializer(instance.userprofile).data
        else:
            response['userprofile'] = None
        return response


class ChangeAlertStatusSerializer(serializers.Serializer):
    status = serializers.CharField(max_length=50, required=True)
    id = serializers.IntegerField(required=True)


class ChangeSosStatusSerializer(serializers.Serializer):
    status = serializers.CharField(max_length=50, required=True)
    id = serializers.IntegerField(required=True)


class PostCommentAlertSosSerializer(serializers.Serializer):
    comment = serializers.CharField(max_length=500, required=True)
    type = serializers.CharField(max_length=50, required=True)
    id = serializers.IntegerField(required=True)


class GetCommentAlertSosSerializer(serializers.Serializer):
    type = serializers.CharField(max_length=50, required=True)
    id = serializers.IntegerField(required=True)


class AlertModifyHistorySerializer(serializers.ModelSerializer):
    # alert = GetAlertSerializer()
    userprofile = UserProfileSerializer()
    type = serializers.SerializerMethodField()

    def get_type(self, obj):
        return "Alert"

    class Meta:
        model = alert_models.AlertModifyHistory
        fields = ['userprofile', 'status', 'type', 'created_at', 'updated_at']
        # fields = '__all__'


class GetAlertModifyHistorySerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)


class SosSerializer(serializers.ModelSerializer):
    sender = UserProfileSerializer()
    alert_datetime = serializers.SerializerMethodField()
    status_name = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()

    def get_type(self, obj):
        return "SOS"

    def get_status_name(self, obj):
        try:
            if obj.status:
                return obj.status
            return ''
        except (AttributeError, ValueError, TypeError):
            return ''

    def get_alert_datetime(self, obj):
        # Use created_at field instead of alert_date and alert_time
        try:
            if obj.created_at:
                date_str = obj.created_at.strftime("%Y-%m-%d")
                time_str = obj.created_at.strftime("%H:%M:%S")
                return '{}T{}'.format(date_str, time_str)
            return None
        except (AttributeError, ValueError, TypeError):
            return None

    class Meta:
        model = sos_models.Sos
        fields = ['sender', 'status', 'status_name', 'active', 'lat', 'long', 'location', 'created_at', 'updated_at', 'alert_datetime', 'type']


class SosModifyHistorySerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializer()
    sos = SosSerializer()

    class Meta:
        model = sos_models.SosModifyHistory
        fields = '__all__'


# This UserSerializer is already defined above, so we don't need to redefine it here


class AlertCommentHistorySerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializer()

    # alert =
    class Meta:
        model = alert_models.AlertComment
        fields = '__all__'






# class UserProfileSerializer(serializers.ModelSerializer):
#     user = UserSerializer()
#     birth_date = serializers.SerializerMethodField()
#
#     # image_url = serializers.SerializerMethodField()
#     def get_birth_date(self, obj):
#         try:
#             old_date = datetime.strptime(str(obj.birth_date), '%Y-%m-%d')
#             return old_date
#         except:
#             return None
#
#     class Meta:
#         model = account_models.UserProfile
#         fields = ['user',
#                   'firebase_uid', 'phone', 'full_name', 'identification_card', 'birth_date', 'role', 'lat', 'long',
#                   'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend', 'created_at',
#                   'updated_at', 'image_url']

