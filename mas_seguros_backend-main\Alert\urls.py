from django.urls import path, reverse
# from Account.views_api import *
from Alert import views as alert_views

urlpatterns = [
    path('', alert_views.Alert.as_view(), name='alert'),
    path('getallalerts/', alert_views.GetAlerts.as_view(), name='get-alerts'),
    path('alertcategories/', alert_views.AlertCategories.as_view(), name='alert-categories'),
    path('alertstatuses/', alert_views.AlertStatuses.as_view(), name='alert-statuses'),
    path('alertreviews/', alert_views.AlertReviews.as_view(), name='alert-reviews'),
    path('unresolvedalertr/', alert_views.UnresolvedAlert.as_view(), name='unresolved-alert'),
    path('resolvedalert/', alert_views.GetresolvedAlert.as_view(), name='resolved-alert'),
    path('alertopened/', alert_views.AlertOpened.as_view(), name='alert-opened'),
    path('alertseened/', alert_views.AlertSeened.as_view(), name='alert-seened'),

]


def get_login_url():
    return reverse("user-login")


def get_register_url():
    return reverse("register-user")


def get_verify_email_url(username, code):
    return reverse("email-verify", kwargs={"username": username, "code": code})


def get_reset_password_url():
    return reverse("reset-password")


def send_verification_code(username):
    return reverse("send-verification-code", kwargs={"username": username})


def set_user_password(username, code):
    return reverse("set-user-password", kwargs={"username": username, "code": code})


def confirm_reset_password_code(username, code=0):
    return reverse("confirm-reset-password-code", kwargs={"username": username, "code": code})
