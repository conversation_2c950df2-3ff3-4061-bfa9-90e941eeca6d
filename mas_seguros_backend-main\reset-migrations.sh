#!/bin/bash
# =============================================================================
# MAS SEGUROS BACKEND - COMPREHENSIVE MIGRATION RESET SCRIPT (BASH)
# =============================================================================
# This script safely resets all Django migrations for the project
# Author: Augment Agent
# Date: $(date +%Y-%m-%d)
# =============================================================================

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Emoji functions
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
error() { echo -e "${RED}❌ $1${NC}"; }
info() { echo -e "${CYAN}ℹ️  $1${NC}"; }

# Default values
SKIP_BACKUP=false
FORCE=false
HELP=false
RECREATE_VENV=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --recreate-venv)
            RECREATE_VENV=true
            shift
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

if [ "$HELP" = true ]; then
    cat << EOF
MAS SEGUROS MIGRATION RESET SCRIPT
==================================

Usage: ./reset-migrations.sh [OPTIONS]

Options:
  --skip-backup      Skip database backup (not recommended for production)
  --force            Skip confirmation prompts
  --recreate-venv    Delete and recreate virtual environment
  --help, -h         Show this help message

Examples:
  ./reset-migrations.sh                      # Normal reset with backup
  ./reset-migrations.sh --skip-backup       # Reset without backup
  ./reset-migrations.sh --force             # Reset without prompts
  ./reset-migrations.sh --recreate-venv     # Reset with new venv

EOF
    exit 0
fi

echo -e "${MAGENTA}"
cat << "EOF"
=============================================================================
🚀 MAS SEGUROS BACKEND - MIGRATION RESET SCRIPT
=============================================================================
EOF
echo -e "${NC}"

# Recreate virtual environment if requested
if [ "$RECREATE_VENV" = true ]; then
    warning "Recreating virtual environment..."
    if [ "$FORCE" != true ]; then
        read -p "This will delete the current venv. Continue? (y/N): " venv_confirm
        if [[ ! "$venv_confirm" =~ ^[Yy]$ ]]; then
            info "Operation cancelled."
            exit 0
        fi
    fi

    info "Deleting old virtual environment..."
    rm -rf venv

    info "Creating new virtual environment..."
    python3 -m venv venv
    source venv/bin/activate

    info "Upgrading pip..."
    pip install --upgrade pip

    info "Installing dependencies..."
    pip install -r requirements.txt

    success "Virtual environment recreated successfully"
fi

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    warning "Virtual environment not detected!"
    info "Attempting to activate virtual environment..."

    # Try to find and activate virtual environment
    venv_paths=("./venv/bin/activate" "./env/bin/activate" "./.venv/bin/activate")
    venv_activated=false

    for venv_path in "${venv_paths[@]}"; do
        if [ -f "$venv_path" ]; then
            info "Found virtual environment at: $venv_path"
            if source "$venv_path" 2>/dev/null; then
                venv_activated=true
                success "Virtual environment activated successfully!"
                break
            else
                warning "Failed to activate virtual environment at: $venv_path"
            fi
        fi
    done

    if [ "$venv_activated" = false ]; then
        warning "Could not find or activate virtual environment!"
        info "Please activate your virtual environment manually:"
        echo -e "   ${YELLOW}source venv/bin/activate${NC}"
        if [ "$FORCE" != true ]; then
            read -p "Continue anyway? (y/N): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
fi

# Check if manage.py exists
if [ ! -f "manage.py" ]; then
    error "manage.py not found! Please run this script from the Django project root."
    exit 1
fi

# Confirmation prompt
if [ "$FORCE" != true ]; then
    warning "This will DELETE ALL migration files and reset the database!"
    info "Make sure you have a backup of your data."
    read -p "Are you sure you want to continue? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        info "Operation cancelled."
        exit 0
    fi
fi

# Create backup if not skipped
if [ "$SKIP_BACKUP" != true ]; then
    info "Creating database backup..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="backup_$timestamp.json"

    if python manage.py dumpdata --natural-foreign --natural-primary --exclude=contenttypes --exclude=auth.Permission > "$backup_file" 2>/dev/null; then
        success "Database backup created: $backup_file"
    else
        warning "Backup failed, but continuing..."
    fi
fi

info "Step 1: Deleting migration .py files (except __init__.py)..."
deleted_files=$(find . -path "*/migrations/*.py" ! -name "__init__.py" ! -path "*/venv/*" ! -path "*/site-packages/*" -delete -print 2>/dev/null | wc -l)
success "Deleted $deleted_files migration files"

info "Step 2: Deleting .pyc files in migrations..."
deleted_pyc=$(find . -path "*/migrations/*.pyc" ! -path "*/venv/*" ! -path "*/site-packages/*" -delete -print 2>/dev/null | wc -l)
success "Deleted $deleted_pyc .pyc files"

info "Step 3: Deleting __pycache__ folders..."
deleted_cache=$(find . -type d -name "__pycache__" ! -path "*/venv/*" ! -path "*/site-packages/*" -exec rm -rf {} + -print 2>/dev/null | wc -l)
success "Deleted $deleted_cache __pycache__ folders"

info "Step 4: Deleting migrations folders (except venv)..."
deleted_dirs=$(find . -type d -name "migrations" ! -path "*/venv/*" ! -path "*/site-packages/*" -exec rm -rf {} + -print 2>/dev/null | wc -l)
success "Deleted $deleted_dirs migration directories"

info "Step 5: Creating fresh migrations..."
if python manage.py makemigrations; then
    success "Fresh migrations created successfully"
else
    error "Failed to create migrations!"
    exit 1
fi

info "Step 6: Applying migrations..."
if python manage.py migrate; then
    success "Migrations applied successfully"
else
    error "Failed to apply migrations!"
    exit 1
fi

info "Step 7: Running system checks..."
if python manage.py check; then
    success "System check passed"
else
    warning "System check failed, but migrations completed"
fi

echo -e "${GREEN}"
cat << "EOF"
=============================================================================
🎉 MIGRATION RESET COMPLETED SUCCESSFULLY!
=============================================================================
EOF
echo -e "${NC}"

info "Summary:"
echo -e "  • Migration files deleted: ${deleted_files}"
echo -e "  • Cache files deleted: ${deleted_pyc}"
echo -e "  • Cache directories deleted: ${deleted_cache}"
echo -e "  • Migration directories deleted: ${deleted_dirs}"
if [ "$SKIP_BACKUP" != true ]; then
    echo -e "  • Backup created: ${backup_file}"
fi

info "Next steps:"
echo -e "  ${YELLOW}1. Test your application: python manage.py runserver${NC}"
echo -e "  ${YELLOW}2. Create superuser if needed: python manage.py createsuperuser${NC}"
if [ "$SKIP_BACKUP" != true ]; then
    echo -e "  ${YELLOW}3. Load backup data if needed: python manage.py loaddata $backup_file${NC}"
fi
