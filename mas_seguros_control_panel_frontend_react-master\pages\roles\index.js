import React, { Fragment, useState, useEffect } from "react";
import Admin from "@/components/layouts/Admin";
import UserRoleCard from "@/components/roles/UserRoleCard";
import SectionHeading from "@/components/SectionHeading";
import Table from "@/components/Table";
import { Menu, Transition } from "@headlessui/react";
import classNames from "classnames";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import AdminCreateModalBtn from "@/components/roles/AdminCreateModalBtn";
import AdminEditModalBtn from "@/components/roles/AdminEditModalBtn";
import AdminFormModal from "@/components/admin/AdminFormModal";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";
import { useSelector, useDispatch } from "react-redux";
import { update as updateUser } from "@/redux/userSlice";

function index() {
  const [admins, setAdmins] = useState([]);
  const [loading, setLoading] = useState(true);
  const { axios } = useAxios();

  // Get authentication state
  const isLoggedIn = useSelector((state) => state.user.logged_in);
  const token = useSelector((state) => state.user.token);

  const dispatch = useDispatch();

  useEffect(() => {
    if (isLoggedIn && token) {
      fetchAdmins();
    } else {
      setLoading(false);
    }
  }, [isLoggedIn, token]);

  const fetchAdmins = async () => {
    if (!isLoggedIn || !token) {
      console.log('User not authenticated');
      setAdmins([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await axios.get('/adminside/api/roles/admins/');

      if (response.data.success) {
        setAdmins(response.data.data);
      } else {
        toast.error('Error fetching administrators');
      }
    } catch (error) {
      console.error('Error fetching admins:', error);
      if (error.response?.status === 401) {
        toast.error('Session expired. Please login again.');
      } else {
        toast.error('Error fetching administrators');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAdminCreated = () => {
    fetchAdmins(); // Refresh the list when a new admin is created
  };

  const handleSuspendAdmin = async (adminId, suspend = true) => {
    try {
      const response = await axios.post('/adminside/api/roles/admin/suspend/', {
        admin_id: adminId,
        suspend: suspend
      });

      if (response.data.success) {
        toast.success(response.data.message);
        fetchAdmins(); // Refresh the list
      } else {
        toast.error('Error updating administrator status');
      }
    } catch (error) {
      console.error('Error suspending admin:', error);
      toast.error('Error updating administrator status');
    }
  };

  const handleDeleteAdmin = async (adminId) => {
    if (!confirm('¿Está seguro de que desea eliminar este administrador?')) {
      return;
    }

    try {
      const response = await axios.post('/adminside/api/roles/admin/delete/', {
        admin_id: adminId
      });

      if (response.data.success) {
        toast.success(response.data.message);
        fetchAdmins(); // Refresh the list
      } else {
        toast.error('Error deleting administrator');
      }
    } catch (error) {
      console.error('Error deleting admin:', error);
      toast.error('Error deleting administrator');
    }
  };
  // Show login message if not authenticated
  if (!isLoggedIn) {
    return (
      <Admin headerTitle="Roles" pageTitle="Roles">
        <div className="container-padding flex flex-col gap-5 py-7">
          <div className="bg-white p-8 text-center">
            <h2 className="text-xl font-semibold mb-4">Acceso Requerido</h2>
            <p className="text-gray-600 mb-4">Por favor, inicie sesión para acceder a la gestión de roles.</p>
            <a href="/" className="inline-block bg-primary text-white px-6 py-2 rounded hover:bg-primary-dark">
              Ir a Login
            </a>
          </div>
        </div>
      </Admin>
    );
  }

  return (
    <Admin headerTitle="Roles" pageTitle="Roles">
      <div className="container-padding flex flex-col gap-5 py-7 lg:flex-row min-h-screen">
        <div className="w-full lg:max-w-lg">
          <UserRoleCard />
        </div>

        <div className="flex-grow space-y-5">
          <div className="space-y-5 bg-white p-6">
            <div className="flex flex-col justify-between gap-4 md:flex-row">
              <SectionHeading>Roles de Administradores</SectionHeading>
              <AdminCreateModalBtn
                onAdminCreated={handleAdminCreated}
                className="ml-auto rounded bg-primary px-4 py-2 text-sm text-white hover:bg-primary-dark transition-colors"
              >
                + Crear Admin
              </AdminCreateModalBtn>
            </div>
            <div className="overflow-visible rounded-lg border border-gray-200" style={{ minHeight: '400px' }}>
              <Table
                wrapperClassName="bg-accent overflow-x-auto min-h-[800px] overflow-y-visible"
                className="relative min-w-full divide-y divide-gray-200"
              >
              <Table.Thead className="sticky top-0 z-[2] bg-accent">
                <Table.Tr>
                  <Table.Th className="w-1/4 min-w-[200px]">Usuario</Table.Th>
                  <Table.Th className="w-1/8 min-w-[100px]">Tipo</Table.Th>
                  <Table.Th className="w-1/8 min-w-[120px]">Fecha Creación</Table.Th>
                  <Table.Th className="w-1/6 min-w-[150px]">Ult. Actividad</Table.Th>
                  <Table.Th className="w-1/8 min-w-[100px]">Estado</Table.Th>
                  <Table.Th className="w-1/8 min-w-[120px]">Acciones</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {loading ? (
                  // Loading skeleton
                  [...Array(5)].map((_, index) => (
                    <Table.Tr key={index}>
                      <Table.Td>
                        <div className="flex items-center gap-3">
                          <div className="w-11 h-11 bg-gray-200 rounded animate-pulse"></div>
                          <div>
                            <div className="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                          </div>
                        </div>
                      </Table.Td>
                      <Table.Td><div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div></Table.Td>
                      <Table.Td><div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div></Table.Td>
                      <Table.Td><div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div></Table.Td>
                      <Table.Td><div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div></Table.Td>
                      <Table.Td><div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div></Table.Td>
                    </Table.Tr>
                  ))
                ) : admins.length === 0 ? (
                  <Table.Tr>
                    <Table.Td colSpan={6} className="text-center py-8 text-gray-500">
                      No hay administradores registrados
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  admins.map((admin, index) => (
                    <Table.Tr key={admin.id} className={index === admins.length - 1 ? "pb-20" : ""}>
                      <Table.Td className="py-6">
                        <div className="flex items-center gap-3">
                          <img
                            src={admin.image_url || "/assets/img/default-profile-pic-1.jpg"}
                            className="block aspect-square w-11 h-11 object-cover rounded-full"
                            onError={(e) => {
                              e.target.src = "/assets/img/default-profile-pic-1.jpg";
                            }}
                          />
                          <div className="min-w-0 flex-1">
                            <dd className="text-base font-medium text-gray-900 truncate">{admin.full_name}</dd>
                            <dd className="text-sm text-gray-500">ID-{admin.id}</dd>
                          </div>
                        </div>
                      </Table.Td>
                      <Table.Td className="py-6">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          admin.admin_permissions?.full_access
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {admin.admin_permissions?.full_access ? 'Superadmin' : 'Admin'}
                        </span>
                      </Table.Td>
                      <Table.Td className="py-6 text-sm text-gray-900">
                        {new Date(admin.created_at).toLocaleDateString('es-ES')}
                      </Table.Td>
                      <Table.Td className="py-6 text-sm text-gray-900">{admin.last_activity}</Table.Td>
                      <Table.Td className="py-6">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          admin.suspend
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {admin.suspend ? 'Suspendido' : 'Activo'}
                        </span>
                      </Table.Td>
                      <Table.Td className="py-6 text-right relative">
                        <ActionBtn
                          admin={admin}
                          onSuspend={handleSuspendAdmin}
                          onDelete={handleDeleteAdmin}
                          onUpdate={fetchAdmins}
                        />
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
              </Table>
              {/* Extra space for dropdown visibility */}
              <div className="h-32"></div>
            </div>
          </div>
        </div>
      </div>
    </Admin>
  );
}

const ActionBtn = ({ admin, onSuspend, onDelete, onUpdate }) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const handleSuspendToggle = () => {
    onSuspend(admin.id, !admin.suspend);
  };

  const handleDelete = () => {
    onDelete(admin.id);
  };

  return (
    <>
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <Menu.Button className="inline-flex w-full items-center justify-center gap-1 rounded-md bg-accent px-4 py-3 text-sm font-medium text-black hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary transition-colors">
            Acción
            <ChevronDownIcon className="h-4 w-4" aria-hidden="true" />
          </Menu.Button>
        </div>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-[9999] mt-2 w-64 origin-top-right rounded-md bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none border border-gray-200 max-h-none">
            <div className="py-2">
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={() => setShowDetailsModal(true)}
                    className={classNames(
                      active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                      "block w-full text-left px-6 py-3 text-sm hover:bg-gray-50 transition-colors"
                    )}
                  >
                    Ver detalles
                  </button>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={handleSuspendToggle}
                    className={classNames(
                      active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                      "block w-full text-left px-6 py-3 text-sm hover:bg-gray-50 transition-colors"
                    )}
                  >
                    {admin.suspend ? 'Reactivar cuenta' : 'Suspender cuenta'}
                  </button>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={() => setShowEditModal(true)}
                    className={classNames(
                      active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                      "block w-full text-left px-6 py-3 text-sm hover:bg-gray-50 transition-colors"
                    )}
                  >
                    Editar
                  </button>
                )}
              </Menu.Item>
              <Menu.Item>
                {({ active }) => (
                  <button
                    onClick={handleDelete}
                    className={classNames(
                      active ? "bg-red-50 text-red-900" : "text-red-700",
                      "block w-full text-left px-6 py-3 text-sm hover:bg-red-50 transition-colors"
                    )}
                  >
                    Eliminar
                  </button>
                )}
              </Menu.Item>
            </div>
          </Menu.Items>
        </Transition>
      </Menu>

      {/* Admin Details Modal */}
      {showDetailsModal && (
        <AdminDetailsModal
          admin={admin}
          open={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
        />
      )}

      {/* Admin Edit Modal */}
      {showEditModal && (
        <AdminEditModal
          admin={admin}
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          onUpdate={onUpdate}
        />
      )}
    </>
  );
};

// Placeholder components for admin details and edit modals
const AdminDetailsModal = ({ admin, open, onClose }) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Detalles del Administrador</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>
        <div className="space-y-3">
          <div>
            <span className="font-semibold">Nombre:</span> {admin.full_name}
          </div>
          <div>
            <span className="font-semibold">Email:</span> {admin.user?.email}
          </div>
          <div>
            <span className="font-semibold">Teléfono:</span> {admin.phone}
          </div>
          <div>
            <span className="font-semibold">Estado:</span> {admin.suspend ? 'Suspendido' : 'Activo'}
          </div>
          <div>
            <span className="font-semibold">Fecha de creación:</span> {new Date(admin.created_at).toLocaleDateString('es-ES')}
          </div>
          {admin.admin_permissions && (
            <div>
              <span className="font-semibold">Permisos:</span>
              <ul className="mt-1 text-sm">
                {admin.admin_permissions.full_access ? (
                  <li>• Acceso completo</li>
                ) : (
                  <>
                    {admin.admin_permissions.users_access && <li>• Usuarios</li>}
                    {admin.admin_permissions.shields_access && <li>• Escudos</li>}
                    {admin.admin_permissions.alerts_sos_access && <li>• Alertas y SOS</li>}
                    {admin.admin_permissions.payment_history_access && <li>• Historial de Pagos</li>}
                    {admin.admin_permissions.support_access && <li>• Soporte</li>}
                    {admin.admin_permissions.roles_access && <li>• Roles</li>}
                  </>
                )}
              </ul>
            </div>
          )}
        </div>
        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Cerrar
          </button>
        </div>
      </div>
    </div>
  );
};

const AdminEditModal = ({ admin, open, onClose, onUpdate }) => {
  const { axios } = useAxios();
  const [wasRecentlySuccessful, setWasRecentlySuccessful] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const currentUserId = useSelector((state) => state.user.id);

  const handleSubmit = async (data, selectedImage = null) => {
    if (!admin) {
      toast.error('No se pudo identificar el administrador');
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare FormData for file upload
      const formData = new FormData();

      // Add text fields
      formData.append('admin_id', admin.id);
      formData.append('first_name', data.first_name);
      formData.append('last_name', data.last_name);
      formData.append('phone', data.phone);
      formData.append('identification_card', data.identification_card || '');

      // Add permission fields
      formData.append('users_access', data.users_access || false);
      formData.append('shields_access', data.shields_access || false);
      formData.append('alerts_sos_access', data.alerts_sos_access || false);
      formData.append('payment_history_access', data.payment_history_access || false);
      formData.append('support_access', data.support_access || false);
      formData.append('roles_access', data.roles_access || false);
      formData.append('full_access', data.full_access || false);

      // Add image if selected
      if (selectedImage) {
        formData.append('image', selectedImage);
      }

      const response = await axios.post('/adminside/api/roles/admin/update/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        toast.success(response.data.message);
        setWasRecentlySuccessful(true);
        onClose();
        onUpdate(); // Refresh the admin list
        // --- Redux sync for current user ---
        if (admin.id === currentUserId && response.data.data) {
          const apiUser = response.data.data;
          // Flatten user fields for Redux
          const mappedUser = {
            ...apiUser,
            id: apiUser.user?.id,
            email: apiUser.user?.email,
            first_name: apiUser.user?.first_name,
            last_name: apiUser.user?.last_name,
            image: apiUser.image_url,
            admin_permissions: apiUser.admin_permissions,
            // keep other fields as is
          };
          dispatch(updateUser(mappedUser));
        }
      } else {
        toast.error(response.data.message || 'Error updating administrator');
      }
    } catch (error) {
      console.error('Error updating admin:', error);

      // Handle validation errors
      if (error?.response?.data?.errors) {
        const errors = error.response.data.errors;

        // Show specific field errors
        Object.keys(errors).forEach(field => {
          const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
          fieldErrors.forEach(errorMsg => {
            toast.error(`${field}: ${errorMsg}`);
          });
        });
      } else {
        // Show general error message
        const errorMessage = error?.response?.data?.message ||
                           error?.response?.data?.msg ||
                           'Error actualizando administrador';
        toast.error(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminFormModal
      open={open}
      close={onClose}
      mode="edit"
      submit={handleSubmit}
      currentAdmin={admin}
      wasRecentlySuccessful={wasRecentlySuccessful}
      isSubmitting={isSubmitting}
    />
  );
};

export default index;
