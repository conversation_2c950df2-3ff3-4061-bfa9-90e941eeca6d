# Virtual environments
venv/
seguron_env/
.venv/
env/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.local
.env.production

# Firebase credentials (SECURITY CRITICAL)
**/mas-seguros-cred.json
**/firebase-cred.json
**/service-account.json
**/*-cred.json
**/*-credentials.json

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so

# Database files
db.sqlite3
db.sqlite3-journal
*.db

# Django static files
/static/
/staticfiles/
/media/

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Migration files (keep initial migrations, ignore subsequent ones)
# Keep 0001_initial.py and 0002_initial.py files for production deployment
# Ignore migrations starting from 0003_*.py onwards
*/migrations/0003_*.py
*/migrations/0004_*.py
*/migrations/0005_*.py
*/migrations/0006_*.py
*/migrations/0007_*.py
*/migrations/0008_*.py
*/migrations/0009_*.py
*/migrations/001[0-9]_*.py
*/migrations/002[0-9]_*.py
*/migrations/003[0-9]_*.py
*/migrations/004[0-9]_*.py
*/migrations/005[0-9]_*.py
*/migrations/006[0-9]_*.py
*/migrations/007[0-9]_*.py
*/migrations/008[0-9]_*.py
*/migrations/009[0-9]_*.py
*/migrations/01[0-9][0-9]_*.py
*/migrations/02[0-9][0-9]_*.py
*/migrations/03[0-9][0-9]_*.py
*/migrations/04[0-9][0-9]_*.py
*/migrations/05[0-9][0-9]_*.py

# Backup files
backup_*.json
*.backup

# Temporary files
*.tmp
*.temp