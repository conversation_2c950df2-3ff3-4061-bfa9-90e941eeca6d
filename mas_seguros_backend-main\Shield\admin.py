from django.contrib import admin

# Register your models here.
from . import models as shield_models

# admin.site.register(shield_models.ShieldModel)
# admin.site.register(shield_models.PointsOfInterest)
admin.site.register(shield_models.Route)
# admin.site.register(shield_models.Location)
# admin.site.register(shield_models.Biometric)
# admin.site.register(shield_models.Hierarchie)
admin.site.register(shield_models.ShieldJoinRequest)
admin.site.register(shield_models.WalkieTalkie)


@admin.register(shield_models.ShieldModel)
class Shield(admin.ModelAdmin):
    list_display = ['shield_name','shield_code', 'shield_joining_code', 'created_at']


@admin.register(shield_models.Location)
class Location(admin.ModelAdmin):
    list_display = ['userprofile', 'shield', 'point_of_interest', 'created_at']


@admin.register(shield_models.Hierarchie)
class Hierarchie(admin.ModelAdmin):
    list_display = ['shield', 'member', 'hierarchy']


@admin.register(shield_models.Biometric)
class Biometric(admin.ModelAdmin):
    list_display = ['biometric_code', 'userprofile', 'shield']


@admin.register(shield_models.PointsOfInterest)
class PointsOfInterest(admin.ModelAdmin):
    list_display = ['poi_tag_name', 'poi_address']
