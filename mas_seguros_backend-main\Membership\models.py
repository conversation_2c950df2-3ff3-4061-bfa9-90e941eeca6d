from django.db import models
import datetime
from django.utils.crypto import get_random_string
from Account.models import UserProfile
from AdminSide.CompanyDashboard import models as company_models

# Create your models here.
def generate_order_id():
    return get_random_string(length=8)

def generate_transaction_id():
    return get_random_string(length=10)
level_1 = 'Level_1'
level_2 = 'Level_2'
level_3 = 'Level_3'
level_4 = 'Level_4'
membership_choice = [
    (level_1, level_1),
    (level_2, level_2),
    (level_3, level_3),
    (level_4, level_4),
]
effected = 'Effected'
failed = 'Failed'
conditions_choice = [
    (effected, effected),
    (failed, failed)
]


class MembershipModel(models.Model):
    userprofile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    companyprofile = models.ForeignKey(company_models.CompanyProfileModel, on_delete=models.CASCADE, null=True,
                                       blank=True)
    order_id = models.Char<PERSON>ield(default=generate_order_id, max_length=10)
    membership_end_date = models.DateField(null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    transaction_id = models.CharField(default=generate_transaction_id, max_length=10)
    membership = models.CharField(max_length=100, choices=membership_choice)
    unitary_amount = models.IntegerField()
    vat_amount = models.IntegerField()
    total_amount = models.IntegerField()
    conditions = models.CharField(choices=conditions_choice, max_length=100)
    payment_address = models.CharField(max_length=100)
    ipaddress = models.CharField(max_length=100)
    payment_method = models.CharField(max_length=100)
