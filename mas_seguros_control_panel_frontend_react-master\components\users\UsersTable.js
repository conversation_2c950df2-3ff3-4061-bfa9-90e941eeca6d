import React, { Fragment, useState } from "react";
import Table from "../Table";
import { Menu, Transition } from "@headlessui/react";
import classNames from "classnames";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import Badge from "../Badge";
import { format } from "date-fns";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const UsersTable = ({
  users = [],
  isLoading,
  isError,
  error,
  sort,
  setSort
}) => {
  return (
    <Table
      wrapperClassName="pb-28 no-scrollbar"
      dataCount={users.length}
      isLoading={isLoading}
      isError={isError}
      error={error}
    >
      <Table.Thead>
        <Table.Tr>
          <Table.Th sort={sort} setSort={setSort} sortable name="id">ID Usuario</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable name="full_name">Nombre</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable name="phone">Teléfono</Table.Th>
          <Table.Th>Correo</Table.Th>
          <Table.Th sort={sort} setSort={setSort} sortable name="created_at">Fecha de Creación</Table.Th>
          <Table.Th>Tipo</Table.Th>
          <Table.Th>Estado</Table.Th>
          <Table.Th>Acción</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {!isLoading &&
          !isError &&
          users?.map((user) => (
            <Row user={user} key={user?.user?.id || user.id} />
          ))}
      </Table.Tbody>
    </Table>
  );
};


const Row = ({ user }) => {
  return (
    <Table.Tr>
      <Table.Td>{user?.user?.id}</Table.Td>
      <Table.Td className="capitalize">{user.full_name}</Table.Td>
      <Table.Td>{user.phone}</Table.Td>
      <Table.Td>{user?.user?.email}</Table.Td>
      <Table.Td>{format(new Date(user.created_at), 'dd/MM/yy')}</Table.Td>
      <Table.Td>{user.user_type}</Table.Td>
      <Table.Td>
        {
          user.suspend ? (
            <Badge.Md text="Suspendido" className="bg-red-100 text-danger" />
          )
            : (
              <Badge.Md text="Activo" className="bg-green-100 text-green-600" />
            )
        }
      </Table.Td>
      <Table.Td>
        <ActionBtn user={user} />
      </Table.Td>
    </Table.Tr>
  )
}

const ActionBtn = ({ user }) => {
  const { axios } = useAxios();
  const [isLoading, setIsLoading] = useState(false);

  const handleSuspendUser = async () => {
    try {
      setIsLoading(true);
      const userId = user?.user?.id;

      if (!userId) {
        toast.error('ID de usuario no encontrado');
        return;
      }

      const response = await axios.post('/adminside/api/dashboard/suspend_users/', {
        id: userId,
        suspended: !user.suspend ? "true" : "false"
      });

      if (response.data.success) {
        toast.success(response.data.message || 'Estado del usuario actualizado');
        // Refresh the page to show updated status
        window.location.reload();
      } else {
        toast.error(response.data.message || 'Error al actualizar el estado del usuario');
      }
    } catch (error) {
      console.error('Error suspending user:', error);
      toast.error(error.response?.data?.message || 'Error al actualizar el estado del usuario');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="inline-flex w-full items-center justify-center gap-2 rounded-md  bg-accent px-4 py-2 text-sm font-medium text-black hover:bg-gray-50 focus:outline-none focus:ring-2  focus:ring-primary">
          Acción
          <ChevronDownIcon className="-mr-1 ml-2 h-5 w-5" aria-hidden="true" />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-[1]  mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            <Menu.Item>
              {({ active }) => (
                <Link
                  href={`/users/${user?.user?.id}`}
                  className={classNames(
                    active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                    "block px-4 py-2 text-sm"
                  )}
                >
                  Ver detalles
                </Link>
              )}
            </Menu.Item>
            <Menu.Item>
              {({ active }) => (
                <button
                  onClick={handleSuspendUser}
                  disabled={isLoading}
                  className={classNames(
                    active ? "bg-gray-100 text-gray-900" : "text-gray-700",
                    "block w-full text-left px-4 py-2 text-sm",
                    isLoading && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {isLoading ? "Procesando..." : user.suspend ? "Activar cuenta" : "Suspender cuenta"}
                </button>
              )}
            </Menu.Item>
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default UsersTable;
