import random

from django.shortcuts import render
from rest_framework.permissions import IsAuthenticated

# Create your views here.

from Account.models import UserProfile

from django.views.decorators.csrf import csrf_exempt

from rest_framework.views import APIView
from Ticket import models as ticket_model
from Ticket import serializers as ticket_serializers
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from rest_framework.parsers import JSONParser
from django.http import HttpResponse, JsonResponse
from rest_framework.generics import get_object_or_404


# Create your views here.


class Ticket(APIView):
    permission_classes = (IsAuthenticated,)

    # TODO: get Requesting user  Tickets
    def get(self, request):
        if request.user:
            data = ticket_model.Ticket.objects.filter(user=request.user.userprofile).prefetch_related(
                'title').order_by('-id')
        else:
            data = ticket_model.Ticket.objects.prefetch_related('title').order_by('-id')
        final_response = list()
        for ticket_obj in data:
            ticket = dict()
            ticket["id"] = ticket_obj.id
            ticket["ticket_num"] = ticket_obj.ticket_num
            ticket["description"] = ticket_obj.description
            ticket["title"] = ticket_obj.title.title
            ticket["resolved"] = ticket_obj.resolved
            ticket["created_at"] = str(ticket_obj.created_at)
            ticket["updated_at"] = str(ticket_obj.updated_at)
            user_info = dict()
            user_info["id"] = ticket_obj.user.id
            user_info["full_name"] = ticket_obj.user.full_name
            user_info["lat"] = ticket_obj.user.lat
            user_info["long"] = ticket_obj.user.long
            ticket["user"] = user_info
            final_response.append(ticket)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=final_response,
                                           msg='all tickets'), status.HTTP_200_OK)

    def post(self, request):
        subject_obj = ticket_model.TicketSubject.objects.filter(id=request.data.get("ticket_subject_id"))
        user = request.user
        if user:
            if subject_obj:
                ticket_id = random.randint(0, 99999)
                new_data = request.data.copy()
                new_data["title"] = subject_obj[0].id
                new_data["user"] = user.userprofile.id
                new_data['ticket_num'] = 'Ticket #{}'.format(ticket_id)
                ticket_serializer = ticket_serializers.TicketSerializer(data=new_data)
                response = ticket_serializer.is_valid(raise_exception=False)
                if response:
                    ticket_obj = ticket_serializer.save()
                    ticket_obj.ticket_num = 'Ticket #{}'.format(ticket_id)
                    ticket_obj.save()
                    return Response(
                        backend_utils.success_response(status_code=status.HTTP_200_OK, data=ticket_serializer.data,
                                                       msg='Ticket succesfully created'))
                else:
                    print(ticket_serializer.error_messages)
                    return Response(
                        backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                       msg=list(ticket_serializer.errors.items())[0][1]),
                        status.HTTP_400_BAD_REQUEST)

            else:
                print(subject_serializer.error_messages)
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg=list(subject_serializer.errors.items())[0][1]),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(subject_serializer.errors.items())[0][1]),
                status.HTTP_400_BAD_REQUEST)


class TicketSubject(APIView):
    # permission_classes = (IsAuthenticated,)
    def get(self, request):
        data = ticket_model.TicketSubject.objects.all()
        serializer = ticket_serializers.TicketSubjectSerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='ticket subject'), status.HTTP_200_OK)

    def put(self, request, pk=None, format=None):
        post = get_object_or_404(ticket_model.TicketSubject.objects.all(), pk=pk)
        serializer = ticket_serializers.TicketSubjectSerializer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = ticket_serializers.TicketSubjectSerializer(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        subject_obj = serializer.save()
        subject_obj.save()
        serializer = ticket_serializers.TicketSubjectSerializer(subject_obj)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='ticket subject succuesfully created'))
