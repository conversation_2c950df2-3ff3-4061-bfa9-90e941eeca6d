# Generated by Django 5.2.3 on 2025-06-14 11:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
        ('Shield', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Sos',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(blank=True, choices=[('Sos enviada', 'Sos enviada'), ('Ayuda enviada', 'Ayuda enviada'), ('Sos resuelta', 'Sos resuelta')], db_index=True, max_length=100, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('lat', models.CharField(blank=True, max_length=40, null=True)),
                ('long', models.Char<PERSON>ield(blank=True, max_length=40, null=True)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
        ),
        migrations.CreateModel(
            name='SosComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comment', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('sos', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Sos.sos')),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='SosEvidence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evidence', models.FileField(blank=True, null=True, upload_to='sos_evidence')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('sos', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Sos.sos')),
            ],
        ),
        migrations.CreateModel(
            name='SosModifyHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(blank=True, choices=[('Sos enviada', 'Sos enviada'), ('Ayuda enviada', 'Ayuda enviada'), ('Sos resuelta', 'Sos resuelta')], max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('sos', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Sos.sos')),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
        migrations.AddIndex(
            model_name='sos',
            index=models.Index(fields=['sender', '-created_at'], name='Sos_sos_sender__cfde4f_idx'),
        ),
        migrations.AddIndex(
            model_name='sos',
            index=models.Index(fields=['shield', '-created_at'], name='Sos_sos_shield__c98481_idx'),
        ),
        migrations.AddIndex(
            model_name='sos',
            index=models.Index(fields=['active', '-created_at'], name='Sos_sos_active_22110a_idx'),
        ),
        migrations.AddIndex(
            model_name='soscomment',
            index=models.Index(fields=['sos', '-created_at'], name='Sos_soscomm_sos_id_4f3b94_idx'),
        ),
        migrations.AddIndex(
            model_name='sosevidence',
            index=models.Index(fields=['sos', '-created_at'], name='Sos_sosevid_sos_id_3d3047_idx'),
        ),
    ]
