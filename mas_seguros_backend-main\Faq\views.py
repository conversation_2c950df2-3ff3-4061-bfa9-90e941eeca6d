from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework.generics import get_object_or_404
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status
from django.shortcuts import render, HttpResponse
from rest_framework.parsers import <PERSON><PERSON>NPars<PERSON>
from rest_framework.response import Response
from . import models as faq_models, serializers as faq_serializers
from mas_seguros_backend import utils as backend_utils
from django.http import JsonResponse
from rest_framework.authtoken.models import Token
from django.contrib.auth.hashers import check_password
from django.views.decorators.csrf import csrf_exempt


# Create your views here.


class FaqCategories(APIView):
    permission_classes = (IsAuthenticated,)

    # serializer_class = faq_serializers.FaqCategorySerializer

    def get(self, request):
        data = faq_models.FaqCategory.objects.all()
        serializer = faq_serializers.FaqCategorySerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='Faq Categories'), status.HTTP_200_OK)

    def put(self, request, pk=None, format=None):
        post = get_object_or_404(faq_models.FaqCategory.objects.all(), pk=pk)
        serializer = faq_serializers.FaqCategorySerializer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = faq_serializers.FaqCategoryRequestSerializer(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            faq_obj = faq_models.FaqCategory.objects.filter(name=request.data.get('category_name'))
            if not faq_obj:
                category_obj = faq_models.FaqCategory.objects.create(name=request.data.get('category_name'))
                serializer = faq_serializers.FaqCategorySerializer(category_obj)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                msg='Faq category added succesfully'))
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                msg='Faq category already present'))
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        


class FaqQuestions(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = faq_serializers.FaqQuestionsRequestSerializer
    category_serializer_class = faq_serializers.FaqQuestionSerializer

    def get(self, request):
        serializer = self.category_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        category_id = request.query_params.get('category_id')
        data = faq_models.FaqQuestion.objects.filter(category_id=category_id)
        if data:
            serializer = faq_serializers.FaqQuestionCompleteSerializer(data, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Faq Questions'), status.HTTP_200_OK)
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='Questions Not Found'), status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        category_obj = faq_models.FaqCategory.objects.filter(id=request.data.get('category_id')).last()
        if category_obj:
            obj = faq_models.FaqQuestion.objects.create(question=request.data.get('question'),
                                                        answer=request.data.get('answer'), category=category_obj)
            serializer = faq_serializers.FaqQuestionCompleteSerializer(obj)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='question added succesfully'))
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='Category Not found'), status.HTTP_400_BAD_REQUEST)

