import React from "react";
import Link from "next/link";
import Admin from "@/components/layouts/Admin";
import UserCard from "@/components/layouts/user/UserCard";
import { ChevronLeftIcon } from "@heroicons/react/20/solid";
import UserTabNav from "@/components/layouts/user/UserTabNav";
import { useRouter } from "next/router";
import { useQuery } from "react-query";
import useAxios from "@/hooks/useAxios";

const UserLayout = ({ children, pageTitle = null, headerTitle = "" }) => {
  const router = useRouter();
  const { user_id } = router.query;
  const { axios } = useAxios();

  const fetchUserData = async () => {
    if (!user_id) return null;
    try {
      console.log(`Fetching user data for ID: ${user_id}`);
      const response = await axios.get(`adminside/api/roles/userprofile/`, {
        params: { id: user_id }
      });
      console.log("User data response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching user data:", error);
      console.error("Error details:", error.response?.data);
      return null;
    }
  };

  const { data: userData, isLoading, isError } = useQuery(
    [`user-${user_id}-data`],
    fetchUserData,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  const user = userData?.data;

  return (
    <Admin pageTitle={pageTitle} headerTitle={headerTitle}>
      <section className="container-padding space-y-5">
        <Link
          href="/users"
          className="inline-flex items-center pt-5 hover:text-primary hover:underline"
        >
          <ChevronLeftIcon className="h-8 w-8" />
          <span>Volver</span>
        </Link>

        <h2 className="text-2xl font-medium capitalize">
          {isLoading ? "Cargando..." : (
            user?.userprofile?.full_name ||
            user?.full_name ||
            (user?.user ? `${user.user.first_name || ''} ${user.user.last_name || ''}` : '') ||
            "Usuario"
          )}
        </h2>
      </section>

      <section className="container-padding mt-6">
        <div className="gap-5 2xl:flex">
          <div className="w-full flex-shrink-0 2xl:max-w-xs">
            <UserCard data={user} isSuccess={!isLoading && !isError && !!user} />
          </div>

          <div className="flex-grow">
            <UserTabNav userId={user_id} />

            {children}
          </div>
        </div>
      </section>
    </Admin>
  );
};

export default UserLayout;
