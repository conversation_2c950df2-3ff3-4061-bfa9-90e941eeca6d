from abc import ABC

from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from Faq import models as faq_models


class FaqQuestionSerializer(serializers.Serializer):
    category_id = serializers.IntegerField(required=True)


class FaqQuestionCompleteSerializer(serializers.ModelSerializer):
    class Meta:
        model = faq_models.FaqQuestion
        fields = ['question', 'answer', 'category', 'id']


class FaqCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = faq_models.FaqCategory
        fields = ['name', 'id']


class FaqSerializerQuestions(serializers.Serializer):
    question = serializers.Char<PERSON><PERSON>(max_length=200, required=True)
    answer = serializers.Char<PERSON><PERSON>(max_length=2000, required=True)
    category_id = serializers.CharField(max_length=20, required=True)


class GetFaqQuestionIdSerializer(serializers.Serializer):
    question_id = serializers.IntegerField(required=True)
    question = serializers.Char<PERSON><PERSON>(max_length=200, required=True)
    answer = serializers.CharField(max_length=2000, required=True)


class UpdateFaqCategorySerializer(serializers.Serializer):
    name = serializers.CharField(max_length=100, required=True)
    id = serializers.CharField(max_length=100, required=True)
