from django.db import models

import Shield.utils
from Account import models as account_models
from Alert import models as alert_model
import datetime
from mas_seguros_backend import settings as backend_setting
from AdminSide.AdminAPi import models as adminapi_models
import random

def generate_shield_code():
    unique_id = random.randint(0, 999999)
    return f'ESC{unique_id}'

def generate_shield_join_code():
    return Shield.utils.create_shield_join_code()

def generate_route_id():
    return str(random.randint(0, 999999))

def generate_biometric_code():
    unique_id = random.randint(0, 999999)
    one_digit = random.randint(0, 9)
    return f'E{unique_id}RF{one_digit}'


# Create your models here.
class PointsOfInterest(models.Model):
    poi_address = models.CharField(max_length=300, null=True, blank=True)
    poi_tag_name = models.CharField(max_length=100, null=True, blank=True)
    poi_lat = models.CharField(max_length=100, null=True, blank=True)
    poi_long = models.CharField(max_length=100, null=True, blank=True)
    admin = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.poi_tag_name


shield_choice = (
    ("Corporate", "corporate"),
    ("Individual", "individual"),
)


class ShieldModel(models.Model):
    members = models.ManyToManyField(account_models.UserProfile, related_name='shield_members', blank=True)
    admin = models.ManyToManyField(account_models.UserProfile, related_name='shield_admin', blank=True)
    shield_super_admin = models.ForeignKey(account_models.UserProfile, related_name='shield_super_admin', null=True,
                                           blank=True, on_delete=models.CASCADE)
    shield_code = models.CharField(max_length=12, default=generate_shield_code, null=True, blank=True)
    shield_joining_code = models.CharField(max_length=12, default=generate_shield_join_code, null=True,
                                           blank=True)
    alert = models.ManyToManyField(alert_model.AlertModel, related_name='shield_alerts', blank=True)
    shield_type = models.CharField(choices=shield_choice, max_length=100, null=False)
    shield_name = models.CharField(max_length=100, null=True, blank=True)
    locations = models.ManyToManyField(PointsOfInterest, blank=True)
    promo_code = models.ForeignKey(adminapi_models.PromoCode, on_delete=models.CASCADE, null=True, blank=True)
    logo = models.ImageField(null=True, blank=True, upload_to='shield_logo')
    condition = models.BooleanField(default=False)
    suspend = models.BooleanField(null=True, blank=True, default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.shield_name

    @property
    def members_count(self):
        return self.members.count()

    @property
    def logo_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.logo.url)
        except:
            return None


# class MemberLocation(models.Model):
#     userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.SET_NULL, null=True, blank=True)
#     lat = models.FloatField(null=True, blank=True)
#     long = models.FloatField(null=True, blank=True)

class Route(models.Model):
    # Route ID
    route_id = models.CharField(max_length=6, default=generate_route_id, null=True, blank=True, db_index=True)
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    member = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    max_speed = models.CharField(max_length=50, null=False, blank=False)
    minimum_phone_battery = models.CharField(max_length=100, null=False, blank=False)
    starting_poi = models.ForeignKey(PointsOfInterest, on_delete=models.CASCADE, null=True, blank=True,
                                     related_name='starting_poi')
    ending_poi = models.ForeignKey(PointsOfInterest, on_delete=models.CASCADE, null=True, blank=True,
                                   related_name='ending_poi')
    route_completed = models.BooleanField(default=False, db_index=True)
    route_starting_time = models.TimeField(auto_now=False, auto_now_add=True, null=True, blank=True)
    route_ending_time = models.TimeField(auto_now=False, auto_now_add=False, null=True, blank=True)
    route_date = models.DateField(default=datetime.date.today, db_index=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['shield', 'member', '-route_date']),
            models.Index(fields=['shield', '-route_date', '-created_at']),
            models.Index(fields=['member', '-route_date', '-created_at']),
            models.Index(fields=['shield', 'member', 'route_date']),
            models.Index(fields=['route_date', '-created_at']),
            models.Index(fields=['route_completed', '-route_date']),
        ]
        ordering = ['-route_date', '-created_at']


class Location(models.Model):
    route = models.ForeignKey(Route, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    location = models.CharField(max_length=255)
    location_changed = models.BooleanField(default=False, null=True)
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    point_of_interest = models.ForeignKey(PointsOfInterest, on_delete=models.CASCADE, null=True, blank=True)
    lat = models.CharField(max_length=40, null=True, blank=True)
    long = models.CharField(max_length=40, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['route', 'created_at']),
            models.Index(fields=['route', '-created_at']),
            models.Index(fields=['userprofile', '-created_at']),
            models.Index(fields=['shield', '-created_at']),
            models.Index(fields=['route', 'userprofile', '-created_at']),
        ]
        ordering = ['created_at']

    @property
    def lat_long(self):
        return '{},{}'.format(self.lat, self.long)


Colaborativo = 'Colaborativo'
Solitario = 'Solitario'
Fantasma = 'Fantasma'
Hierarchy_choice = (
    (Colaborativo, Colaborativo),
    (Solitario, Solitario),
    (Fantasma, Fantasma),
)

pending = 'Pendiente'
approved = 'Aprobado'
rejected = 'Rechazado'
request_status = (
    (pending, pending),
    (approved, approved),
    (rejected, rejected),
)


class ShieldJoinRequest(models.Model):
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True)
    shield_super_admin = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True,
                                           related_name="super_admin_shield")
    shield_requester = models.ForeignKey(account_models.UserProfile, on_delete=models.SET_NULL, null=True, blank=True,
                                         related_name="requester")
    status = models.CharField(choices=request_status, max_length=80, default=False)
    already_used_as_notification = models.BooleanField(null=True, blank=True, default=False)


class Hierarchie(models.Model):
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True)
    member = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    hierarchy = models.CharField(max_length=50, choices=Hierarchy_choice, null=True, blank=True)


ENTRADA = 'ENTRADA'
SALIDA = 'SALIDA'
biometric_choices = [
    (ENTRADA, ENTRADA),
    (SALIDA, SALIDA)
]


class Biometric(models.Model):
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    biometric_code = models.CharField(max_length=10, default=generate_biometric_code,
                                      null=True, blank=True, db_index=True)
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    lat = models.CharField(max_length=40, null=True, blank=True)
    long = models.CharField(max_length=40, null=True, blank=True)
    address = models.CharField(max_length=300, null=True, blank=True)
    image = models.ImageField(null=True, blank=True, upload_to='biometric_images')
    type = models.CharField(max_length=100, choices=biometric_choices, null=True, blank=True, default=SALIDA, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['userprofile', '-created_at']),
            models.Index(fields=['shield', '-created_at']),
            models.Index(fields=['type', '-created_at']),
            models.Index(fields=['userprofile', 'type', '-created_at']),
            models.Index(fields=['shield', 'userprofile', '-created_at']),
        ]
        ordering = ['-created_at']

    @property
    def image_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.image.url)
        except:
            return None


class WalkieTalkie(models.Model):
    shield = models.ForeignKey(ShieldModel, on_delete=models.CASCADE, null=True, blank=True)
    member = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    listen_audio = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
