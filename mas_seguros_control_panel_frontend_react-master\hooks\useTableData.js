import { orderBy } from "lodash";
import { useEffect, useMemo, useState } from 'react'
import { toast } from "react-hot-toast";
import { useQuery } from "react-query";
import useAxios from "./useAxios";


const useTableData = ({
  enabled = true,
  dataUrl = '',
  dataCallback = (resp) => {
    const data = resp?.data?.data ?? [];
    // Handle nested data structure if needed
    return Array.isArray(data) ? data : [];
  },
  baseURL = process.env.NEXT_PUBLIC_BACKEND_URL,
  queryKeys = [],
  pageSize = 10,
  noAuth = false,
  initialSort = { field: 'id', direction: 'desc' }
}) => {
  // Custom Axios instance
  const { axios } = useAxios({
    baseURL: baseURL,
    noAuth: noAuth,
  });

  // States
  const [tempFilters, setTempFilters] = useState({});
  const [filters, setFilters] = useState({});
  const [search, setSearch] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sort, setSort] = useState(initialSort);



  // function to fetch data
  const fetchData = () => {
    // Build query parameters for backend filtering
    const params = new URLSearchParams();

    // Add search parameter
    if (search && search.trim().length > 0) {
      params.append('search', search.trim());
    }

    // Add filter parameters - handle multiple values properly
    Object.entries(filters).forEach(([filterKey, filterValues]) => {
      if (Array.isArray(filterValues) && filterValues.length > 0) {
        // Send all filter values for the same key
        filterValues.forEach(value => {
          params.append(filterKey, value);
        });
      }
    });

    const queryString = params.toString();
    const url = queryString ? `${dataUrl}?${queryString}` : dataUrl;

    console.log('Fetching data with URL:', url); // Debug log
    return axios.get(url);
  };

  // React-query for data fetching with dependency on filters and search
  const { isLoading, isError, refetch, isRefetching, isSuccess, data: responseData, error } = useQuery(
    [...queryKeys, filters, search],
    fetchData,
    {
      refetchOnWindowFocus: false,
      staleTime: 30000, // Data remains fresh for 30 seconds
      cacheTime: 300000, // Cache data for 5 minutes
      enabled: enabled,
    }
  );

  useEffect(() => {
    if (isError) {
      toast.error(error.message);
    }
  }, [isError]);




  useEffect(() => {
    resetPage()
  }, [filters])

  const dataItems = dataCallback(responseData) ?? [];

  // Since filtering is now handled by backend, we just return the data items
  const allDataUnsorted = useMemo(() => {
    // Check if items is an array
    if (!Array.isArray(dataItems)) {
      console.warn('dataItems is not an array:', dataItems);
      return [];
    }

    return dataItems;
  }, [dataItems])


  // Sorting
  const allData = useMemo(() => {
    // Check if allDataUnsorted is an array and has items
    if (!Array.isArray(allDataUnsorted) || allDataUnsorted.length === 0) {
      return [];
    }

    let sortedProducts = [...allDataUnsorted];
    const fieldsNames = Object.keys(allDataUnsorted[0] ?? {});

    if (fieldsNames.length > 0 && fieldsNames.includes(sort.field)) {
      sortedProducts = orderBy(sortedProducts, sort.field, sort.direction);
    }

    return sortedProducts;
  }, [sort, allDataUnsorted]);





  // Since search is now handled by backend, we just return the sorted data
  const data = useMemo(() => {
    return allData ?? [];
  }, [allData])


  // current data for the page
  const currentTableData = useMemo(() => {
    const firstPageIndex = (currentPage - 1) * pageSize;
    const lastPageIndex = firstPageIndex + pageSize;
    return data.slice(firstPageIndex, lastPageIndex);
  }, [currentPage, data]);

  // Apply filters
  const applyFilters = () => setFilters(tempFilters)

  // Reset to initial page
  const resetPage = () => setCurrentPage(1)

  return {
    tempFilters,
    setTempFilters,

    filters,
    setFilters,

    applyFilters,

    search,
    setSearch,

    sort,
    setSort,

    isLoading,
    refetch,
    isRefetching,
    isError,
    isSuccess,
    error,

    allData,
    currentTableData,

    currentPage,
    setCurrentPage,

    resetPage
  }
}

export default useTableData