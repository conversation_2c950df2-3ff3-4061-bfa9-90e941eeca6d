from django.urls import path, reverse, include, re_path
from . import views as shield_views

# router = routers.DefaultRouter()
# router.register('promo', admin_url.promo_codes)
urlpatterns = [
    path("all-shields/", shield_views.AllShields.as_view()),
    path("suspend-shield/", shield_views.SuspendShield.as_view()),
    path("shield-point-of-interest/", shield_views.ShieldPointOfInterest.as_view()),
    path("point-of-interest-visit-history/", shield_views.PointOfInterestVisitHistory.as_view()),
    path("shield-members/", shield_views.ShieldMembers.as_view()),
    path("shield-members-locations/", shield_views.ShieldMemberLocations.as_view()),
    path("shield-members-routes/", shield_views.ShieldMemberRoutes.as_view()),
    path("shield-alert-and-sos/", shield_views.ShieldAlertAndSos.as_view()),
    path("shield-biometrics/", shield_views.ShieldBiometrics.as_view()),
    path("shield-update/<int:pk>", shield_views.update_sheild, name="update_sheild"),
    path('get-single/<int:id>', shield_views.sheild_get_single.as_view()),
    path('sheild-membership/', shield_views.sheild_membership.as_view()),
    path('all-biometrics/', shield_views.AllBiometrics.as_view()),
    path('all-biometrics-of-user/', shield_views.UserBiometrics.as_view()),
    path('user-biometrics-optimized/', shield_views.GetUserBiometricsOptimized.as_view()),
    path('download-all-biometrics-of-user/', shield_views.DownloadUserBiometrics.as_view()),
    path('download-route-history-admin/', shield_views.DownloadRouteHistory.as_view()),
]
