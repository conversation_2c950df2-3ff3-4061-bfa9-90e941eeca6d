{".class": "MypyFile", "_fullname": "Shield.views", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.APIView", "name": "APIView", "type": {".class": "AnyType", "missing_import_name": "Shield.views.APIView", "source_any": null, "type_of_any": 3}}}, "AddShieldMembersInAddMemberViewMobile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile", "name": "AddShieldMembersInAddMemberViewMobile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.AddShieldMembersInAddMemberViewMobile", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.AddMemberToShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.AddShieldMembersInAddMemberViewMobile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.AddShieldMembersInAddMemberViewMobile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AllowAny": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.AllowAny", "name": "AllowAny", "type": {".class": "AnyType", "missing_import_name": "Shield.views.AllowAny", "source_any": null, "type_of_any": 3}}}, "AssignShieldAdmin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.AssignShieldAdmin", "name": "As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.AssignShieldAdmin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.AssignShieldAdmin", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.AssignShieldAdmin.get", "name": "get", "type": null}}, "get_request_serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.AssignShieldAdmin.get_request_serializer_class", "name": "get_request_serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.AssignShieldAdmin.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.AssignShieldAdmin.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.AssignShieldAdmin.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.AssignAdminShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.AssignShieldAdmin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.AssignShieldAdmin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeShieldCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ChangeShieldCode", "name": "ChangeShieldCode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ChangeShieldCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ChangeShieldCode", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldCode.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ChangeShieldCode.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldCode.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ChangeShieldCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ChangeShieldCode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeShieldImage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ChangeShieldImage", "name": "ChangeShieldImage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ChangeShieldImage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ChangeShieldImage", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldImage.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ChangeShieldImage.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldImage.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldImageChangeSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ChangeShieldImage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ChangeShieldImage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeShieldMemberHierarchies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ChangeShieldMemberHierarchies", "name": "ChangeShieldMemberHierarchies", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ChangeShieldMemberHierarchies", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ChangeShieldMemberHierarchies", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ChangeShieldMemberHierarchies.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldMemberHierarchies.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdAndMemberIDHierarchySerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ChangeShieldMemberHierarchies.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ChangeShieldMemberHierarchies", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeShieldName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ChangeShieldName", "name": "ChangeShieldName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ChangeShieldName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ChangeShieldName", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ChangeShieldName.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ChangeShieldName.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ChangeShieldNameSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ChangeShieldName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ChangeShieldName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckUserInShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.CheckUserInShield", "name": "CheckUserInShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.CheckUserInShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.CheckUserInShield", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.CheckUserInShield.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.CheckUserInShield.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.CheckUserInShield.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.CheckUserInShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.CheckUserInShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CreateShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.CreateShield", "name": "CreateShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.CreateShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.CreateShield", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.CreateShield.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.CreateShield.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.CreateShield.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.CreateShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.CreateShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.CreateShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.DeleteShield", "name": "DeleteShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.DeleteShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.DeleteShield", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.DeleteShield.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.DeleteShield.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.DeleteShield.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.DeleteShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.DeleteShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExitShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ExitShield", "name": "ExitShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ExitShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ExitShield", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ExitShield.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ExitShield.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ExitShield.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ExitShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ExitShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.FileResponse", "name": "FileResponse", "type": {".class": "AnyType", "missing_import_name": "Shield.views.FileResponse", "source_any": null, "type_of_any": 3}}}, "GetOneTimeShieldStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.GetOneTimeShieldStatus", "name": "GetOneTimeShieldStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.GetOneTimeShieldStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.GetOneTimeShieldStatus", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.GetOneTimeShieldStatus.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.GetOneTimeShieldStatus.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.GetOneTimeShieldStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.GetOneTimeShieldStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.GetShield", "name": "GetShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.GetShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.GetShield", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.GetShield.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.GetShield.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.GetShield.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.GetShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.GetShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetUserShields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.GetUserShields", "name": "GetUserShields", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.GetUserShields", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.GetUserShields", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.GetUserShields.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.GetUserShields.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.GetUserShields.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.GetUserShields", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.HttpResponse", "name": "HttpResponse", "type": {".class": "AnyType", "missing_import_name": "Shield.views.HttpResponse", "source_any": null, "type_of_any": 3}}}, "IsAuthenticated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.IsAuthenticated", "name": "IsAuthenticated", "type": {".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}}}, "JSONParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.JSONParser", "name": "JSO<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "Shield.views.JSONParser", "source_any": null, "type_of_any": 3}}}, "JsonResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.JsonResponse", "name": "JsonResponse", "type": {".class": "AnyType", "missing_import_name": "Shield.views.JsonResponse", "source_any": null, "type_of_any": 3}}}, "MemberAudioStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.MemberAudioStatus", "name": "MemberAudioStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.MemberAudioStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.MemberAudioStatus", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.MemberAudioStatus.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.MemberAudioStatus.put", "name": "put", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.MemberAudioStatus.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.MemberAudioStatusSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.MemberAudioStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.MemberAudioStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Paragraph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Paragraph", "name": "Paragraph", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Paragraph", "source_any": null, "type_of_any": 3}}}, "PointOfInterestVisitHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.PointOfInterestVisitHistory", "name": "PointOfInterestVisitHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.PointOfInterestVisitHistory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.PointOfInterestVisitHistory", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.PointOfInterestVisitHistory.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.PointOfInterestVisitHistory.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.PointOfInterestVisitHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.PointOfInterestVisitHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Prefetch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Prefetch", "name": "Prefetch", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Prefetch", "source_any": null, "type_of_any": 3}}}, "Q": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Q", "name": "Q", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Q", "source_any": null, "type_of_any": 3}}}, "RemoveShieldMembersInAddMemberViewMobile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile", "name": "RemoveShieldMembersInAddMemberViewMobile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.RemoveShieldMembersInAddMemberViewMobile", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.RemoveMemberToShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.RemoveShieldMembersInAddMemberViewMobile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.RemoveShieldMembersInAddMemberViewMobile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Response", "name": "Response", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Response", "source_any": null, "type_of_any": 3}}}, "ShieldAlertAndSos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldAlertAndSos", "name": "ShieldAlertAndSos", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldAlertAndSos", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldAlertAndSos", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldAlertAndSos.get", "name": "get", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldAlertAndSos.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldAlertAndSos.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldAlertAndSos", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldBiometrics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldBiometrics", "name": "ShieldBiometrics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldBiometrics", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldBiometrics", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldBiometrics.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldBiometrics.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldBiometrics.post", "name": "post", "type": null}}, "post_serializer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldBiometrics.post_serializer", "name": "post_serializer", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.BiometricUserShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldBiometrics.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldBiometrics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldBiometrics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldBiometricsReport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldBiometricsReport", "name": "ShieldBiometricsReport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldBiometricsReport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldBiometricsReport", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldBiometricsReport.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldBiometricsReport.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldBiometricsReport.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldBiometricsReportSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldBiometricsReport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldBiometricsReport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldDetailsAgainstJoiningCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode", "name": "ShieldDetailsAgainstJoiningCode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldDetailsAgainstJoiningCode", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldJoiningCodeSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldDetailsAgainstJoiningCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldDetailsAgainstJoiningCode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldHierarchies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldHierarchies", "name": "ShieldHierarchies", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldHierarchies", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldHierarchies", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldHierarchies.get", "name": "get", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldHierarchies.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldHierarchies.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldHierarchies", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldJoiningRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldJoiningRequest", "name": "ShieldJoiningRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldJoiningRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldJoiningRequest", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldJoiningRequest.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldJoiningRequest.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldJoiningRequest.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldJoiningRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldJoiningRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberHierarchies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldMemberHierarchies", "name": "ShieldMemberHierarchies", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldMemberHierarchies", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldMemberHierarchies", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldMemberHierarchies.get", "name": "get", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberHierarchies.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetMemberIDSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldMemberHierarchies.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldMemberHierarchies", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldMemberLocations", "name": "ShieldMemberLocations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldMemberLocations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldMemberLocations", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldMemberLocations.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberLocations.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberLocations.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetMemberIDSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldMemberLocations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldMemberLocations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocationsBattery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldMemberLocationsBattery", "name": "ShieldMemberLocationsBattery", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldMemberLocationsBattery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldMemberLocationsBattery", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldMemberLocationsBattery.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberLocationsBattery.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberLocationsBattery.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldMemberLocationsBattery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldMemberLocationsBattery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberRoutes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldMemberRoutes", "name": "ShieldMemberRoutes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldMemberRoutes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldMemberRoutes", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldMemberRoutes.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberRoutes.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMemberRoutes.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetMemberIDSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldMemberRoutes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldMemberRoutes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMembers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldMembers", "name": "ShieldMembers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldMembers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldMembers", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldMembers.get", "name": "get", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldMembers.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldMembers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldMembers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldPending": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldPending", "name": "ShieldPending", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldPending", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldPending", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldPending.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPending.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldPending.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldPending", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldPointOfInterest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldPointOfInterest", "name": "ShieldPointOfInterest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldPointOfInterest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldPointOfInterest", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldPointOfInterest.delete", "name": "delete", "type": null}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldPointOfInterest.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPointOfInterest.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldPointOfInterest.post", "name": "post", "type": null}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldPointOfInterest.put", "name": "put", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPointOfInterest.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serializer_class_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPointOfInterest.serializer_class_delete", "name": "serializer_class_delete", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetPointOfInterestIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serializer_class_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPointOfInterest.serializer_class_post", "name": "serializer_class_post", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.AddPointOfInterestShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serializer_class_put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldPointOfInterest.serializer_class_put", "name": "serializer_class_put", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.EditPointOfInterestShieldSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldPointOfInterest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldPointOfInterest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldRequesters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldRequesters", "name": "ShieldRequesters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldRequesters", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldRequesters", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldRequesters.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldRequesters.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldRequesters.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldRequesters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldRequesters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldUpdateJoiningStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.ShieldUpdateJoiningStatus", "name": "ShieldUpdateJoiningStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.ShieldUpdateJoiningStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.ShieldUpdateJoiningStatus", "builtins.object"], "names": {".class": "SymbolTable", "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldUpdateJoiningStatus.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.ShieldUpdateJoiningStatus.post", "name": "post", "type": null}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.ShieldUpdateJoiningStatus.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldUpdateJoiningStatusSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.ShieldUpdateJoiningStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.ShieldUpdateJoiningStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleDocTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.SimpleDocTemplate", "name": "SimpleDocTemplate", "type": {".class": "AnyType", "missing_import_name": "Shield.views.SimpleDocTemplate", "source_any": null, "type_of_any": 3}}}, "Spacer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Spacer", "name": "Spacer", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Spacer", "source_any": null, "type_of_any": 3}}}, "Sum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Sum", "name": "Sum", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Sum", "source_any": null, "type_of_any": 3}}}, "Table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.Table", "name": "Table", "type": {".class": "AnyType", "missing_import_name": "Shield.views.Table", "source_any": null, "type_of_any": 3}}}, "TableStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.TableStyle", "name": "TableStyle", "type": {".class": "AnyType", "missing_import_name": "Shield.views.TableStyle", "source_any": null, "type_of_any": 3}}}, "WalkieTalkieReceivers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.WalkieTalkieReceivers", "name": "WalkieTalkieReceivers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.WalkieTalkieReceivers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.WalkieTalkieReceivers", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.WalkieTalkieReceivers.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.WalkieTalkieReceivers.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.WalkieTalkieReceivers.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.ShieldJoiningRequestSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.WalkieTalkieReceivers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.WalkieTalkieReceivers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.views.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "account_models": {".class": "SymbolTableNode", "cross_ref": "Account.models", "kind": "Gdef"}, "alert_models": {".class": "SymbolTableNode", "cross_ref": "Alert.models", "kind": "Gdef"}, "backend_setting": {".class": "SymbolTableNode", "cross_ref": "mas_seguros_backend.settings", "kind": "Gdef"}, "backend_utils": {".class": "SymbolTableNode", "cross_ref": "mas_seguros_backend.utils", "kind": "Gdef"}, "canvas": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.canvas", "name": "canvas", "type": {".class": "AnyType", "missing_import_name": "Shield.views.canvas", "source_any": null, "type_of_any": 3}}}, "check_member_in_shield": {".class": "SymbolTableNode", "cross_ref": "mas_seguros_backend.decorator.check_member_in_shield", "kind": "Gdef"}, "csrf_exempt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.csrf_exempt", "name": "csrf_exempt", "type": {".class": "AnyType", "missing_import_name": "Shield.views.csrf_exempt", "source_any": null, "type_of_any": 3}}}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "default_storage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.default_storage", "name": "default_storage", "type": {".class": "AnyType", "missing_import_name": "Shield.views.default_storage", "source_any": null, "type_of_any": 3}}}, "getSampleStyleSheet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.getSampleStyleSheet", "name": "getSampleStyleSheet", "type": {".class": "AnyType", "missing_import_name": "Shield.views.getSampleStyleSheet", "source_any": null, "type_of_any": 3}}}, "get_object_or_404": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.get_object_or_404", "name": "get_object_or_404", "type": {".class": "AnyType", "missing_import_name": "Shield.views.get_object_or_404", "source_any": null, "type_of_any": 3}}}, "inch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.inch", "name": "inch", "type": {".class": "AnyType", "missing_import_name": "Shield.views.inch", "source_any": null, "type_of_any": 3}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "letter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.letter", "name": "letter", "type": {".class": "AnyType", "missing_import_name": "Shield.views.letter", "source_any": null, "type_of_any": 3}}}, "membership_models": {".class": "SymbolTableNode", "cross_ref": "Membership.models", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "sheild_membership": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.views.sheild_membership", "name": "sheild_membership", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.views.sheild_membership", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.views", "mro": ["Shield.views.sheild_membership", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.views.sheild_membership.get", "name": "get", "type": null}}, "permission_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.sheild_membership.permission_classes", "name": "permission_classes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "Shield.views.IsAuthenticated", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "serializer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.views.sheild_membership.serializer_class", "name": "serializer_class", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.serializers.GetShieldIdSerializer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.views.sheild_membership.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.views.sheild_membership", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "shield_models": {".class": "SymbolTableNode", "cross_ref": "Shield.models", "kind": "Gdef"}, "shield_serialziers": {".class": "SymbolTableNode", "cross_ref": "Shield.serializers", "kind": "Gdef"}, "shield_utils": {".class": "SymbolTableNode", "cross_ref": "Shield.utils", "kind": "Gdef"}, "status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "Shield.views.status", "source_any": null, "type_of_any": 3}}}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}, "ticket_models": {".class": "SymbolTableNode", "cross_ref": "Ticket.models", "kind": "Gdef"}, "user_passes_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.views.user_passes_test", "name": "user_passes_test", "type": {".class": "AnyType", "missing_import_name": "Shield.views.user_passes_test", "source_any": null, "type_of_any": 3}}}}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\views.py"}