{".class": "MypyFile", "_fullname": "Shield.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Biometric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Biometric", "name": "Biometric", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.Biometric", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Biometric", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Biometric.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.models.Biometric.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Biometric.Meta", "builtins.object"], "names": {".class": "SymbolTable", "indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.Meta.indexes", "name": "indexes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ordering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.Meta.ordering", "name": "ordering", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Biometric.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Biometric.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.address", "name": "address", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "biometric_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.biometric_code", "name": "biometric_code", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.image", "name": "image", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "Shield.models.Biometric.image_url", "name": "image_url", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "Shield.models.Biometric.image_url", "name": "image_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["Shield.models.Biometric"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "image_url of Biometric", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.lat", "name": "lat", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.long", "name": "long", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.type", "name": "type", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "userprofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Biometric.userprofile", "name": "userprofile", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Biometric.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Biometric", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Colaborativo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Colaborativo", "name": "Colaborativo", "type": "builtins.str"}}, "ENTRADA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ENTRADA", "name": "ENTRADA", "type": "builtins.str"}}, "Fantasma": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Fantasma", "name": "Fantasma", "type": "builtins.str"}}, "Hierarchie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Hierarchie", "name": "Hierarchie", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.Hierarchie", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Hierarchie", "builtins.object"], "names": {".class": "SymbolTable", "hierarchy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Hierarchie.hierarchy", "name": "hierarchy", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Hierarchie.member", "name": "member", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Hierarchie.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Hierarchie.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Hierarchie", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hierarchy_choice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "Shield.models.Hierarchy_choice", "name": "Hierarchy_choice", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Location": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Location", "name": "Location", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.Location", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Location", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Location.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.models.Location.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Location.Meta", "builtins.object"], "names": {".class": "SymbolTable", "indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.Meta.indexes", "name": "indexes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ordering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.Meta.ordering", "name": "ordering", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Location.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Location.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.lat", "name": "lat", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "lat_long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "Shield.models.Location.lat_long", "name": "lat_long", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "Shield.models.Location.lat_long", "name": "lat_long", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["Shield.models.Location"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lat_long of Location", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.location", "name": "location", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "location_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.location_changed", "name": "location_changed", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.long", "name": "long", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_of_interest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.point_of_interest", "name": "point_of_interest", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.route", "name": "route", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "userprofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Location.userprofile", "name": "userprofile", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Location.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Location", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointsOfInterest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.PointsOfInterest", "name": "PointsOfInterest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.PointsOfInterest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.PointsOfInterest", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.PointsOfInterest.__str__", "name": "__str__", "type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.admin", "name": "admin", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.poi_address", "name": "poi_address", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_lat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.poi_lat", "name": "poi_lat", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_long": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.poi_long", "name": "poi_long", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "poi_tag_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.poi_tag_name", "name": "poi_tag_name", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.PointsOfInterest.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.PointsOfInterest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.PointsOfInterest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Route", "name": "Route", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.Route", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Route", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.Route.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "Shield.models.Route.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.Route.Meta", "builtins.object"], "names": {".class": "SymbolTable", "indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.Meta.indexes", "name": "indexes", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ordering": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.Meta.ordering", "name": "ordering", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Route.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Route.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ending_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.ending_poi", "name": "ending_poi", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "max_speed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.max_speed", "name": "max_speed", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.member", "name": "member", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "minimum_phone_battery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.minimum_phone_battery", "name": "minimum_phone_battery", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_completed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.route_completed", "name": "route_completed", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.route_date", "name": "route_date", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_ending_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.route_ending_time", "name": "route_ending_time", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.route_id", "name": "route_id", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_starting_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.route_starting_time", "name": "route_starting_time", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "starting_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.starting_poi", "name": "starting_poi", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Route.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.Route.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.Route", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SALIDA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.SALIDA", "name": "SALIDA", "type": "builtins.str"}}, "Shield": {".class": "SymbolTableNode", "cross_ref": "Shield", "kind": "Gdef"}, "ShieldJoinRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.ShieldJoinRequest", "name": "ShieldJoinRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.ShieldJoinRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.ShieldJoinRequest", "builtins.object"], "names": {".class": "SymbolTable", "already_used_as_notification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldJoinRequest.already_used_as_notification", "name": "already_used_as_notification", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldJoinRequest.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_requester": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldJoinRequest.shield_requester", "name": "shield_requester", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_super_admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldJoinRequest.shield_super_admin", "name": "shield_super_admin", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldJoinRequest.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.ShieldJoinRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.ShieldJoinRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.ShieldModel", "name": "ShieldModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.ShieldModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.ShieldModel", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.ShieldModel.__str__", "name": "__str__", "type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.admin", "name": "admin", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "alert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.alert", "name": "alert", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.condition", "name": "condition", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.locations", "name": "locations", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.logo", "name": "logo", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "logo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "Shield.models.ShieldModel.logo_url", "name": "logo_url", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "Shield.models.ShieldModel.logo_url", "name": "logo_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["Shield.models.ShieldModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logo_url of ShieldModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "members": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.members", "name": "members", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "members_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "Shield.models.ShieldModel.members_count", "name": "members_count", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "Shield.models.ShieldModel.members_count", "name": "members_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["Shield.models.ShieldModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "members_count of ShieldModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "promo_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.promo_code", "name": "promo_code", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.shield_code", "name": "shield_code", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_joining_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.shield_joining_code", "name": "shield_joining_code", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.shield_name", "name": "shield_name", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_super_admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.shield_super_admin", "name": "shield_super_admin", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.shield_type", "name": "shield_type", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "suspend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.suspend", "name": "suspend", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.ShieldModel.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.ShieldModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.ShieldModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Solitario": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.Solitario", "name": "Solitario", "type": "builtins.str"}}, "WalkieTalkie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "Shield.models.<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "Shield.models.<PERSON><PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "Shield.models", "mro": ["Shield.models.<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.WalkieTalkie.created_at", "name": "created_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "listen_audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.WalkieTalkie.listen_audio", "name": "listen_audio", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.WalkieT<PERSON>ie.member", "name": "member", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.WalkieTalkie.shield", "name": "shield", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.WalkieTalkie.updated_at", "name": "updated_at", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "Shield.models.WalkieTalkie.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "Shield.models.<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "Shield.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "account_models": {".class": "SymbolTableNode", "cross_ref": "Account.models", "kind": "Gdef"}, "adminapi_models": {".class": "SymbolTableNode", "cross_ref": "AdminSide.AdminAPi.models", "kind": "Gdef"}, "alert_model": {".class": "SymbolTableNode", "cross_ref": "Alert.models", "kind": "Gdef"}, "approved": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.approved", "name": "approved", "type": "builtins.str"}}, "backend_setting": {".class": "SymbolTableNode", "cross_ref": "mas_seguros_backend.settings", "kind": "Gdef"}, "biometric_choices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "Shield.models.biometric_choices", "name": "biometric_choices", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "generate_biometric_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.generate_biometric_code", "name": "generate_biometric_code", "type": null}}, "generate_route_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.generate_route_id", "name": "generate_route_id", "type": null}}, "generate_shield_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.generate_shield_code", "name": "generate_shield_code", "type": null}}, "generate_shield_join_code": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "Shield.models.generate_shield_join_code", "name": "generate_shield_join_code", "type": null}}, "models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "Shield.models.models", "name": "models", "type": {".class": "AnyType", "missing_import_name": "Shield.models.models", "source_any": null, "type_of_any": 3}}}, "pending": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.pending", "name": "pending", "type": "builtins.str"}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "rejected": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "Shield.models.rejected", "name": "rejected", "type": "builtins.str"}}, "request_status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "Shield.models.request_status", "name": "request_status", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "shield_choice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "Shield.models.shield_choice", "name": "shield_choice", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\models.py"}