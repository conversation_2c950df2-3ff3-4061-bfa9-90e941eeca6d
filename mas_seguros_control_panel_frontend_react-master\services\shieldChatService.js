import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

/**
 * Service for handling shield chat related API calls
 */
class ShieldChatService {
  /**
   * Get shield details
   * @param {string} token - Authentication token
   * @param {string} shieldId - Shield ID
   * @returns {Promise} - Promise with shield data
   */
  static async getShieldDetails(token, shieldId) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/shield/get-single/${shieldId}`, {
        headers: {
          Authorization: `token ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching shield details:', error);
      throw error;
    }
  }

  /**
   * Get shield members
   * @param {string} token - Authentication token
   * @param {string} shieldId - Shield ID
   * @returns {Promise} - Promise with shield members data
   */
  static async getShieldMembers(token, shieldId) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/shield/shield-members/`, {
        headers: {
          Authorization: `token ${token}`,
        },
        params: {
          id: shieldId,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching shield members:', error);
      throw error;
    }
  }

  /**
   * Send a message to a shield chat
   * @param {string} token - Authentication token
   * @param {string} shieldId - Shield ID
   * @param {string} message - Message content
   * @returns {Promise} - Promise with sent message data
   */
  static async sendShieldMessage(token, shieldId, message) {
    try {
      const messageData = {
        shield_id: shieldId,
        message: message,
        is_admin: true
      };

      // For now, we'll just return a mock response since the backend API doesn't exist yet
      // In a real implementation, this would make an API call to the backend
      return {
        success: true,
        data: {
          id: new Date().getTime(),
          shield_id: shieldId,
          message: message,
          is_admin: true,
          sender_name: "Administrador",
          created_at: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error sending shield message:', error);
      throw error;
    }
  }
}

export default ShieldChatService;
