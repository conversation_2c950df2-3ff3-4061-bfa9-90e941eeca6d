from django.urls import path, reverse
# from Account.views_api import *
from . import views as company_views

urlpatterns = [
    path('all-companies/', company_views.AllCompanies.as_view()),
    path('company-shields/', company_views.CompanyShields.as_view()),
    path('company-members/', company_views.CompanyMembers.as_view()),
    path('company-promocodes/', company_views.CompanyPromoCodes.as_view()),
    path('membership-company/', company_views.membershipCompany.as_view()),
    path('company-single/<int:id>', company_views.company_get_single.as_view()),
    path('update-company/<int:pk>', company_views.update_company, name="update_company"),
]
