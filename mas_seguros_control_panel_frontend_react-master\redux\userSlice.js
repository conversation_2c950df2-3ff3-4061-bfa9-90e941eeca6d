import { createSlice } from "@reduxjs/toolkit";

export const userSlice = createSlice({
  name: "user",
  initialState: {},
  reducers: {
    setToken: (state, action) => {
      state.token = action.payload;
    },
    setLoggedIn: (state, action) => {
      state.logged_in = action.payload;
    },
    update: (state, action) => {
      state.first_name = action.payload.first_name;
      state.last_name = action.payload.last_name;
      state.email = action.payload.email;
      state.phone = action.payload.phone;
      state.image = action.payload.image;
      state.id = action.payload.id;
      state.role = action.payload.role;
      // Add additional fields from the API response
      state.full_name = action.payload.full_name;
      state.identification_card = action.payload.identification_card;
      state.birth_date = action.payload.birth_date;
      // Add admin permissions
      state.admin_permissions = action.payload.admin_permissions;
    },
  },
});

const { update, setLoggedIn, setToken } = userSlice.actions;
const userReducer = userSlice.reducer;

export { update, setLoggedIn, setToken, userReducer };
