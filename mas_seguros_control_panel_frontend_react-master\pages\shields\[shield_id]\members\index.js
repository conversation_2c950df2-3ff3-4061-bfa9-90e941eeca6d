import React from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import Table from "@/components/Table";
import LocationHistoryBtn from "@/components/shields/shield/LocationHistoryBtn";
import { useRouter } from "next/router";
import Pagination from "@/components/Pagination";
import useTableData from "@/hooks/useTableData";
import { format } from "date-fns";
import ProfilePicture from "@/components/ProfilePicture";

const pageSize = 10

export default function index() {
  const router = useRouter();
  const { shield_id } = router.query;

  const {
    currentTableData,
    isLoading,
    isError,
    error,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess
  } = useTableData({
    dataUrl: `adminside/api/shield/shield-members/?id=${shield_id}`,
    pageSize: pageSize,
    queryKeys: [`shield-${shield_id}-members-table-data`],
    enabled: !!shield_id,
    dataCallback: (response) => {
      // The API returns members data in response.data (not nested)
      // Handle both possible response structures for robustness
      if (response?.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      return [];
    },
  })


  return (
    <ShieldLayout pageTitle="Escudos" headerTitle="Escudos">
      <div className="mt-5">
        <Table
          dataCount={currentTableData.length}
          isLoading={isLoading}
          isError={isError}
          error={error}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Nombre</Table.Th>
              <Table.Th>Fecha de Creación</Table.Th>
              <Table.Th>Tipo</Table.Th>
              <Table.Th>Jerarquía</Table.Th>
              <Table.Th>Ubicaciones</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {isSuccess && currentTableData?.length > 0 ? (
              currentTableData.map((member) => (
                <Row
                  member={member}
                  key={member?.member?.user?.id || member?.user?.id || Math.random()}
                />
              ))
            ) : isSuccess && currentTableData?.length === 0 ? (
              <Table.Tr>
                <Table.Td colSpan={5} className="text-center py-4">
                  ¡No hay datos en el período de tiempo seleccionado!
                </Table.Td>
              </Table.Tr>
            ) : null}
          </Table.Tbody>
        </Table>
        {/* <SamplePagination /> */}
        {isSuccess && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </ShieldLayout>
  );
}

const Row = ({ member = {} }) => {
  // Extract the actual member data from the nested structure
  const memberData = member.member || member;

  return (
    <Table.Tr>
      <Table.Td>
        <div className="flex min-w-fit items-center gap-4">
          <ProfilePicture
            src={memberData.image_url ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${memberData.image_url}` : null}
            className="block aspect-square w-11 rounded-full object-cover"
            alt=""
          />
          <div>
            <p>{memberData.full_name || 'N/A'}</p>
            <p>ID-{memberData.user?.id || memberData.id || 'N/A'}</p>
          </div>
        </div>
      </Table.Td>
      <Table.Td>{memberData.created_at ? format(new Date(memberData.created_at), 'dd/MM/yy') : 'N/A'}</Table.Td>
      <Table.Td>{memberData.user_type || 'N/A'}</Table.Td>
      <Table.Td>
        <span className="capitalize inline-flex items-center rounded-full bg-warning bg-opacity-20 px-3 py-1.5 text-sm font-semibold text-warning">
          <svg
            className="mr-1.5 h-2 w-2 text-warning"
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx={5} cy={4} r={3} />
          </svg>
          {member.hierarchy || memberData.hierarchy || 'Miembro'}
        </span>
      </Table.Td>
      <Table.Td>
        <LocationHistoryBtn
          member={memberData}
          type="button"
          className="font-semibold text-primary hover:underline"
        >
          Historial de Ubicaciones
        </LocationHistoryBtn>
      </Table.Td>
    </Table.Tr>
  )
}