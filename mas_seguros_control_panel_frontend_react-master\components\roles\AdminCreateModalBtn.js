import useAxios from "@/hooks/useAxios";
import React, { createElement, useState } from "react";
import { toast } from "react-hot-toast";
import AdminFormModal from "../admin/AdminFormModal";

const AdminCreateModalBtn = ({ as = "button", className = "", onAdminCreated, ...props }) => {
  const [open, setOpen] = useState(false);
  const { axios } = useAxios();
  const [wasRecentlySuccessful, setwasRecentlySuccessful] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const close = () => {
    setOpen(false);
  };

  const submit = (data, selectedImage = null) => {
    setIsSubmitting(true);

    // Prepare FormData for file upload
    const formData = new FormData();

    // Add text fields
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('phone', data.phone);
    formData.append('email', data.email);
    formData.append('password', data.password);
    formData.append('identification_card', typeof data.identification_card === 'string' ? data.identification_card : (data.identification_card ? String(data.identification_card) : ''));

    // Add permission fields
    formData.append('users_access', data.users_access || false);
    formData.append('shields_access', data.shields_access || false);
    formData.append('alerts_sos_access', data.alerts_sos_access || false);
    formData.append('payment_history_access', data.payment_history_access || false);
    formData.append('support_access', data.support_access || false);
    formData.append('roles_access', data.roles_access || false);
    formData.append('full_access', data.full_access || false);

    // Add image if selected
    if (selectedImage) {
      formData.append('image', selectedImage);
    }

    axios
      .post("/adminside/api/roles/admin/create/", formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      .then((response) => {
        setOpen(false);
        toast.success(response.data.message || 'Administrador creado exitosamente');
        setwasRecentlySuccessful(true);

        // Call the callback to refresh the admin list
        if (onAdminCreated) {
          onAdminCreated();
        }
      })
      .catch((error) => {
        console.error('Error creating admin:', error);

        // Handle validation errors
        if (error?.response?.data?.errors) {
          const errors = error.response.data.errors;

          // Show specific field errors
          Object.keys(errors).forEach(field => {
            const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
            fieldErrors.forEach(errorMsg => {
              toast.error(`${field}: ${errorMsg}`);
            });
          });
        } else {
          // Show general error message
          const errorMessage = error?.response?.data?.message ||
                             error?.response?.data?.msg ||
                             'Error interno del servidor';
          toast.error(errorMessage);
        }
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <AdminFormModal
        submit={submit}
        open={open}
        close={close}
        mode="create"
        wasRecentlySuccessful={wasRecentlySuccessful}
        isSubmitting={isSubmitting}
      />
      {createElement(as, {
        type: "button",
        onClick: () => setOpen(true),
        className: className,
        ...props,
      })}
    </>
  );
};

export default AdminCreateModalBtn;
