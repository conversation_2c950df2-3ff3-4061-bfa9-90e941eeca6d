from django.db.models import Q
from django.shortcuts import render
from Membership import models as membership_models
from Membership import serializer as membership_seri
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from django.http import HttpResponse,JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework import permissions
from .pagination import customPagination
from rest_framework.generics import ListAPIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework import generics
# from .pagination import customPagination


# Create your views here.

# class membership_payment(APIView):
#     def get(self, request):
#         filter = request.query_params.get('filter', None)
#         if not filter:
#             member_obj = membership_models.MembershipModel.objects.all()
#             serializer = membership_seri.membershipSerializer(member_obj, many=True)
#             return Response(
#                 backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
#                                                msg='All members payment'))
#         else:
#             effected = None
#             failed = None
#             level_1 = None
#             level_2 = None
#             level_3 = None
#             level_4 = None

#             member_obj = membership_models.MembershipModel.objects.filter(
#                 Q(conditions=membership_models.effected) |
#                 Q(conditions=membership_models.failed) |
#                 Q(membership=membership_models.level_1) |
#                 Q(membership=membership_models.level_2) |
#                 Q(membership=membership_models.level_3) |
#                 Q(membership=membership_models.level_4)
#             )
#             serializer = membership_seri.membershipSerializer(member_obj, many=True)
#             return Response(
#                 backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
#                                                msg='All members payment'))
class membership_get(APIView):
    ...

    def get(self, request, id=None):
        if id:
            item = membership_models.MembershipModel.objects.get(id=id)
            serializer = membership_seri.membershipSerializer(item)
            return Response({"status": "success", "data": serializer.data}, status=status.HTTP_200_OK)


class membership_payment(generics.ListAPIView):
    #permission_classes = [IsAuthenticated,]
    queryset = membership_models.MembershipModel.objects.all()
    serializer_class = membership_seri.membershipSerializer
    # pagination_class= customPagination
    # filter_backends = (DjangoFilterBackend, OrderingFilter, SearchFilter)
    # filterset_fields = ["membership",'conditions']
    # search_fields = ('order_id','transaction_id','unitary_amount','membership','conditions','payment_address','ipaddress','total_amount')
    # ordering_fields = ("date",)

