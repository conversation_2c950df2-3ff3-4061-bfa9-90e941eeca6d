import Table from "@/components/Table";
import InputGroup from "@/components/utility/InputGroup";
import React, { useState } from "react";
import { useQuery } from "react-query";
import { format, isValid, parse } from "date-fns";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const LocationHistoryCard = ({ userId }) => {
  const { axios } = useAxios();
  const [selectedDate, setSelectedDate] = useState("");
  const [searchError, setSearchError] = useState("");
  const [filteredData, setFilteredData] = useState([]);

  const fetchLocationHistory = async () => {
    if (!userId) return [];
    try {
      // Use shield endpoint to get Location objects with real address data
      const response = await axios.get(`adminside/api/shield/shield-members-locations/`, {
        params: { member_id: userId }
      });
      // This endpoint returns Location objects directly with address information
      const locations = Array.isArray(response.data) ? response.data : [];

      // Also fetch route data for additional information
      const routeResponse = await axios.get(`adminside/api/dashboard/user-locations/`, {
        params: { user_id: userId }
      });
      const routes = routeResponse.data?.data || [];

      // Combine location and route data
      return locations.map((location, index) => {
        const correspondingRoute = routes.find(route =>
          route.created_at && location.created_at &&
          new Date(route.created_at).toDateString() === new Date(location.created_at).toDateString()
        ) || routes[index] || {};

        return {
          location: location.location || "Ubicación no disponible",
          created_at: location.created_at,
          lat_long: location.lat_long || "No disponible",
          route_date: location.created_at, // Use location timestamp as route date
          route_completed: correspondingRoute.route_completed || false,
          max_speed: correspondingRoute.max_speed || "No disponible"
        };
      });
    } catch (error) {
      console.error("Error fetching location history:", error);
      return [];
    }
  };

  const { data: locationData = [], isLoading, isError } = useQuery(
    [`user-${userId}-location-history`],
    fetchLocationHistory,
    {
      enabled: !!userId,
      refetchOnWindowFocus: false,
      onError: (error) => {
        console.error("Error loading location history:", error);
        toast.error("Error al cargar el historial de ubicaciones");
      }
    }
  );

  // Filter data based on selected date
  const handleSearch = () => {
    setSearchError("");

    if (!selectedDate) {
      setFilteredData(locationData);
      toast.success("Historial cargado correctamente");
      return;
    }

    // Validate date format
    const parsedDate = parse(selectedDate, 'yyyy-MM-dd', new Date());
    if (!isValid(parsedDate)) {
      setSearchError("Formato de fecha inválido");
      toast.error("Formato de fecha inválido");
      return;
    }

    const filtered = locationData.filter(route => {
      const dateToCheck = route.route_date || route.created_at;
      if (!dateToCheck) return false;
      const routeDate = format(new Date(dateToCheck), 'yyyy-MM-dd');
      return routeDate === selectedDate;
    });

    setFilteredData(filtered);

    if (filtered.length === 0) {
      setSearchError("No se encontraron registros para la fecha ingresada");
      toast.error("No se encontraron registros para la fecha ingresada");
    } else {
      toast.success("Historial cargado correctamente");
    }
  };

  // Use filtered data if available, otherwise use all data
  const displayData = filteredData.length > 0 || selectedDate ? filteredData : locationData;

  // Sort by most recent first
  const sortedData = [...displayData].sort((a, b) => {
    const dateA = a.route_date || a.created_at || 0;
    const dateB = b.route_date || b.created_at || 0;
    return new Date(dateB) - new Date(dateA);
  });

  return (
    <div className="flex h-[800px] flex-col bg-white p-5 lg:h-full">
      <h2 className="text-lg font-bold">Historial de ubicaciones</h2>

      <div className="flex items-center justify-end gap-2 text-sm">
        <span>Buscar</span>
        <div>
          <InputGroup>
            <InputGroup.Input
              type="date"
              className="bg-accent"
              value={selectedDate}
              onChange={(e) => {
                setSelectedDate(e.target.value);
                setSearchError("");
              }}
              placeholder="DD/MM/YYYY"
            />
          </InputGroup>
          {searchError && (
            <p className="text-xs text-red-500 mt-1">{searchError}</p>
          )}
        </div>
        <button
          onClick={handleSearch}
          className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-primary/90"
        >
          Buscar
        </button>
        {selectedDate && (
          <button
            onClick={() => {
              setSelectedDate("");
              setFilteredData([]);
              setSearchError("");
            }}
            className="self-stretch rounded bg-gray-500 px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-gray-600"
          >
            Limpiar
          </button>
        )}
      </div>

      <div className="relative flex-grow">
        <Table
          wrapperClassName="mt-5 px-2.5 bg-accent absolute inset-0"
          className="relative"
          isLoading={isLoading}
          isError={isError}
          dataCount={sortedData.length}
        >
          <Table.Thead className="sticky top-0 bg-accent">
            <Table.Tr>
              <Table.Th className="w-1/3">Ubicación</Table.Th>
              <Table.Th className="w-1/3">Horario</Table.Th>
              <Table.Th className="w-1/3 text-right">Velocidad</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {!isLoading && !isError && sortedData.length === 0 && (
              <Table.Tr>
                <Table.Td colSpan={3} className="!py-10 text-center text-secondary">
                  {selectedDate
                    ? "No se encontraron ubicaciones para la fecha seleccionada"
                    : "No hay historial de ubicaciones disponible para este usuario"
                  }
                </Table.Td>
              </Table.Tr>
            )}
            {!isLoading && !isError && sortedData.map((locationEntry, index) => (
              <Table.Tr key={index}>
                <Table.Td className="!py-5">
                  <div className="text-sm font-mono truncate" title={locationEntry.lat_long}>
                    {locationEntry.lat_long && locationEntry.lat_long !== "No disponible"
                      ? locationEntry.lat_long
                      : "No disponible"
                    }
                  </div>
                </Table.Td>
                <Table.Td className="!py-5">
                  <div className="text-sm whitespace-nowrap">
                    {locationEntry.created_at
                      ? `${format(new Date(locationEntry.created_at), 'dd/MM/yyyy')}, ${format(new Date(locationEntry.created_at), 'HH:mm')} Hrs.`
                      : "Horario no disponible"
                    }
                  </div>
                </Table.Td>
                <Table.Td className="!py-5 text-right">
                  <span className="font-medium text-primary">
                    {locationEntry.max_speed && !isNaN(locationEntry.max_speed) && locationEntry.max_speed > 0
                      ? `${Math.round(locationEntry.max_speed)}km/h`
                      : "No disponible"}
                  </span>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </div>
    </div>
  );
};

export default LocationHistoryCard;
