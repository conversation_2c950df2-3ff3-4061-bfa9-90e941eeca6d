import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

/**
 * Service for handling support ticket related API calls
 */
class SupportService {
  /**
   * Get all tickets
   * @param {string} token - Authentication token
   * @returns {Promise} - Promise with tickets data
   */
  static async getAllTickets(token) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/ticket/ticket/`, {
        headers: {
          Authorization: `token ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching tickets:', error);
      throw error;
    }
  }

  /**
   * Get tickets for a specific user
   * @param {string} token - Authentication token
   * @param {string} userId - User ID
   * @returns {Promise} - Promise with user tickets data
   */
  static async getUserTickets(token, userId) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/ticket/getusertickets/`, {
        headers: {
          Authorization: `token ${token}`,
        },
        params: {
          user_id: userId,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user tickets:', error);
      throw error;
    }
  }

  /**
   * Get ticket subjects (categories)
   * @param {string} token - Authentication token
   * @returns {Promise} - Promise with ticket subjects data
   */
  static async getTicketSubjects(token) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/ticket/alltickets/`, {
        headers: {
          Authorization: `token ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching ticket subjects:', error);
      throw error;
    }
  }

  /**
   * Get ticket by ID
   * @param {string} token - Authentication token
   * @param {string} ticketId - Ticket ID
   * @returns {Promise} - Promise with ticket data
   */
  static async getTicketById(token, ticketId) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/ticket/ticket_id/${ticketId}`, {
        headers: {
          Authorization: `token ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      throw error;
    }
  }

  /**
   * Create a new ticket
   * @param {string} token - Authentication token
   * @param {Object} ticketData - Ticket data
   * @returns {Promise} - Promise with created ticket data
   */
  static async createTicket(token, ticketData) {
    try {
      const response = await axios.post(`${API_URL}/adminside/api/ticket/ticket/`, ticketData, {
        headers: {
          Authorization: `token ${token}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating ticket:', error);
      throw error;
    }
  }

  /**
   * Mark a ticket as resolved
   * @param {string} token - Authentication token
   * @param {string} ticketId - Ticket ID
   * @returns {Promise} - Promise with updated ticket data
   */
  static async resolveTicket(token, ticketId) {
    try {
      const response = await axios.post(
        `${API_URL}/adminside/api/ticket/resolve_ticket/`,
        { id: ticketId },
        {
          headers: {
            Authorization: `token ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error resolving ticket:', error);
      throw error;
    }
  }

  /**
   * Get messages for a ticket
   * @param {string} token - Authentication token
   * @param {string} ticketId - Ticket ID
   * @returns {Promise} - Promise with ticket messages data
   */
  static async getTicketMessages(token, ticketId) {
    try {
      const response = await axios.get(`${API_URL}/adminside/api/ticket/ticket_messages/`, {
        headers: {
          Authorization: `token ${token}`,
        },
        params: {
          ticket_id: ticketId,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching ticket messages:', error);
      throw error;
    }
  }

  /**
   * Send a message for a ticket
   * @param {string} token - Authentication token
   * @param {string} ticketId - Ticket ID
   * @param {string} message - Message content
   * @param {string} senderId - Sender ID (optional)
   * @returns {Promise} - Promise with sent message data
   */
  static async sendTicketMessage(token, ticketId, message, senderId = null) {
    try {
      const messageData = {
        ticket_id: ticketId,
        message: message,
        is_admin: true
      };

      if (senderId) {
        messageData.sender_id = senderId;
      }

      const response = await axios.post(
        `${API_URL}/adminside/api/ticket/ticket_messages/`,
        messageData,
        {
          headers: {
            Authorization: `token ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error sending ticket message:', error);
      throw error;
    }
  }
}

export default SupportService;
