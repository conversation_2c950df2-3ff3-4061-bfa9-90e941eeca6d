from django.db import models
from django.contrib.auth.models import User
from tinymce import models as tinymce_models
from mas_seguros_backend import settings as backend_setting

Hierarchy_choice = (
    ("COLLABORATIVE", "collaborative"),
    ("SOLITAIRE", "solitaire"),
    ("GHOST", "ghost"),
)
type_choice = (
    ("STANDARD", "standard"),
)
normal_user = 'User'
web_admin = 'Admin'
role_choices = [
    (normal_user, normal_user),
    (web_admin, web_admin)
]
individual = 'Individual'
corporate = 'Corporate'
user_type_choices = [
    (individual, individual),
    (corporate, corporate)
]


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, blank=False, null=False)
    firebase_uid = models.CharField(max_length=100, null=True, blank=True)
    ui_id = models.CharField(max_length=100, null=True, blank=True)
    phone = models.Char<PERSON>ield(max_length=100, null=True, blank=True, unique=True)
    full_name = models.CharField(max_length=60, null=True, blank=True)
    identification_card = models.CharField(max_length=60, null=True, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    role = models.CharField(max_length=30, choices=role_choices, null=True, blank=True, default=normal_user)
    lat = models.CharField(max_length=40, null=True, blank=True)  # logitute
    long = models.CharField(max_length=40, null=True, blank=True)  # latitute
    verification_code = models.IntegerField(null=True, blank=True)
    email_verified = models.BooleanField(null=True, blank=True, default=False)
    enable_location = models.BooleanField(null=False, blank=False, default=False)
    image = models.ImageField(null=True, blank=True, upload_to='profile_images')
    user_type = models.CharField(max_length=100, choices=user_type_choices, null=True, default=individual)
    suspend = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=False)
    updated_at = models.DateTimeField(auto_now=True, null=False)

    # Hierarchy = models.CharField(max_length=100, choices=Hierarchy_choice, blank=True)

    def __str__(self):
        try:
            return "{}".format(self.user.get_full_name())
        except:
            return "{}".format(self.ui_id)

    def __str__(self):
        return self.full_name

    @property
    def name(self):
        return self.user.get_full_name()

    @property
    def email(self):
        return self.user.email

    @property
    def image_url(self):
        try:
            return backend_setting.Base_url_path.format(url=self.image.url)
        except:
            return None


# class Company(models.Model):
#     name = models.CharField(max_length=100, null=True, blank=True)
#     users = models.ManyToManyField(UserProfile)
#
#     created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
#     updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class Feature(models.Model):
    title = models.CharField(max_length=200, null=True, blank=False)
    is_included = models.BooleanField(null=False, blank=False, default=True)

    def __str__(self):
        return self.title


class Package(models.Model):
    MONTHLY = "monthly"
    YEARLY = "yearly"
    pricing_durations = [
        (MONTHLY, MONTHLY),
        (YEARLY, YEARLY)
    ]
    title = models.CharField(max_length=200, null=True, blank=False)
    price = models.FloatField(default=0.0, null=True, blank=False, help_text="")
    features = models.ManyToManyField(Feature, related_name="package_features")
    short_description = models.TextField(max_length=200, null=True, blank=False, help_text="You can add html as well")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


# class AdminProfileModel(models.Model):
#     userprofile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)


class FcmDeviceRegistration(models.Model):
    userprofile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    device_id = models.CharField(max_length=1000, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.device_id
