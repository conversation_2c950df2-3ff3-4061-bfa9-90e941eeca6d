import firebase_admin
from firebase_admin import credentials
from firebase_admin import messaging
import os
import requests
import json
from django.conf import settings

# import firebase_admin
# from firebase_admin import credentials
# from firebase_admin import messaging
# from pyfcm import FCMNotification
# import os
# from fcm_django.models import FCMDevice

# BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# JSON_PATH = os.path.join(BASE_DIR, 'Account/mas-seguros-cred.json')
# cred = credentials.Certificate(JSON_PATH)
# firebase_admin.initialize_app(cred)

# def send_fcm_notification(registration_id, message_title, message_body):
#     push_service = FCMNotification(api_key="AIzaSyAvWjgFutpCvylALfQ3iUUlBrRVF6CZChM")
#     registration_ids = [registration_id]
#     message = {
#         "title": message_title,
#         "body": message_body
#     }
#     result = push_service.notify_multiple_devices(registration_ids=registration_ids, message_body=message)
#     return result


# Initialize Firebase Admin SDK if not already initialized
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    JSON_PATH = os.path.join(BASE_DIR, 'Account/mas-seguros-cred.json')

    if not firebase_admin._apps:
        cred = credentials.Certificate(JSON_PATH)
        # Initialize Firebase Admin SDK (Firestore is enabled by default)
        firebase_admin.initialize_app(cred)
        print("Firebase Admin SDK initialized with Firestore")
except Exception as e:
    print(f"Error initializing Firebase Admin SDK: {e}")

def send_fcm_notification(registration_id, message_title, message_body):
    """Legacy function for compatibility"""
    return send_fcm_notification_for_generating_alert(registration_id, message_title, message_body)


import requests
import json


def send_fcm_notification_for_generating_alert(registration_id, message_title, message_body):
    url = "https://fcm.googleapis.com/fcm/send"

    payload = json.dumps({
        "notification": {
            "title": message_title,
            "body": message_body,
            "sound": "default"
        },
        "priority": "normal",
        "data": {
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
            "id": "1",
            "status": "done"
        },
        "registration_ids": [registration_id]
    })
    headers = {
        'Authorization': 'key=AAAADRF6UAQ:APA91bGwUphGLxAG5f-ctgP-yqVj3eBc0fwmlMjlUAv0GzUFzVipp6xkdvS-hTncr_RqVI3Kmq-L6BlDlBzIWhRWbvKmKDTJ-3YDFNROX4s934EuOSKv6Rolswr0-c6R5xT4fMe6jjlE',
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)


# +593990637176


def send_fcm_notification_for_generating_reviews_of_alert(registration_id, message_title, request):
    url = "https://fcm.googleapis.com/fcm/send"
    message_body = {
        'rating': request.data.get('rating'),
        'description': request.data.get('rating_description')
    }
    payload = json.dumps({
        "notification": {
            "title": message_title,
            "body": message_body,
            "sound": "default"
        },
        "priority": "normal",
        "data": {
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
            "id": "1",
            "status": "done"
        },
        "registration_ids": [registration_id]
    })
    headers = {
        'Authorization': 'key=AAAADRF6UAQ:APA91bGwUphGLxAG5f-ctgP-yqVj3eBc0fwmlMjlUAv0GzUFzVipp6xkdvS-hTncr_RqVI3Kmq-L6BlDlBzIWhRWbvKmKDTJ-3YDFNROX4s934EuOSKv6Rolswr0-c6R5xT4fMe6jjlE',
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)


def send_ticket_message_notification(registration_id, ticket_id, message_text, sender_name="Administrador"):
    """
    Send a notification for a new ticket message

    Args:
        registration_id (str): FCM device token
        ticket_id (int): ID of the ticket
        message_text (str): Content of the message
        sender_name (str): Name of the sender
    """
    url = "https://fcm.googleapis.com/fcm/send"

    # Truncate message if too long
    if len(message_text) > 100:
        message_preview = message_text[:97] + "..."
    else:
        message_preview = message_text

    payload = json.dumps({
        "notification": {
            "title": f"Nuevo mensaje de {sender_name}",
            "body": message_preview,
            "sound": "default"
        },
        "priority": "high",
        "data": {
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
            "id": str(ticket_id),
            "type": "ticket_message",
            "status": "new"
        },
        "registration_ids": [registration_id]
    })

    headers = {
        'Authorization': 'key=AAAADRF6UAQ:APA91bGwUphGLxAG5f-ctgP-yqVj3eBc0fwmlMjlUAv0GzUFzVipp6xkdvS-hTncr_RqVI3Kmq-L6BlDlBzIWhRWbvKmKDTJ-3YDFNROX4s934EuOSKv6Rolswr0-c6R5xT4fMe6jjlE',
        'Content-Type': 'application/json'
    }

    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        print(f"FCM Response for ticket message: {response.text}")
        return response
    except Exception as e:
        print(f"Error sending FCM notification for ticket message: {e}")
        return None