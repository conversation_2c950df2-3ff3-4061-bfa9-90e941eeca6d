{"info": {"_postman_id": "mas-seguros-backend-api", "name": "<PERSON><PERSON> Se<PERSON> Backend API", "description": "Complete API collection for Mas Seguros Backend - A comprehensive security and emergency response system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "mas-seguros-api"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"full_name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+**********\",\n    \"password\": \"SecurePassword123\",\n    \"identification_card\": \"********\",\n    \"birth_date\": \"15/01/1990\"\n}"}, "url": {"raw": "{{base_url}}/api/account/register/", "host": ["{{base_url}}"], "path": ["api", "account", "register", ""]}, "description": "Register a new user account"}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.environment.set('auth_token', response.data.token);", "        pm.environment.set('user_id', response.data.user_profile.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+**********\",\n    \"password\": \"SecurePassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/account/login/", "host": ["{{base_url}}"], "path": ["api", "account", "login", ""]}, "description": "Login user and get authentication token"}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/account/logout/", "host": ["{{base_url}}"], "path": ["api", "account", "logout", ""]}, "description": "Logout user and invalidate token"}, "response": []}, {"name": "Send Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/account/sendverificationcode/", "host": ["{{base_url}}"], "path": ["api", "account", "sendverificationcode", ""]}, "description": "Send verification code for password reset"}, "response": []}, {"name": "Verify Forget Password Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/account/forgetpasswordverifycode/", "host": ["{{base_url}}"], "path": ["api", "account", "forgetpasswordverifycode", ""]}, "description": "Verify the code sent for password reset"}, "response": []}, {"name": "Set New Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"new_password\": \"NewSecurePassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/account/setnewpassword/", "host": ["{{base_url}}"], "path": ["api", "account", "setnewpassword", ""]}, "description": "Set new password after verification"}, "response": []}], "description": "User authentication and account management endpoints"}, {"name": "Profile Management", "item": [{"name": "Edit Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "full_name", "value": "<PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "birth_date", "value": "15/01/1990", "type": "text"}, {"key": "identification_card", "value": "********", "type": "text"}, {"key": "image", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/account/editprofile/", "host": ["{{base_url}}"], "path": ["api", "account", "editprofile", ""]}, "description": "Update user profile information"}, "response": []}, {"name": "Change Phone", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/account/changephone/", "host": ["{{base_url}}"], "path": ["api", "account", "changephone", ""]}, "description": "Change user phone number"}, "response": []}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"new_password\": \"NewSecurePassword456\"\n}"}, "url": {"raw": "{{base_url}}/api/account/changepassword/", "host": ["{{base_url}}"], "path": ["api", "account", "changepassword", ""]}, "description": "Change user password"}, "response": []}, {"name": "Confirm Current Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"CurrentPassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/account/confirmcurrentpasschangepassword/", "host": ["{{base_url}}"], "path": ["api", "account", "confirmcurrentpasschangepassword", ""]}, "description": "Confirm current password before changing"}, "response": []}], "description": "User profile management endpoints"}, {"name": "Location Management", "item": [{"name": "Enable Location", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"enable_location\": true\n}"}, "url": {"raw": "{{base_url}}/api/account/enablelocation/", "host": ["{{base_url}}"], "path": ["api", "account", "enablelocation", ""]}, "description": "Enable or disable location tracking"}, "response": []}, {"name": "Update Real Time Location", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"lat\": \"40.7128\",\n    \"long\": \"-74.0060\"\n}"}, "url": {"raw": "{{base_url}}/api/account/realtimelocation/", "host": ["{{base_url}}"], "path": ["api", "account", "realtimelocation", ""]}, "description": "Update user's real-time location"}, "response": []}, {"name": "Get Real Time Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/account/realtimelocation/", "host": ["{{base_url}}"], "path": ["api", "account", "realtimelocation", ""]}, "description": "Get user's current location"}, "response": []}], "description": "Location tracking and management endpoints"}, {"name": "Alert Management", "item": [{"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Emergency Alert", "type": "text"}, {"key": "description", "value": "This is an emergency situation", "type": "text"}, {"key": "category", "value": "1", "type": "text"}, {"key": "lat", "value": "40.7128", "type": "text"}, {"key": "long", "value": "-74.0060", "type": "text"}, {"key": "evidence", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/alert/", "host": ["{{base_url}}"], "path": ["api", "alert", ""]}, "description": "Create a new alert"}, "response": []}, {"name": "Get All Alerts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/alert/getallalerts/", "host": ["{{base_url}}"], "path": ["api", "alert", "getallalerts", ""]}, "description": "Get all alerts for the user"}, "response": []}, {"name": "Get Alert Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/alert/alertcategories/", "host": ["{{base_url}}"], "path": ["api", "alert", "alertcategories", ""]}, "description": "Get all available alert categories"}, "response": []}, {"name": "Get Alert <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/alert/alertstatuses/", "host": ["{{base_url}}"], "path": ["api", "alert", "alertstatuses", ""]}, "description": "Get all alert statuses"}, "response": []}, {"name": "Get Unresolved Al<PERSON>s", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/alert/unresolvedalertr/", "host": ["{{base_url}}"], "path": ["api", "alert", "unresolvedalertr", ""]}, "description": "Get all unresolved alerts"}, "response": []}, {"name": "Get Resolved Al<PERSON>s", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/alert/resolvedalert/", "host": ["{{base_url}}"], "path": ["api", "alert", "<PERSON><PERSON><PERSON>", ""]}, "description": "Get all resolved alerts"}, "response": []}], "description": "Alert creation and management endpoints"}, {"name": "Shield Management", "item": [{"name": "Create Shield", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "shield_name", "value": "Family Shield", "type": "text"}, {"key": "description", "value": "Family protection shield", "type": "text"}, {"key": "shield_logo", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/shield/create-shield/", "host": ["{{base_url}}"], "path": ["api", "shield", "create-shield", ""]}, "description": "Create a new shield"}, "response": []}, {"name": "Get User Shields", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/shield/get-user-shields/", "host": ["{{base_url}}"], "path": ["api", "shield", "get-user-shields", ""]}, "description": "Get all shields for the current user"}, "response": []}, {"name": "Get Shield Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/shield/get-shield/?shield_id=1", "host": ["{{base_url}}"], "path": ["api", "shield", "get-shield", ""], "query": [{"key": "shield_id", "value": "1"}]}, "description": "Get details of a specific shield"}, "response": []}, {"name": "Join Shield Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"joining_code\": \"ABC123\"\n}"}, "url": {"raw": "{{base_url}}/api/shield/join-shield-request/", "host": ["{{base_url}}"], "path": ["api", "shield", "join-shield-request", ""]}, "description": "Request to join a shield using joining code"}, "response": []}, {"name": "Get Shield Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/shield/shield-members/?shield_id=1", "host": ["{{base_url}}"], "path": ["api", "shield", "shield-members", ""], "query": [{"key": "shield_id", "value": "1"}]}, "description": "Get all members of a shield"}, "response": []}, {"name": "Shield Member Locations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/shield/shield-members-locations/?shield_id=1", "host": ["{{base_url}}"], "path": ["api", "shield", "shield-members-locations", ""], "query": [{"key": "shield_id", "value": "1"}]}, "description": "Get real-time locations of shield members"}, "response": []}], "description": "Shield creation and management endpoints"}, {"name": "SOS Emergency", "item": [{"name": "Create SOS", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"lat\": \"40.7128\",\n    \"long\": \"-74.0060\",\n    \"description\": \"Emergency situation\",\n    \"emergency_type\": \"medical\"\n}"}, "url": {"raw": "{{base_url}}/api/sos/sos-create/", "host": ["{{base_url}}"], "path": ["api", "sos", "sos-create", ""]}, "description": "Create an emergency SOS alert"}, "response": []}, {"name": "Add SOS Evidence", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "sos_id", "value": "1", "type": "text"}, {"key": "evidence", "type": "file", "src": []}, {"key": "description", "value": "Evidence description", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/sos/sos-evidence/", "host": ["{{base_url}}"], "path": ["api", "sos", "sos-evidence", ""]}, "description": "Add evidence to an existing SOS"}, "response": []}, {"name": "Cancel SOS", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"sos_id\": 1,\n    \"reason\": \"False alarm\"\n}"}, "url": {"raw": "{{base_url}}/api/sos/sos-cancel/", "host": ["{{base_url}}"], "path": ["api", "sos", "sos-cancel", ""]}, "description": "Cancel an active SOS"}, "response": []}, {"name": "Get User SOS", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/sos/user-sos/", "host": ["{{base_url}}"], "path": ["api", "sos", "user-sos", ""]}, "description": "Get all SOS alerts for the current user"}, "response": []}], "description": "Emergency SOS system endpoints"}, {"name": "Membership & Payments", "item": [{"name": "Process Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"membership_type\": \"premium\",\n    \"payment_method\": \"credit_card\",\n    \"amount\": 29.99,\n    \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/api/Membership/payments/", "host": ["{{base_url}}"], "path": ["api", "Membership", "payments", ""]}, "description": "Process membership payment"}, "response": []}, {"name": "Get Membership Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/Membership/ship/1", "host": ["{{base_url}}"], "path": ["api", "Membership", "ship", "1"]}, "description": "Get membership details by ID"}, "response": []}], "description": "Membership and payment processing endpoints"}, {"name": "Support Tickets", "item": [{"name": "Create Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"subject\": \"Technical Issue\",\n    \"description\": \"I'm experiencing issues with the app\",\n    \"priority\": \"medium\",\n    \"category\": \"technical\"\n}"}, "url": {"raw": "{{base_url}}/api/ticket/ticket/", "host": ["{{base_url}}"], "path": ["api", "ticket", "ticket", ""]}, "description": "Create a new support ticket"}, "response": []}, {"name": "Get Ticket Subjects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/ticket/ticket-subjects/", "host": ["{{base_url}}"], "path": ["api", "ticket", "ticket-subjects", ""]}, "description": "Get all available ticket subjects/categories"}, "response": []}, {"name": "Update Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"resolved\",\n    \"response\": \"Issue has been resolved\"\n}"}, "url": {"raw": "{{base_url}}/api/ticket/update-ticket/1", "host": ["{{base_url}}"], "path": ["api", "ticket", "update-ticket", "1"]}, "description": "Update an existing ticket"}, "response": []}], "description": "Support ticket management endpoints"}, {"name": "FAQ", "item": [{"name": "Get FAQ Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/faq/faq-categories/", "host": ["{{base_url}}"], "path": ["api", "faq", "faq-categories", ""]}, "description": "Get all FAQ categories"}, "response": []}, {"name": "Get FAQ Questions by Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/faq/faq-category-questions/?category_id=1", "host": ["{{base_url}}"], "path": ["api", "faq", "faq-category-questions", ""], "query": [{"key": "category_id", "value": "1"}]}, "description": "Get FAQ questions for a specific category"}, "response": []}], "description": "Frequently Asked Questions endpoints"}, {"name": "About & Legal", "item": [{"name": "Get Data Policy", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/about/datapolicy/", "host": ["{{base_url}}"], "path": ["api", "about", "datapolicy", ""]}, "description": "Get data policy information"}, "response": []}, {"name": "Get Terms and Conditions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/about/termsandcondition/", "host": ["{{base_url}}"], "path": ["api", "about", "termsandcondition", ""]}, "description": "Get terms and conditions"}, "response": []}], "description": "Legal documents and about information endpoints"}, {"name": "Device & Notifications", "item": [{"name": "Register FCM Device Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"device_token\": \"fcm_device_token_here\",\n    \"device_type\": \"android\"\n}"}, "url": {"raw": "{{base_url}}/api/account/fcm-device-token/", "host": ["{{base_url}}"], "path": ["api", "account", "fcm-device-token", ""]}, "description": "Register FCM device token for push notifications"}, "response": []}, {"name": "Delete Account", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"confirmation\": \"DELETE\"\n}"}, "url": {"raw": "{{base_url}}/api/account/deleteaccount/", "host": ["{{base_url}}"], "path": ["api", "account", "deleteaccount", ""]}, "description": "Delete user account permanently"}, "response": []}, {"name": "Get All Accounts (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/account/accounts/", "host": ["{{base_url}}"], "path": ["api", "account", "accounts", ""]}, "description": "Get all user accounts (Admin only)"}, "response": []}], "description": "Device management and administrative endpoints"}, {"name": "Admin Dashboard", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/users/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "users", ""]}, "description": "Get all users in the system (Admin only)"}, "response": []}, {"name": "Suspend Users", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user_id\": 1,\n    \"suspend\": true,\n    \"reason\": \"Policy violation\"\n}"}, "url": {"raw": "{{base_url}}/api/dashboard/suspend_users/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "suspend_users", ""]}, "description": "Suspend or unsuspend users (Admin only)"}, "response": []}, {"name": "Get User Locations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/user-locations/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "user-locations", ""]}, "description": "Get all user locations (Admin only)"}, "response": []}, {"name": "Get User Shields", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/user-shields/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "user-shields", ""]}, "description": "Get all user shields (Admin only)"}, "response": []}, {"name": "Get Registered Users Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/registered-users/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "registered-users", ""]}, "description": "Get registered users statistics (Admin only)"}, "response": []}, {"name": "Get Membership Purchases by Month", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/purchased-memberships-according-months/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "purchased-memberships-according-months", ""]}, "description": "Get membership purchases statistics by month (Admin only)"}, "response": []}, {"name": "Get Shields Created by Month", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/created-shields-according-months/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "created-shields-according-months", ""]}, "description": "Get shields created statistics by month (Admin only)"}, "response": []}, {"name": "Get Alerts with SOS by Month", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/generated-alerts-with-sos-according-months/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "generated-alerts-with-sos-according-months", ""]}, "description": "Get alerts with SOS statistics by month (Admin only)"}, "response": []}, {"name": "Download Dashboard Excel Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/download-excel-file-for-dashboard/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "download-excel-file-for-dashboard", ""]}, "description": "Download dashboard data as Excel file (Admin only)"}, "response": []}, {"name": "Download Dashboard PDF Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/dashboard/download-pdf-file-for-dashboard/", "host": ["{{base_url}}"], "path": ["api", "dashboard", "download-pdf-file-for-dashboard", ""]}, "description": "Download dashboard data as PDF file (Admin only)"}, "response": []}], "description": "Administrative dashboard and analytics endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://************:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}]}