from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from . import models as membership_models
from Account import serializers as account_seri


class membershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = membership_models.MembershipModel
        fields = "__all__"
    def to_representation(self, instance):
        response = super().to_representation(instance)
        response['userprofile'] = account_seri.UserProfileSerializer(instance.userprofile).data
        
        return response