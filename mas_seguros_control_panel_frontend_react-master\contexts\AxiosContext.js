import React, { createContext, useContext } from 'react';
import axios from 'axios';

const AxiosContext = createContext();

export function AxiosProvider({ children }) {
  const axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add request interceptor to add auth token
  axiosInstance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return (
    <AxiosContext.Provider value={{ axios: axiosInstance }}>
      {children}
    </AxiosContext.Provider>
  );
}

export function useAxios() {
  const context = useContext(AxiosContext);
  if (context === undefined) {
    throw new Error('useAxios must be used within an AxiosProvider');
  }
  return context;
} 