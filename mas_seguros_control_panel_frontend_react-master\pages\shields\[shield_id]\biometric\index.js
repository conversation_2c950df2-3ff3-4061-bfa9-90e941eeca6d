import React, { useState, useEffect } from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import Table from "@/components/Table";
import InputGroup from "@/components/utility/InputGroup";
import ViewPhotoBtn from "@/components/ViewPhotoBtn";
import { useRouter } from "next/router";
import useTableData from "@/hooks/useTableData";
import Pagination from "@/components/Pagination";
import { format } from "date-fns";
import Badge from "@/components/Badge";
import { toast } from "react-hot-toast";

const pageSize = 10

export default function index() {
  const router = useRouter();
  const { shield_id } = router.query;
  const [selectedDate, setSelectedDate] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [hasSearched, setHasSearched] = useState(false);

  const {
    currentTableData,
    isLoading,
    isError,
    error,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess
  } = useTableData({
    dataUrl: `/adminside/api/shield/shield-biometrics/?id=${shield_id}`,
    pageSize: pageSize,
    queryKeys: [`shield-${shield_id}-biometrics-table-data`],
    enabled: !!shield_id,
    dataCallback: (resp) => {
      // Handle both formats: direct array or nested data property
      const data = resp?.data?.data || resp?.data || [];
      return Array.isArray(data) ? data : [];
    },
  });

  // Debug function to log data
  useEffect(() => {
    if (isSuccess && allData) {
      console.log('Biometric Page - All Data:', allData);
      console.log('Biometric Page - Current Table Data:', currentTableData);
    }
  }, [isSuccess, allData, currentTableData]);

  const handleDateSearch = () => {
    if (!selectedDate) {
      toast.error('Por favor seleccione una fecha');
      return;
    }

    setHasSearched(true);

    try {
      const filtered = allData.filter(bio => {
        if (!bio.created_at) return false;

        try {
          const bioDate = new Date(bio.created_at);
          if (isNaN(bioDate.getTime())) return false;

          const bioDateFormatted = format(bioDate, 'yyyy-MM-dd');
          return bioDateFormatted === selectedDate;
        } catch (error) {
          console.error("Error parsing date:", error);
          return false;
        }
      });

      setFilteredData(filtered);
      setCurrentPage(1);

      if (filtered.length > 0) {
        toast.success(`Se encontraron ${filtered.length} registros para la fecha seleccionada`);
      } else {
        toast.info('No hay datos para la fecha seleccionada');
      }
    } catch (error) {
      console.error("Error filtering data:", error);
      setFilteredData([]);
      toast.error('Error al filtrar los datos');
    }
  };

  // Clear search function
  const clearSearch = () => {
    setSelectedDate('');
    setFilteredData([]);
    setHasSearched(false);
    setCurrentPage(1);
  };

  // Calculate current page data based on search state
  const currentDisplayData = React.useMemo(() => {
    if (hasSearched) {
      // When searching, show all filtered results without pagination
      return filteredData;
    } else {
      // When not searching, use the hook's pagination
      return currentTableData;
    }
  }, [hasSearched, filteredData, currentTableData]);

  return (
    <ShieldLayout pageTitle="Escudos" headerTitle="Escudos">
      <div className="mt-5">
        <div className="flex items-center gap-2 text-sm">
          <span>Buscar</span>
          <div>
            <InputGroup>
              <InputGroup.Input
                type="date"
                className="!border-none bg-accent"
                value={selectedDate}
                onChange={(e) => {
                  const newDate = e.target.value;
                  setSelectedDate(newDate);
                  // Only clear search if date is cleared AND we had an active search
                  if (!newDate && hasSearched) {
                    clearSearch();
                  }
                }}
              />
            </InputGroup>
          </div>
          <button
            onClick={handleDateSearch}
            className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2"
          >
            Buscar
          </button>
          {hasSearched && (
            <button
              onClick={clearSearch}
              className="self-stretch rounded bg-gray-500 px-3 font-medium text-white ring-offset-2 focus:ring-2"
            >
              Limpiar
            </button>
          )}
        </div>

        <Table
          wrapperClassName="mt-6"
          dataCount={currentDisplayData.length}
          isLoading={isLoading}
          isError={isError}
          error={error}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID</Table.Th>
              <Table.Th>Nombre</Table.Th>
              <Table.Th>Horario</Table.Th>
              <Table.Th>Ubicación</Table.Th>
              <Table.Th>Tipo</Table.Th>
              <Table.Th>Archivo adjunto</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {!isLoading && !isError && currentDisplayData && currentDisplayData.length > 0 ? (
              currentDisplayData.map((bio) => (
                <Table.Tr key={bio.id}>
                  <Table.Td className="font-semibold">#{bio.biometric_code || bio.id}</Table.Td>
                  <Table.Td>
                    <div className="flex min-w-fit items-center gap-4">
                      <img
                        src={bio.userprofile?.image ? process.env.NEXT_PUBLIC_BACKEND_URL + bio.userprofile.image : "/assets/img/default-profile-pic-1.jpg"}
                        className="block aspect-square w-11 rounded-full object-cover"
                        alt=""
                      />
                      <div>
                        <p className="capitalize">{bio.userprofile?.full_name || 'Usuario'}</p>
                        <p>{bio.userprofile?.id || ''}</p>
                      </div>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <dd>{format(new Date(bio.created_at), 'p')}</dd>
                    <dd>{format(new Date(bio.created_at), 'dd/MM/yyyy')}</dd>
                  </Table.Td>
                  <Table.Td>
                    {/* <dd>Av. Navarrete 403</dd> */}
                    <dd className="font-semibold">{bio.lat || 'N/A'}, {bio.long || 'N/A'}</dd>
                  </Table.Td>
                  <Table.Td>
                    <span className={`inline-flex items-center rounded-full px-3 py-1.5 text-sm font-semibold ${
                      bio.type === 'ENTRADA'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      <svg
                        className={`mr-1.5 h-2 w-2 ${
                          bio.type === 'ENTRADA' ? 'text-green-800' : 'text-red-800'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 8 8"
                      >
                        <circle cx={4} cy={4} r={3} />
                      </svg>
                      {bio.type || 'SALIDA'}
                    </span>
                  </Table.Td>
                  <Table.Td>
                    <ViewPhotoBtn
                      headerTitle={`Biométrico #${bio.id}`}
                      user={{
                        id: bio.userprofile?.id || '',
                        name: bio.userprofile?.full_name || 'Usuario',
                        dp: bio.userprofile?.image_url || '',
                        avatar: bio.image_url || ''
                      }}
                      className="font-semibold text-primary hover:underline"
                    >
                      Ver foto
                    </ViewPhotoBtn>
                  </Table.Td>
                </Table.Tr>
              ))
            ) : (
              !isLoading && !isError && (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center py-4">
                    ¡No hay datos en el período de tiempo seleccionado!
                  </Table.Td>
                </Table.Tr>
              )
            )}
          </Table.Tbody>
        </Table>
        {/* <SamplePagination /> */}
        {isSuccess && !hasSearched && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </ShieldLayout>
  );
}
