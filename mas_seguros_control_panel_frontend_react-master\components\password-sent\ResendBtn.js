import React, { useState } from "react";
import ConfirmationModal from "../utility/ConfirmationModal";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const ResendBtn = ({ email }) => {
  const [open, setOpen] = useState(false);
  const [processing, setProcessing] = useState(false);
  const { axios } = useAxios();

  const close = () => {
    setOpen(false);
  };

  const resend = () => {
    if (!email) {
      toast.error("Falta la dirección de correo electrónico");
      return;
    }

    setProcessing(true);

    axios
      .post("/adminside/api/admin/admin-password-recovery/", { email })
      .then(() => {
        setOpen(true);
      })
      .catch((error) => {
        console.error("Resend password error:", error);

        // Handle different error response formats
        if (error?.response?.data?.message) {
          toast.error(error.response.data.message);
        } else if (error?.response?.data?.errors) {
          // Handle field-specific errors
          const fieldErrors = error.response.data.errors;
          Object.keys(fieldErrors).forEach(field => {
            if (Array.isArray(fieldErrors[field])) {
              toast.error(fieldErrors[field][0]);
            } else {
              toast.error(fieldErrors[field]);
            }
          });
        } else {
          // Fallback error message
          toast.error("No se pudo reenviar la nueva contraseña");
        }
      })
      .finally(() => {
        setProcessing(false);
      });
  };

  return (
    <>
      <ConfirmationModal
        type="success"
        mode="alert"
        open={open}
        close={close}
        caption="Contraseña reenviada"
        closeBtn={{
          show: false,
        }}
      />

      <button
        onClick={resend}
        disabled={processing}
        type="button"
        className="mx-auto inline-block font-semibold underline hover:text-primary disabled:opacity-50"
      >
        {processing ? "Enviando..." : "Reenviar contraseña"}
      </button>
    </>
  );
};

export default ResendBtn;
