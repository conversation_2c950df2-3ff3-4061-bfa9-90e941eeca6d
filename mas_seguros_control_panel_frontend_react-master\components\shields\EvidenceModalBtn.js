import Modal from "@/components/utility/Modal";
import classNames from "classnames";
import React, { createElement, useState } from "react";
import GoogleMap from "@/components/maps/GoogleMap";
import { useAxios } from "@/contexts/AxiosContext";

const EvidenceModalBtn = ({ as = "button", alert: alertData = {}, className = "", ...props }) => {
  const [open, setOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [evidenceError, setEvidenceError] = useState(false);
  const { axios } = useAxios();

  const close = () => {
    setOpen(false);
    setEvidenceError(false); // Reset error state when modal closes
  };

  // Validate evidence URL
  const hasValidEvidence = () => {
    const evidenceUrl = alertData.evidence_url || alertData.evidence;
    return evidenceUrl && evidenceUrl.trim() !== '' && evidenceUrl !== 'null' && evidenceUrl !== 'undefined';
  };

  const handleDownload = async () => {
    const evidenceUrl = alertData.evidence_url || alertData.evidence;
    if (!evidenceUrl) {
      alert('No hay evidencia disponible para descargar.');
      return;
    }

    setIsDownloading(true);
    try {
      console.log('Attempting to download evidence from:', evidenceUrl);

      // Use authenticated fetch to download the file
      const response = await axios.get(evidenceUrl, {
        responseType: 'blob',
        headers: {
          'Accept': '*/*',
        },
        timeout: 30000, // 30 second timeout
      });

      // Check if response is valid
      if (!response.data || response.data.size === 0) {
        throw new Error('Empty file received');
      }

      // Create blob URL and download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);

      // Get file extension from URL - improved logic
      let fileExtension = 'file';
      try {
        const urlParts = evidenceUrl.split('.');
        if (urlParts.length > 1) {
          fileExtension = urlParts.pop().split('?')[0]; // Remove query parameters
        }
      } catch (e) {
        console.warn('Could not determine file extension:', e);
      }

      const fileName = `evidencia_${alertData.evidence_number || alertData.num || alertData.id || 'unknown'}.${fileExtension}`;

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('Evidence downloaded successfully:', fileName);

    } catch (error) {
      console.error('Error downloading evidence:', error);

      // Show specific error message based on error type
      let errorMessage = 'El archivo no pudo ser descargado, intente más tarde.';

      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        errorMessage = 'La descarga tardó demasiado tiempo. Intente más tarde.';
      } else if (error.response?.status === 404) {
        errorMessage = 'El archivo de evidencia no fue encontrado.';
      } else if (error.response?.status === 403) {
        errorMessage = 'No tiene permisos para descargar este archivo.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Error del servidor. Intente más tarde.';
      } else if (error.message === 'Empty file received') {
        errorMessage = 'El archivo está vacío o corrupto.';
      } else if (error.message.includes('Network Error')) {
        errorMessage = 'Error de conexión. Verifique su conexión a internet.';
      }

      alert(errorMessage);
    } finally {
      setIsDownloading(false);
    }
  };

  const renderEvidence = () => {
    const evidenceUrl = alertData.evidence_url || alertData.evidence;

    if (!evidenceUrl) {
      return (
        <div className="block aspect-[9/16] w-1/3 bg-red-50 border-2 border-red-200 flex flex-col items-center justify-center text-red-600 p-4">
          <div className="text-center">
            <div className="text-lg font-semibold mb-2">⚠️</div>
            <div className="text-sm font-medium">No hay evidencia disponible</div>
            <div className="text-xs mt-1 text-red-500">Este registro no contiene archivos multimedia</div>
          </div>
        </div>
      );
    }

    // Show error message if evidence failed to load
    if (evidenceError) {
      return (
        <div className="block aspect-[9/16] w-1/3 bg-neutral flex items-center justify-center text-red-500 text-center p-4">
          La evidencia no pudo ser cargada.
        </div>
      );
    }

    // Enhanced video detection
    const isVideo = evidenceUrl.toLowerCase().includes('.mp4') ||
                   evidenceUrl.toLowerCase().includes('video') ||
                   evidenceUrl.toLowerCase().includes('.mov') ||
                   evidenceUrl.toLowerCase().includes('.avi') ||
                   evidenceUrl.toLowerCase().includes('.webm') ||
                   evidenceUrl.toLowerCase().includes('.mkv') ||
                   evidenceUrl.toLowerCase().includes('.m4v') ||
                   evidenceUrl.toLowerCase().includes('.3gp');

    if (isVideo) {
      return (
        <video
          className="block aspect-[9/16] w-1/3 object-cover"
          controls
          src={evidenceUrl}
          onError={() => {
            console.error('Error loading video evidence:', evidenceUrl);
            setEvidenceError(true);
          }}
          onLoadStart={() => {
            console.log('Loading video evidence:', evidenceUrl);
          }}
        >
          Tu navegador no soporta el elemento de video.
        </video>
      );
    }

    return (
      <img
        src={evidenceUrl}
        className="block aspect-[9/16] w-1/3 object-cover"
        alt="evidence"
        onError={() => {
          console.error('Error loading image evidence:', evidenceUrl);
          setEvidenceError(true);
        }}
        onLoad={() => {
          console.log('Successfully loaded image evidence:', evidenceUrl);
        }}
      />
    );
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-4xl overflow-hidden bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">{alertData.evidence_number || alertData.num || 'N/A'}</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body>
            <div className="flex gap-5">
              {renderEvidence()}
              <div className="flex-1 space-y-5">
                <div className="aspect-video bg-neutral">
                  <GoogleMap
                    lat={alertData.lat}
                    lng={alertData.long}
                    zoom={15}
                    className="w-full h-full"
                    showMarker={true}
                  />
                </div>
                {/* Show description field for alerts as per user story */}
                {(alertData.type !== 'SOS' || alertData.description) && (
                  <div className="bg-neutral px-5 pt-4 pb-6">
                    <div className="text-sm font-semibold mb-2">
                      {alertData.type === 'SOS' ? 'Comentarios' : 'Descripción'}
                    </div>
                    <p>{alertData.description || "No hay descripción disponible"}</p>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <dd className="font-semibold">{alertData.type === 'SOS' ? 'SOS' : (alertData.category?.name || 'Alerta')}</dd>
                    <dd>{alertData.type === 'SOS' ? 'SOS' : (alertData.category?.name || 'Alerta')}#{alertData.evidence_number || alertData.num || alertData.id || 'N/A'}</dd>
                  </div>
                  <div>
                    <dd className="font-semibold">Miembro</dd>
                    <dd>{alertData.userprofile?.full_name || alertData.sender?.full_name || 'Usuario'}</dd>
                  </div>
                  <div>
                    <dd className="font-semibold">Ubicación</dd>
                    <dd>{(alertData.lat && alertData.long) ? `${alertData.lat}, ${alertData.long}` : 'No disponible'}</dd>
                  </div>
                  <div>
                    <dd className="font-semibold">Teléfono</dd>
                    <dd>{(() => {
                      const phone = alertData.userprofile?.phone ||
                                   alertData.sender?.phone ||
                                   alertData.userprofile?.user?.phone ||
                                   alertData.sender?.user?.phone;
                      return phone || 'No disponible';
                    })()}</dd>
                  </div>
                  <div>
                    <dd className="font-semibold">Fecha</dd>
                    <dd>{alertData.alert_date || 'N/A'}</dd>
                  </div>
                  <div>
                    <dd className="font-semibold">Hora</dd>
                    <dd>{alertData.alert_time || 'N/A'}</dd>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-white">
              Cancelar
            </Modal.FooterBtn>
            {hasValidEvidence() && (
              <Modal.FooterBtn
                onClick={handleDownload}
                className="bg-black text-white"
                disabled={isDownloading}
              >
                {isDownloading ? "Descargando..." : "Descargar Evidencia"}
              </Modal.FooterBtn>
            )}
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        type: "button",
        onClick: () => {
          // Always open the modal - show error message inside modal as per CA0104
          setOpen(true);
        },
        className: classNames(className, "font-semibold hover:underline"),
        ...props,
      })}
    </>
  );
};

export default EvidenceModalBtn;
