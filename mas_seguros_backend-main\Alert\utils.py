import random

from mas_seguros_backend import prefixes as backend_prefixes
from . import models as alert_models


def generate_prefix_number(userprofile):
    new_alert_num = backend_prefixes.get_new_prefix_number(userprofile=userprofile,
                                                           _model=alert_models.AlertModel,
                                                           field_name='num',
                                                           prefix=backend_prefixes.ALERT)
    return new_alert_num


def check_if_unsolved_alert_exist(user):
    """
    Check if user can create a new alert.
    Returns True if user CAN create a new alert (no active unresolved alerts).
    Returns False if user CANNOT create a new alert (has active unresolved alerts).
    """
    # Get status objects for unresolved alerts
    alert_sent_status = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SENT).first()
    help_sent_status = alert_models.AlertStatus.objects.filter(name=alert_models.HELP_SENT).first()
    
    # Check if user has any active unresolved alerts
    unresolved_statuses = [status for status in [alert_sent_status, help_sent_status] if status]
    
    if unresolved_statuses:
        unresolved_alerts = alert_models.AlertModel.objects.filter(
            userprofile=user.userprofile,
            status__in=unresolved_statuses
        )
        
        # If there are any unresolved alerts, user cannot create new one
        if unresolved_alerts.exists():
            return False
    
    # User can create new alert if no unresolved alerts exist
    return True


def generate_unique_evidence_number(data):
    num = 'Evidencia # {}'.format(str(random.randint(100000, 999999)))
    return num
