{".class": "MypyFile", "_fullname": "AdminSide.Shield.serializers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlertCategorySerialzier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier", "name": "AlertCategorySerialzier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.AlertCategorySerialzier", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.AlertCategorySerialzier.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Alert.models.AlertCategories", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.AlertCategorySerialzier.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.AlertCategorySerialzier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.AlertCategorySerialzier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetMemberIDSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer", "name": "GetMemberIDSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.GetMemberIDSerializer", "builtins.object"], "names": {".class": "SymbolTable", "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer.month", "name": "month", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer.year", "name": "year", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.GetMemberIDSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.GetMemberIDSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetMonthlySerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.GetMonthlySerializer", "name": "GetMonthlySerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.GetMonthlySerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.GetMonthlySerializer", "builtins.object"], "names": {".class": "SymbolTable", "month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetMonthlySerializer.month", "name": "month", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.GetMonthlySerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.GetMonthlySerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldIdAndMemberIDSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer", "name": "GetShieldIdAndMemberIDSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer", "builtins.object"], "names": {".class": "SymbolTable", "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.GetShieldIdAndMemberIDSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetShieldIdSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.GetShieldIdSerializer", "name": "GetShieldIdSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.GetShieldIdSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.GetShieldIdSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.GetShieldIdSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.GetShieldIdSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.GetShieldIdSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HierarchySerializerShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield", "name": "HierarchySerializerShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.HierarchySerializerShield", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.HierarchySerializerShield.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Hierarchie", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.HierarchySerializerShield.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.admin", "name": "admin", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.get_admin", "name": "get_admin", "type": null}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.member", "name": "member", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.HierarchySerializerShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.HierarchySerializerShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointOfInterestSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer", "name": "PointOfInterestSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.PointOfInterestSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.PointOfInterestSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.PointsOfInterest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.PointOfInterestSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.admin", "name": "admin", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.PointOfInterestSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointOfInterestSerializerInShield": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield", "name": "PointOfInterestSerializerInShield", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.PointOfInterestSerializerInShield", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.PointsOfInterest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldAlertSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer", "name": "ShieldAlertSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldAlertSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldAlertSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Alert.models.AlertModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldAlertSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.category", "name": "category", "type": "AdminSide.Shield.serializers.AlertCategorySerialzier"}}, "userprofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.userprofile", "name": "userprofile", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldAlertSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldAlertSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldBiometricsSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer", "name": "ShieldBiometricsSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldBiometricsSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Biometric", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "get_image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.get_image_url", "name": "get_image_url", "type": null}}, "image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.image_url", "name": "image_url", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "userprofile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.userprofile", "name": "userprofile", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldBiometricsSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldBiometricsSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocationHistorySerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer", "name": "ShieldMemberLocationHistorySerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer", "builtins.object"], "names": {".class": "SymbolTable", "date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer.date", "name": "date", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer.validate", "name": "validate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberLocationHistorySerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberLocationSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer", "name": "ShieldMemberLocationSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberLocationSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Location", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberLocationSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberRouteSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer", "name": "ShieldMemberRouteSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberRouteSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.Route", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ending_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.ending_poi", "name": "ending_poi", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "get_ending_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.get_ending_poi", "name": "get_ending_poi", "type": null}}, "get_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.get_location", "name": "get_location", "type": null}}, "get_route_ending_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.get_route_ending_time", "name": "get_route_ending_time", "type": null}}, "get_route_starting_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.get_route_starting_time", "name": "get_route_starting_time", "type": null}}, "get_starting_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.get_starting_poi", "name": "get_starting_poi", "type": null}}, "location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.location", "name": "location", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.member", "name": "member", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}, "route_ending_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.route_ending_time", "name": "route_ending_time", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "route_starting_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.route_starting_time", "name": "route_starting_time", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "starting_poi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.starting_poi", "name": "starting_poi", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberRouteSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldMemberRoutesRequestSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer", "name": "ShieldMemberRoutesRequestSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer", "builtins.object"], "names": {".class": "SymbolTable", "date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.date", "name": "date", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.month", "name": "month", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "shield_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.shield_id", "name": "shield_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.validate", "name": "validate", "type": null}}, "year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.year", "name": "year", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldMemberRoutesRequestSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShieldsSerializerForDashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard", "name": "ShieldsSerializerForDashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldsSerializerForDashboard", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Shield.models.ShieldModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.admin", "name": "admin", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}, "get_logo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.get_logo_url", "name": "get_logo_url", "type": null}}, "get_participent_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.get_participent_count", "name": "get_participent_count", "type": null}}, "get_walkie_talkie_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.get_walkie_talkie_status", "name": "get_walkie_talkie_status", "type": null}}, "locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.locations", "name": "locations", "type": "AdminSide.Shield.serializers.PointOfInterestSerializerInShield"}}, "logo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.logo_url", "name": "logo_url", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "members": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.members", "name": "members", "type": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard"}}, "participent_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.participent_count", "name": "participent_count", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "walkie_talkie_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.walkie_talkie_status", "name": "walkie_talkie_status", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.ShieldsSerializerForDashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SuspendSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.SuspendSerializer", "name": "SuspendSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.SuspendSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.SuspendSerializer", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.SuspendSerializer.id", "name": "id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "suspended": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.SuspendSerializer.suspended", "name": "suspended", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.SuspendSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.SuspendSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "User": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "AdminSide.Shield.serializers.User", "name": "User", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.User", "source_any": null, "type_of_any": 3}}}, "UserProfileSerializerForDashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard", "name": "UserProfileSerializerForDashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.UserProfileSerializerForDashboard", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta.fields", "name": "fields", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Account.models.UserProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "get_image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.get_image_url", "name": "get_image_url", "type": null}}, "image_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.image_url", "name": "image_url", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.user", "name": "user", "type": "AdminSide.Shield.serializers.UserSerializer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.UserProfileSerializerForDashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.UserSerializer", "name": "UserSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.UserSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.UserSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.UserSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.UserSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.UserSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserSerializer.Meta.fields", "name": "fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserSerializer.Meta.model", "name": "model", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.User", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.UserSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.UserSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.UserSerializer.password", "name": "password", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "validate_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.UserSerializer.validate_email", "name": "validate_email", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.UserSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.UserSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "AdminSide.Shield.serializers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "account_models": {".class": "SymbolTableNode", "cross_ref": "Account.models", "kind": "Gdef"}, "alert_models": {".class": "SymbolTableNode", "cross_ref": "Alert.models", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "getMemberIdSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer", "name": "getMemberIdSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.getMemberIdSerializer", "builtins.object"], "names": {".class": "SymbolTable", "date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer.date", "name": "date", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "member_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer.member_id", "name": "member_id", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "report_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer.report_type", "name": "report_type", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.getMemberIdSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.getMemberIdSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "membership_models": {".class": "SymbolTableNode", "cross_ref": "Membership.models", "kind": "Gdef"}, "serializers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "AdminSide.Shield.serializers.serializers", "name": "serializers", "type": {".class": "AnyType", "missing_import_name": "AdminSide.Shield.serializers.serializers", "source_any": null, "type_of_any": 3}}}, "sheildMembershipSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer", "name": "sheildMembershipSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.sheildMembershipSerializer", "builtins.object"], "names": {".class": "SymbolTable", "Meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta", "name": "Meta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "AdminSide.Shield.serializers", "mro": ["AdminSide.Shield.serializers.sheildMembershipSerializer.Meta", "builtins.object"], "names": {".class": "SymbolTable", "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta.fields", "name": "fields", "type": "builtins.str"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "Membership.models.MembershipModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.sheildMembershipSerializer.Meta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "AdminSide.Shield.serializers.sheildMembershipSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "AdminSide.Shield.serializers.sheildMembershipSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "shield_models": {".class": "SymbolTableNode", "cross_ref": "Shield.models", "kind": "Gdef"}, "ticket_models": {".class": "SymbolTableNode", "cross_ref": "Ticket.models", "kind": "Gdef"}}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\AdminSide\\Shield\\serializers.py"}