#!/usr/bin/env python
"""
Complete setup script for MAS Seguros admin panel.
This single file will:
1. Make and apply all necessary migrations
2. Create authentication tokens for users
3. Insert sample data for testing the admin panel
"""

import os
import sys
import random
import datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mas_seguros_backend.settings')

import django
django.setup()

from django.core.management import call_command
from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

# Import models after Django setup
from Account.models import UserProfile, Feature, Package
from Shield.models import ShieldModel, PointsOfInterest, Route, ShieldJoinRequest
from Alert.models import AlertModel, AlertCategories, AlertStatus
from Membership.models import MembershipModel
from Ticket.models import Ticket, TicketSubject
from AdminSide.AdminAPi.models import PromoCode
from AdminSide.CompanyDashboard.models import CompanyProfileModel, SuperAdmin

print("=" * 80)
print("MAS SEGUROS ADMIN PANEL COMPLETE SETUP")
print("=" * 80)

# STEP 1: Make and apply migrations
print("\nSTEP 1: MAKING AND APPLYING MIGRATIONS")
print("-" * 50)

print("Making migrations for all apps...")
call_command('makemigrations')

print("\nRunning migrations for all apps...")
call_command('migrate')

print("\nRunning migrations specifically for authtoken...")
call_command('migrate', 'authtoken')

print("\n✅ Migrations completed successfully!")

# STEP 2: Insert sample data
print("\nSTEP 2: INSERTING SAMPLE DATA")
print("-" * 50)

# Create alert categories
alert_categories = [
    {"name": "Health", "image": None},
    {"name": "Police", "image": None},
    {"name": "Traffic", "image": None},
    {"name": "Firefighter", "image": None},
    {"name": "24/7 Assistance", "image": None},
]

for category in alert_categories:
    AlertCategories.objects.get_or_create(name=category["name"], defaults={"image": category["image"]})

print("✅ Created alert categories")

# Create alert statuses
alert_statuses = [
    {"name": "Alerta enviada"},
    {"name": "Ayuda enviada"},
    {"name": "Alerta resuelta"},
]

for status in alert_statuses:
    AlertStatus.objects.get_or_create(name=status["name"])

print("✅ Created alert statuses")

# Create ticket subjects
ticket_subjects = [
    {"title": "Technical Issue", "description": "Issues related to technical problems"},
    {"title": "Billing Question", "description": "Questions about billing and payments"},
    {"title": "Feature Request", "description": "Requests for new features"},
    {"title": "Account Problem", "description": "Issues with user accounts"},
    {"title": "General Inquiry", "description": "General questions about the service"},
]

for subject in ticket_subjects:
    TicketSubject.objects.get_or_create(title=subject["title"], defaults={"description": subject["description"]})

print("✅ Created ticket subjects")

# Create membership features
features = [
    {"title": "Basic Support"},
    {"title": "Priority Support"},
    {"title": "24/7 Support"},
    {"title": "Multiple Shields"},
    {"title": "Unlimited Shields"},
    {"title": "Advanced Analytics"},
    {"title": "Custom Branding"},
]

for feature in features:
    Feature.objects.get_or_create(title=feature["title"])

print("✅ Created membership features")

# Create membership packages
packages = [
    {
        "title": "Basic",
        "price": 9.99,
        "short_description": "Basic protection for individuals",
        "features": ["Basic Support", "Multiple Shields"]
    },
    {
        "title": "Premium",
        "price": 19.99,
        "short_description": "Enhanced protection for small teams",
        "features": ["Priority Support", "Multiple Shields", "Advanced Analytics"]
    },
    {
        "title": "Enterprise",
        "price": 49.99,
        "short_description": "Complete protection for organizations",
        "features": ["24/7 Support", "Unlimited Shields", "Advanced Analytics", "Custom Branding"]
    },
]

for package in packages:
    pkg, created = Package.objects.get_or_create(
        title=package["title"],
        defaults={
            "price": package["price"],
            "short_description": package["short_description"],
        }
    )
    
    if created:
        for feature_title in package["features"]:
            try:
                feature = Feature.objects.get(title=feature_title)
                pkg.features.add(feature)
            except Feature.DoesNotExist:
                print(f"Feature '{feature_title}' not found")

print("✅ Created membership packages")

# Create regular users
regular_users = [
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "User@123",
        "first_name": "Juan",
        "last_name": "Pérez",
        "full_name": "Juan Pérez",
        "phone": "+1234567890",
        "identification_card": "ID12345678",
        "birth_date": "1985-05-15",
        "user_type": "Individual"
    },
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "User@123",
        "first_name": "María",
        "last_name": "González",
        "full_name": "María González",
        "phone": "+1234567891",
        "identification_card": "ID12345679",
        "birth_date": "1990-08-20",
        "user_type": "Individual"
    },
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "User@123",
        "first_name": "Carlos",
        "last_name": "Rodríguez",
        "full_name": "Carlos Rodríguez",
        "phone": "+1234567892",
        "identification_card": "ID12345680",
        "birth_date": "1988-03-10",
        "user_type": "Corporate"
    },
]

user_profiles = []
for user_data in regular_users:
    user, created = User.objects.get_or_create(
        username=user_data["username"],
        defaults={
            "email": user_data["email"],
            "first_name": user_data["first_name"],
            "last_name": user_data["last_name"],
            "is_active": True
        }
    )
    
    if created:
        user.set_password(user_data["password"])
        user.save()
    
    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={
            "full_name": user_data["full_name"],
            "phone": user_data["phone"],
            "identification_card": user_data["identification_card"],
            "birth_date": user_data["birth_date"],
            "role": "User",
            "user_type": user_data["user_type"],
            "email_verified": True
        }
    )
    user_profiles.append(profile)

print(f"✅ Created/updated regular users")

# Create admin users
admin_users = [
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "Admin@123",
        "first_name": "Admin",
        "last_name": "User",
        "full_name": "Admin User",
        "phone": "+9876543210",
        "identification_card": "ADMIN12345",
        "birth_date": "1980-01-01",
        "user_type": "Corporate",
        "role": "Admin"
    },
    {
        "username": "<EMAIL>",
        "email": "<EMAIL>",
        "password": "Admin@123",
        "first_name": "Super",
        "last_name": "Admin",
        "full_name": "Super Admin",
        "phone": "+9876543211",
        "identification_card": "ADMIN12346",
        "birth_date": "1982-02-02",
        "user_type": "Corporate",
        "role": "Admin"
    },
]

admin_profiles = []
for admin_data in admin_users:
    user, created = User.objects.get_or_create(
        username=admin_data["username"],
        defaults={
            "email": admin_data["email"],
            "first_name": admin_data["first_name"],
            "last_name": admin_data["last_name"],
            "is_active": True,
            "is_staff": True
        }
    )
    
    if created:
        user.set_password(admin_data["password"])
        user.save()
    
    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={
            "full_name": admin_data["full_name"],
            "phone": admin_data["phone"],
            "identification_card": admin_data["identification_card"],
            "birth_date": admin_data["birth_date"],
            "role": admin_data["role"],
            "user_type": admin_data["user_type"],
            "email_verified": True
        }
    )
    admin_profiles.append(profile)
    
    # Create SuperAdmin entry
    SuperAdmin.objects.get_or_create(userprofile=profile)

print(f"✅ Created/updated admin users")

# Create companies
companies = [
    {
        "name": "Seguridad Total S.A.",
        "admin": admin_profiles[0] if admin_profiles else None,
    },
    {
        "name": "Protección Integral Ltda.",
        "admin": admin_profiles[1] if len(admin_profiles) > 1 else None,
    },
]

company_objects = []
for company_data in companies:
    company, created = CompanyProfileModel.objects.get_or_create(
        name=company_data["name"]
    )
    
    if company_data["admin"]:
        company.admin.add(company_data["admin"])
        
    company_objects.append(company)

print(f"✅ Created/updated companies")

# Create promo codes
promo_codes = [
    {
        "label": "Summer Discount",
        "promo_code": "SUMMER2023",
        "company": company_objects[0] if company_objects else None,
        "start_duration": datetime.date.today(),
        "end_duration": datetime.date.today() + datetime.timedelta(days=90),
        "stocks": 100,
        "discount": 20,
        "Etiquette": "Seasonal",
        "state": True,
        "membership": "level 1"
    },
    {
        "label": "New User Discount",
        "promo_code": "NEWUSER",
        "company": company_objects[1] if len(company_objects) > 1 else None,
        "start_duration": datetime.date.today(),
        "end_duration": datetime.date.today() + datetime.timedelta(days=30),
        "stocks": 50,
        "discount": 15,
        "Etiquette": "Welcome",
        "state": True,
        "membership": "level 2"
    },
]

for promo_data in promo_codes:
    PromoCode.objects.get_or_create(
        promo_code=promo_data["promo_code"],
        defaults={
            "label": promo_data["label"],
            "company": promo_data["company"],
            "start_duration": promo_data["start_duration"],
            "end_duration": promo_data["end_duration"],
            "stocks": promo_data["stocks"],
            "discount": promo_data["discount"],
            "Etiquette": promo_data["Etiquette"],
            "state": promo_data["state"],
            "membership": promo_data["membership"]
        }
    )

print("✅ Created promo codes")

# Create shields
shields = [
    {
        "shield_name": "Neighborhood Watch",
        "shield_type": "Individual",
        "shield_super_admin": user_profiles[0] if user_profiles else None,
        "condition": True,
    },
    {
        "shield_name": "Office Security",
        "shield_type": "Corporate",
        "shield_super_admin": user_profiles[1] if len(user_profiles) > 1 else None,
        "condition": True,
    },
]

shield_objects = []
for shield_data in shields:
    shield, created = ShieldModel.objects.get_or_create(
        shield_name=shield_data["shield_name"],
        defaults={
            "shield_type": shield_data["shield_type"],
            "shield_super_admin": shield_data["shield_super_admin"],
            "condition": shield_data["condition"],
        }
    )
    
    if shield_data["shield_super_admin"]:
        shield.admin.add(shield_data["shield_super_admin"])
        
        # Add some members to the shield
        for profile in user_profiles:
            if profile != shield_data["shield_super_admin"]:
                shield.members.add(profile)
                
    shield_objects.append(shield)
    
    # Add shield to company
    if company_objects:
        company = company_objects[0]
        company.shields.add(shield)

print(f"✅ Created/updated shields")

# Create points of interest
pois = [
    {
        "poi_address": "123 Main St, City",
        "poi_tag_name": "Police Station",
        "poi_lat": "40.7128",
        "poi_long": "-74.0060",
        "admin": user_profiles[0] if user_profiles else None,
    },
    {
        "poi_address": "456 Park Ave, City",
        "poi_tag_name": "Hospital",
        "poi_lat": "40.7129",
        "poi_long": "-74.0061",
        "admin": user_profiles[1] if len(user_profiles) > 1 else None,
    },
]

for poi_data in pois:
    poi, created = PointsOfInterest.objects.get_or_create(
        poi_tag_name=poi_data["poi_tag_name"],
        poi_address=poi_data["poi_address"],
        defaults={
            "poi_lat": poi_data["poi_lat"],
            "poi_long": poi_data["poi_long"],
            "admin": poi_data["admin"],
        }
    )
    
    # Add POI to shield
    if shield_objects:
        shield = shield_objects[0]
        shield.locations.add(poi)

print("✅ Created points of interest")

# Create alerts
alerts = [
    {
        "userprofile": user_profiles[0] if user_profiles else None,
        "category": AlertCategories.objects.first(),
        "status": AlertStatus.objects.first(),
        "description": "Car accident on Main Street",
        "address": "123 Main St, City",
        "lat": "40.7128",
        "long": "-74.0060",
        "current_speed": "0",
        "phone_battery": "75",
        "evidence_number": "EV12345",
    },
    {
        "userprofile": user_profiles[1] if len(user_profiles) > 1 else None,
        "category": AlertCategories.objects.last(),
        "status": AlertStatus.objects.last(),
        "description": "Medical emergency at Park Avenue",
        "address": "456 Park Ave, City",
        "lat": "40.7129",
        "long": "-74.0061",
        "current_speed": "0",
        "phone_battery": "50",
        "evidence_number": "EV12346",
    },
]

for alert_data in alerts:
    alert, created = AlertModel.objects.get_or_create(
        evidence_number=alert_data["evidence_number"],
        defaults={
            "userprofile": alert_data["userprofile"],
            "category": alert_data["category"],
            "status": alert_data["status"],
            "description": alert_data["description"],
            "address": alert_data["address"],
            "lat": alert_data["lat"],
            "long": alert_data["long"],
            "current_speed": alert_data["current_speed"],
            "phone_battery": alert_data["phone_battery"],
            "num": f"ALT{random.randint(10000, 99999)}",
        }
    )
    
    # Add alert to shield
    if shield_objects and created:
        shield = shield_objects[0]
        shield.alert.add(alert)

print("✅ Created alerts")

# Create tickets
tickets = [
    {
        "user": user_profiles[0] if user_profiles else None,
        "title": TicketSubject.objects.first(),
        "ticket_description": "I'm having trouble logging into my account",
        "resolved": False,
        "ticket_num": f"TKT{random.randint(10000, 99999)}",
    },
    {
        "user": user_profiles[1] if len(user_profiles) > 1 else None,
        "title": TicketSubject.objects.last(),
        "ticket_description": "I need help setting up a new shield",
        "resolved": False,
        "ticket_num": f"TKT{random.randint(10000, 99999)}",
    },
]

for ticket_data in tickets:
    Ticket.objects.get_or_create(
        ticket_num=ticket_data["ticket_num"],
        defaults={
            "user": ticket_data["user"],
            "title": ticket_data["title"],
            "ticket_description": ticket_data["ticket_description"],
            "resolved": ticket_data["resolved"],
        }
    )

print("✅ Created tickets")

# Create memberships
memberships = [
    {
        "userprofile": user_profiles[0] if user_profiles else None,
        "companyprofile": None,
        "membership": "Level_1",
        "unitary_amount": 9,
        "vat_amount": 1,
        "total_amount": 10,
        "conditions": "Effected",
        "payment_address": "123 Main St",
        "ipaddress": "***********",
        "payment_method": "Credit Card",
        "membership_end_date": datetime.date.today() + datetime.timedelta(days=365),
    },
    {
        "userprofile": None,
        "companyprofile": company_objects[0] if company_objects else None,
        "membership": "Level_3",
        "unitary_amount": 45,
        "vat_amount": 5,
        "total_amount": 50,
        "conditions": "Effected",
        "payment_address": "456 Corporate Ave",
        "ipaddress": "***********",
        "payment_method": "Bank Transfer",
        "membership_end_date": datetime.date.today() + datetime.timedelta(days=365),
    },
]

for membership_data in memberships:
    MembershipModel.objects.get_or_create(
        userprofile=membership_data["userprofile"],
        companyprofile=membership_data["companyprofile"],
        membership=membership_data["membership"],
        defaults={
            "unitary_amount": membership_data["unitary_amount"],
            "vat_amount": membership_data["vat_amount"],
            "total_amount": membership_data["total_amount"],
            "conditions": membership_data["conditions"],
            "payment_address": membership_data["payment_address"],
            "ipaddress": membership_data["ipaddress"],
            "payment_method": membership_data["payment_method"],
            "membership_end_date": membership_data["membership_end_date"],
        }
    )

print("✅ Created memberships")

# STEP 3: Create authentication tokens
print("\nSTEP 3: CREATING AUTHENTICATION TOKENS")
print("-" * 50)

print("Creating authentication tokens for users...")

# Get all users
users = User.objects.all()
count = 0

for user in users:
    # Create token if it doesn't exist
    token, created = Token.objects.get_or_create(user=user)
    if created:
        count += 1
        print(f"Created token for user: {user.username}")
    else:
        print(f"Token already exists for user: {user.username}")

print(f"\n✅ Created {count} new tokens.")

print("\n" + "=" * 80)
print("SETUP COMPLETED SUCCESSFULLY!")
print("=" * 80)
print("\nYou can now log in with the following credentials:")
print("Admin: <EMAIL> / Admin@123")
print("User: <EMAIL> / User@123")
print("\nThe admin panel should now be fully functional with sample data.")
print("=" * 80)
