from django.shortcuts import render
from rest_framework.views import APIView
from About import models  as about_models
from About import serializer as about_serializer
from rest_framework.permissions import IsAuthenticated, AllowAny
from mas_seguros_backend import utils as backend_utils
from rest_framework.response import Response
from rest_framework import status
from rest_framework.generics import get_object_or_404
# Create your views here.


class DataPolicy(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        data = about_models.DataPolicie.objects.all().last()
        serializer = about_serializer.DataPoliciesSerializer(data)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='Data Policy'))
    # put request to update the data policy
    def put(self, request, pk=None, format=None):
        post = get_object_or_404(about_models.DataPolicie.objects.all(), pk=pk)
        serializer = about_serializer.DataPoliciesSerializer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)


class TermsAndCondition(APIView):
    permission_classes = (AllowAny,)

    def get(self, request):
        data = about_models.TermsAndCondition.objects.all().last()
        serializer = about_serializer.TermsAndConditionSerializer(data)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='Terms And Conditions'))
    def put(self, request, pk=None, format=None):
        post = get_object_or_404(about_models.TermsAndCondition.objects.all(), pk=pk)
        serializer = about_serializer.TermsAndConditionSerializer(post, data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors,
                        status=status.HTTP_400_BAD_REQUEST)
