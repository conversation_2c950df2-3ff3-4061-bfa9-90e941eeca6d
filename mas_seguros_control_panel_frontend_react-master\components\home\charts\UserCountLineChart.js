import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import useAxios from "@/hooks/useAxios";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const options = {
  responsive: true,
  interaction: {
    intersect: false,
  },
  scales: {
    x: {
      border: {
        display: false,
      },
      ticks: {
        font: {
          size: 14,
          weight: 700,
          color: "black",
        },
      },
      grid: {
        display: false,
      },
    },
  },
  plugins: {
    legend: {
      display: false,
    },
  },
};

const labels = [
  "ENE",
  "FEB",
  "MAR",
  "ABR",
  "MAY",
  "JUN",
  "JUL",
  "AGO",
  "SEP",
  "OCT",
  "NOV",
  "DIC",
  "ENE",
];

const UserCountLineChart = () => {
  const [chartData, setChartData] = useState({
    labels,
    datasets: [
      {
        label: "Usuarios",
        data: Array(13).fill(0),
        borderColor: "#1555ED",
        backgroundColor: labels.map(() => "#fff"),
        pointRadius: 3,
        pointHoverRadius: 4,
        pointBorderWidth: 3,
      },
    ],
  });

  const [totalUsers, setTotalUsers] = useState(0);
  const { axios } = useAxios();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get("/api/dashboard/registered-users-per-month/");
        if (response.data && response.data.data) {
          const userData = response.data.data;

          // Calculate total users
          let total = 0;
          const monthlyData = Array(13).fill(0);

          // Map API data to chart data
          userData.forEach(item => {
            const month = parseInt(item.month);
            if (month >= 1 && month <= 12) {
              monthlyData[month-1] = item.count;
              total += item.count;
            }
          });

          // Add January of next year as the last point (for continuity)
          monthlyData[12] = monthlyData[0];

          setTotalUsers(total);
          setChartData({
            labels,
            datasets: [
              {
                label: "Usuarios",
                data: monthlyData,
                borderColor: "#1555ED",
                backgroundColor: labels.map(() => "#fff"),
                pointRadius: 3,
                pointHoverRadius: 4,
                pointBorderWidth: 3,
              },
            ],
          });
        }
      } catch (error) {
        console.error("Error fetching user chart data:", error);
        setTotalUsers(0);
      }
    };

    fetchData();
  }, []);

  return (
    <div>
      <h3 className="text-right mb-2">Total Usuarios: {totalUsers}</h3>
      <Line options={options} data={chartData} />
    </div>
  );
};

export default UserCountLineChart;
