import ProfilePicture from "@/components/ProfilePicture";
import Table from "@/components/Table";
import InputGroup from "@/components/utility/InputGroup";
import Modal from "@/components/utility/Modal";
import useAxios from "@/hooks/useAxios";
import { format } from "date-fns";
import React, { createElement, useState } from "react";
import { useQuery } from "react-query";
import { toast } from "react-hot-toast";

const LocationHistoryBtn = ({ as = "button", member, ...props }) => {
  const [open, setOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');

  const close = () => {
    setOpen(false);
  };



  const { axios } = useAxios()


  const fetchData = () => {
    // Get the member ID from either the user object or directly from member
    const memberId = member?.user?.id || member.id;

    if (!memberId) {
      console.error("No member ID found for location history");
      return Promise.resolve({ data: [] });
    }

    return axios.get(`adminside/api/shield/shield-members-locations/`, {
      params: {
        member_id: memberId
      }
    });
  }

  // React-query for data fetching
  const { isLoading, isError, data: response, error } = useQuery(
    `shield-member-${member?.user?.id || member.id}-location-history`,
    fetchData,
    {
      refetchOnWindowFocus: false,
      enabled: open && !!(member?.user?.id || member.id)
    }
  );

  const items = response?.data ?? []

  // Filter items based on selected date
  const filteredItems = selectedDate
    ? items.filter(item => {
        if (!item.created_at) return false;
        const itemDate = format(new Date(item.created_at), 'yyyy-MM-dd');
        return itemDate === selectedDate;
      })
    : items;

  const handleSearch = () => {
    // CA0092: Validation of empty fields - prevent search when date field is empty
    if (!selectedDate) {
      toast.error('Debe seleccionar una fecha para buscar');
      return;
    }

    // The filtering is already handled by the filteredItems calculation above
    // Additional validation for date format
    try {
      const parsedDate = new Date(selectedDate);
      if (isNaN(parsedDate.getTime())) {
        toast.error('Formato de fecha inválido');
        return;
      }

      // If we reach here, the search is valid
      toast.success('Búsqueda aplicada correctamente');
    } catch (error) {
      toast.error('Error en la fecha seleccionada');
    }
  };

  const clearSearch = () => {
    setSelectedDate('');
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-2xl overflow-hidden rounded bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Historial de Ubicaciones</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <div className="flex justify-between gap-3 p-5">
            <div className="flex gap-3 text-sm">
              <ProfilePicture
                src={member.image_url ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${member.image_url}` : null}
                className="inline-block h-11 w-11 rounded-full"
              />
              <div>
                <dd>{member.full_name || 'N/A'}</dd>
                <dd>ID-{member?.user?.id || member.id || 'N/A'}</dd>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span>Buscar</span>
              <div>
                <InputGroup>
                  <InputGroup.Input
                    type="date"
                    className="!border-none bg-accent"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                  />
                </InputGroup>
              </div>
              <button
                onClick={handleSearch}
                className="rounded bg-primary py-1.5 px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-primary/90"
              >
                Buscar
              </button>
              {selectedDate && (
                <button
                  onClick={clearSearch}
                  className="rounded bg-gray-500 py-1.5 px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-gray-600"
                >
                  Limpiar
                </button>
              )}
            </div>
          </div>
          <Modal.Body className="space-y-7 py-5">
            <Table
              wrapperClassName="bg-accent px-4"
              dataCount={filteredItems.length}
              isLoading={isLoading}
              isError={isError}
              error={error}
            >
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Horario</Table.Th>
                  <Table.Th>Ubicación</Table.Th>
                  <Table.Th>Coordenada</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {/* CA0093: Modal without data - specific message for filtered results */}
                {!isLoading && !isError && filteredItems.length === 0 && selectedDate && (
                  <Table.Tr>
                    <Table.Td colSpan={3} className="text-center py-4">
                      No hay ubicaciones disponibles para el rango seleccionado
                    </Table.Td>
                  </Table.Tr>
                )}
                {/* No data message when no date filter is applied */}
                {!isLoading && !isError && filteredItems.length === 0 && !selectedDate && (
                  <Table.Tr>
                    <Table.Td colSpan={3} className="text-center py-4">
                      No hay ubicaciones disponibles
                    </Table.Td>
                  </Table.Tr>
                )}
                {!isLoading && !isError &&
                  filteredItems?.map((item) => (
                    <Table.Tr key={item.id}>
                      <Table.Td>
                        <dd>{format(new Date(item.created_at), 'p')}</dd>
                        <dd>{format(new Date(item.created_at), 'dd/MM/yyyy')}</dd>
                      </Table.Td>
                      <Table.Td className="!whitespace-normal">
                        {item.location}
                      </Table.Td>
                      <Table.Td className="font-semibold">
                        {item.lat_long}
                      </Table.Td>
                    </Table.Tr>
                  ))}
              </Table.Tbody>
            </Table>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-black text-white">
              Aceptar
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        ...props,
        onClick: () => setOpen(true),
      })}
    </>
  );
};

export default LocationHistoryBtn;
