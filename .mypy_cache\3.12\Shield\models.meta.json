{"data_mtime": **********, "dep_lines": [8, 3, 4, 5, 7, 8, 3, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 20, 20, 20, 10, 20, 10, 5, 30, 30, 30, 30, 5], "dependencies": ["AdminSide.AdminAPi.models", "Shield.utils", "Account.models", "Alert.models", "mas_seguros_backend.settings", "AdminSide.AdminAPi", "Shield", "Account", "<PERSON><PERSON>", "datetime", "mas_seguros_backend", "random", "builtins", "AdminSide", "_frozen_importlib", "abc", "typing"], "hash": "1b6c655d555a0db9f54390bcb3f24f554d080fdc", "id": "Shield.models", "ignore_all": true, "interface_hash": "2991c9de8a1b47d30df954d95b94b89d8311ae6e", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\MAS_SEGUROS\\local_project\\mas_seguros_backend-main\\Shield\\models.py", "plugin_data": null, "size": 10576, "suppressed": ["django.db"], "version_id": "1.15.0"}