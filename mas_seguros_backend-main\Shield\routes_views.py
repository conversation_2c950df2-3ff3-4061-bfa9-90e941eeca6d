from . import models as shield_models, serializers as shield_serialziers
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from rest_framework.generics import get_object_or_404
from Account import models as account_models
from datetime import datetime, timedelta
import json
from django.utils import timezone
from Shield import utils as shield_utils


class ShieldMemberLocationsBattery(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        shield_obj = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield_obj:
            member_objs = shield_obj.members.all().order_by('user__id')
            result = []
            for member in member_objs:
                member_detail = {}
                member_detail['name'] = member.full_name
                member_detail['user_id'] = member.user.id
                member_detail['image_url'] = member.image_url
                
                # Get the most recent location data for this member in this shield
                latest_location = shield_models.Location.objects.filter(
                    shield=shield_obj, 
                    userprofile=member
                ).order_by('-created_at').first()
                
                # Get current active route (if any)
                active_route = shield_models.Route.objects.filter(
                    shield=shield_obj, 
                    member=member,
                    route_completed=False
                ).order_by('-created_at').first()
                
                # Get the most recent completed route for battery/speed history
                latest_completed_route = shield_models.Route.objects.filter(
                    shield=shield_obj, 
                    member=member,
                    route_completed=True
                ).order_by('-created_at').first()
                
                if latest_location:
                    # We have recent location data
                    location_time = latest_location.created_at
                    
                    # Format the time
                    try:
                        if location_time:
                            # Convert to local time and format
                            local_time = timezone.localtime(location_time)
                            formatted_time = local_time.strftime('%I:%M %p')
                        else:
                            formatted_time = "Unknown"
                    except Exception:
                        formatted_time = "Unknown"
                    
                    # Determine current status based on latest data
                    if active_route:
                        # User is currently on a route
                        member_detail['status'] = "On Route"
                        member_detail['on_route'] = True
                        member_detail['location'] = latest_location.location or 'En Route'
                        member_detail['on_location_since'] = formatted_time
                        
                        # Use route speed data - but cap unrealistic speeds
                        try:
                            speed_value = float(active_route.max_speed or '0')
                            # Cap speed at realistic maximum (e.g., 150 km/h for cars, but show as 150+ for anything higher)
                            if speed_value > 150:
                                member_detail['current_speed'] = "150+ km/h"
                            else:
                                member_detail['current_speed'] = f"{speed_value:.0f} km/h"
                        except (ValueError, TypeError):
                            member_detail['current_speed'] = "0 km/h"
                            
                        # Use route battery data (should be current/latest)
                        battery = active_route.minimum_phone_battery if active_route.minimum_phone_battery else "100"
                        
                    elif latest_location.point_of_interest:
                        # User is at a POI
                        poi_name = latest_location.point_of_interest.poi_tag_name.lower()
                        if "home" in poi_name or "casa" in poi_name:
                            member_detail['status'] = "At Home"
                        else:
                            member_detail['status'] = "At POI"
                            
                        member_detail['on_route'] = False
                        member_detail['location'] = latest_location.point_of_interest.poi_tag_name
                        member_detail['on_location_since'] = formatted_time
                        member_detail['current_speed'] = "0 km/h"
                        
                        # Try to get battery from the most recent route (active or completed)
                        most_recent_route = shield_models.Route.objects.filter(
                            shield=shield_obj, 
                            member=member
                        ).order_by('-created_at').first()
                        
                        if most_recent_route and most_recent_route.minimum_phone_battery:
                            battery = most_recent_route.minimum_phone_battery
                        else:
                            battery = "100"  # Default if no route data
                            
                    else:
                        # User has location but not at POI and not on route
                        member_detail['status'] = "Moving"
                        member_detail['on_route'] = False
                        member_detail['location'] = latest_location.location or f"Location: {latest_location.lat}, {latest_location.long}"
                        member_detail['on_location_since'] = formatted_time
                        member_detail['current_speed'] = "0 km/h"
                        
                        # Get battery from the most recent route (which should contain latest data)
                        most_recent_route = shield_models.Route.objects.filter(
                            shield=shield_obj, 
                            member=member
                        ).order_by('-created_at').first()
                        
                        if most_recent_route and most_recent_route.minimum_phone_battery:
                            battery = most_recent_route.minimum_phone_battery
                        else:
                            battery = "100"  # Default
                    
                    # Format battery information
                    try:
                        battery_value = int(float(battery))
                        member_detail['battery'] = f"{battery_value}%"
                        member_detail['battery_level'] = battery_value
                        
                        # Add battery icon status
                        if battery_value == 0:
                            member_detail['battery_icon_status'] = "gray"
                        elif battery_value < 20:
                            member_detail['battery_icon_status'] = "red"
                        elif battery_value < 50:
                            member_detail['battery_icon_status'] = "yellow"
                        else:
                            member_detail['battery_icon_status'] = "green"
                    except (ValueError, TypeError):
                        member_detail['battery'] = "100%"
                        member_detail['battery_level'] = 100
                        member_detail['battery_icon_status'] = "green"
                        
                else:
                    # No location data for this member - show "No recent location"
                    member_detail['status'] = "No recent location"
                    member_detail['on_route'] = False
                    member_detail['location'] = 'No recent location'
                    member_detail['on_location_since'] = "Unknown"
                    member_detail['battery'] = "0%"
                    member_detail['battery_level'] = 0
                    member_detail['battery_icon_status'] = "gray"
                    member_detail['current_speed'] = "0 km/h"
                
                result.append(member_detail)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=result,
                                               msg='All members details'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No shield of this ID found'), status.HTTP_400_BAD_REQUEST)


class UserRouteProfile(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldUserRouteProfileSerializer

    def get(self, request):
        try:
            serializer = self.serializer_class(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            shield_id = request.query_params.get('shield_id')
            user_id = request.query_params.get('user_id')

            # Validate required parameters
            if not shield_id or not user_id:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='shield_id and user_id are required'),
                    status.HTTP_400_BAD_REQUEST)

            # Check if requesting user is in the shield
            check_user_in_shield = shield_utils.check_user_in_shield(shield_id, request.user)
            if not check_user_in_shield:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                   msg='You are not a member of this shield'),
                    status.HTTP_400_BAD_REQUEST)

            # Get shield
            shield = shield_models.ShieldModel.objects.filter(id=shield_id).first()
            if not shield:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                                   msg='Shield not found'),
                    status.HTTP_404_NOT_FOUND)

            # Get member
            member_obj = shield.members.filter(user_id=user_id).first()
            if not member_obj:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                                   msg='Member not found in this shield'),
                    status.HTTP_404_NOT_FOUND)

            # Initialize result
            result = {}
            result['member'] = member_obj

            # Get last point of interest
            last_poi = shield_models.Location.objects.filter(
                shield=shield,
                userprofile=member_obj,
                point_of_interest__isnull=False
            ).order_by('-created_at').first()

            if last_poi and last_poi.point_of_interest:
                result["last_poi"] = last_poi.point_of_interest.poi_tag_name
                result["last_poi_time"] = last_poi.created_at
            else:
                result["last_poi"] = "No recent location"
                result["last_poi_time"] = None

            # Get current location
            current_location = shield_models.Location.objects.filter(
                shield=shield,
                userprofile=member_obj
            ).order_by('-created_at').first()

            if current_location:
                result["current_location"] = current_location.location
                result["current_address"] = current_location.location  # Full address
                # Find when user arrived at this location
                same_location = shield_models.Location.objects.filter(
                    route=current_location.route,
                    location=current_location.location
                ).order_by('created_at').first()
                result["current_location_since"] = same_location.created_at if same_location else current_location.created_at
            else:
                result["current_location"] = "No recent location"
                result["current_address"] = "No recent location"
                result["current_location_since"] = None

            # Get last route
            last_route = shield_models.Route.objects.filter(
                member=member_obj,
                shield=shield
            ).order_by('-created_at').first()

            if last_route:
                # Set current status
                if last_route.route_completed:
                    result["on_route"] = False
                    result["current_status"] = "At home" if (last_route.ending_poi and "home" in last_route.ending_poi.poi_tag_name.lower()) else "At POI"
                    result["max_speed"] = last_route.max_speed
                    result["min_battery"] = last_route.minimum_phone_battery
                    result["current_speed"] = "0 km/h"  # Not moving
                    result["battery_percentage"] = last_route.minimum_phone_battery
                    
                    # Calculate time since route ended
                    if last_route.route_ending_time and last_route.created_at:
                        route_end_datetime = datetime.combine(
                            last_route.created_at.date(),
                            last_route.route_ending_time
                        )
                        # Make it timezone aware
                        route_end_datetime = timezone.make_aware(route_end_datetime)
                        current_time = timezone.now()
                        time_diff = current_time - route_end_datetime
                        result["on_route_since"] = f"{int(time_diff.total_seconds() / 60)} minutes ago"
                    else:
                        result["on_route_since"] = "Unknown"
                else:
                    result["on_route"] = True
                    result["current_status"] = "On route"
                    
                    # Get the most recent location for speed data
                    latest_location = shield_models.Location.objects.filter(
                        route=last_route,
                        userprofile=member_obj
                    ).order_by('-created_at').first()
                    
                    # Calculate current speed (use max_speed if we don't have real-time data)
                    try:
                        speed_value = float(last_route.max_speed or '0')
                        result["current_speed"] = f"{speed_value} km/h"
                    except (ValueError, TypeError):
                        result["current_speed"] = "0 km/h"
                    
                    # Calculate time since route started
                    if last_route.route_starting_time and last_route.created_at:
                        route_start_datetime = datetime.combine(
                            last_route.created_at.date(),
                            last_route.route_starting_time
                        )
                        # Make it timezone aware
                        route_start_datetime = timezone.make_aware(route_start_datetime)
                        current_time = timezone.now()
                        time_diff = current_time - route_start_datetime
                        result["on_route_since"] = f"{int(time_diff.total_seconds() / 60)} minutes"
                    else:
                        result["on_route_since"] = "Unknown"

                    result["max_speed"] = last_route.max_speed
                    result["min_battery"] = last_route.minimum_phone_battery
                    result["battery_percentage"] = last_route.minimum_phone_battery
            else:
                # No route data available
                result["on_route"] = False
                result["current_status"] = "Unknown"
                result["max_speed"] = "0"
                result["min_battery"] = "0"
                result["current_speed"] = "0 km/h"
                result["battery_percentage"] = "0"
                result["on_route_since"] = "No routes available"

            # Serialize the result
            routes_serializer = shield_serialziers.UserProfileRouteSerializer(result)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=routes_serializer.data,
                                               msg='User route profile retrieved successfully'),
                status.HTTP_200_OK)

        except Exception as e:
            print(f"Error in UserRouteProfile: {str(e)}")
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                               msg=f'Internal server error: {str(e)}'),
                status.HTTP_500_INTERNAL_SERVER_ERROR)


class MemberRoutesHistory(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        member_id = request.user.id
        limit = int(request.query_params.get('limit', 50))
        offset = int(request.query_params.get('offset', 0))
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            member_obj = shield.members.filter(id=member_id)
            if member_obj:
                # Remove date filter, add pagination
                routes_qs = (
                    shield_models.Route.objects
                    .filter(member=member_obj[0], shield_id=shield_id)
                    .select_related('starting_poi', 'ending_poi')
                    .order_by('-id')
                )
                total_count = routes_qs.count()
                routes = routes_qs[offset:offset+limit]
                routes_serializer = shield_serialziers.MemberRouteSerializer(routes, many=True)
                return Response(
                    backend_utils.success_response(
                        status_code=status.HTTP_200_OK,
                        data={
                            'count': total_count,
                            'results': routes_serializer.data
                        },
                        msg='All routes of this member in this shield'
                    ),
                    status.HTTP_200_OK
                )
            else:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='El escudo actual fue eliminado'
                    ),
                    status.HTTP_400_BAD_REQUEST
                )
        else:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg='No Shield of this ID found'
                ),
                status.HTTP_400_BAD_REQUEST
            )


class MemberLocation(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldMemberLocationSerializer

    def post(self, request):
        # Validate the incoming data
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Get parameters from the request
        member_id = request.user.id
        lat = request.data.get('lat')
        long = request.data.get('long')
        location = request.data.get('location')
        speed = request.data.get('speed', '0')  # Default to 0 if not provided
        phone_battery = request.data.get('phone_battery', '0')  # Default to 0 if not provided
        
        # Input validation
        if not lat or not long:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg="Latitude and longitude are required"
                ), 
                status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Validate lat/long format
            float(lat)
            float(long)
        except (ValueError, TypeError):
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    msg="Invalid latitude or longitude format"
                ), 
                status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Update user profile with the latest coordinates
            member_obj = account_models.UserProfile.objects.filter(user=request.user).last()
            if not member_obj:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg="User profile not found"
                    ), 
                    status.HTTP_404_NOT_FOUND
                )
                
            # Update the user's location data
            member_obj.lat = lat
            member_obj.long = long
            member_obj.save()
            
            # Get all shields that the user belongs to
            shield_objs = shield_models.ShieldModel.objects.filter(members__id=request.user.userprofile.id)
            if not shield_objs.exists():
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg="User not a member of any shield"
                    ), 
                    status.HTTP_404_NOT_FOUND
                )
            
            # Create location entries for each shield
            locations_created = []
            
            for shield_obj in shield_objs:
                # Check if user is on an active route for this shield
                route_obj = shield_models.Route.objects.filter(
                    member=member_obj, 
                    route_completed=False,
                    shield=shield_obj
                ).order_by('-created_at').first()
                
                # Check if user is at a point of interest
                point_of_interest_exists = shield_utils.check_point_of_interest_exists_in_making_location(
                    shield_obj, lat, long
                )
                
                # IMPROVED LOGIC: Always ensure we have a route for real-time tracking
                if not point_of_interest_exists and not route_obj:
                    # User is not at a POI and not on a route - CREATE a tracking route for real-time data
                    # This ensures battery and speed data is always stored and accessible
                    route_obj = shield_models.Route.objects.create(
                        shield=shield_obj, 
                        member=member_obj, 
                        max_speed=speed or '0',
                        minimum_phone_battery=phone_battery or '100',
                        route_completed=False  # Keep this open for continuous updates
                    )
                    
                    location_obj = shield_models.Location.objects.create(
                        location=location or f"Location at {lat}, {long}", 
                        shield=shield_obj, 
                        userprofile=member_obj,
                        lat=lat, 
                        long=long,
                        route=route_obj  # Link to the tracking route
                    )
                    locations_created.append(location_obj)
                    
                elif not point_of_interest_exists and route_obj:
                    # User is on a route but not at a POI - update route with latest data
                    # Update route with latest speed data
                    try:
                        current_max_speed = float(route_obj.max_speed or '0')
                        new_speed = float(speed or '0')
                        if new_speed > current_max_speed:
                            route_obj.max_speed = str(new_speed)
                    except (ValueError, TypeError):
                        route_obj.max_speed = speed or '0'
                    
                    # CRITICAL: Always update battery to the LATEST value for real-time data
                    # For mobile app real-time tracking, we want current battery, not minimum
                    if phone_battery:
                        route_obj.minimum_phone_battery = phone_battery
                    
                    route_obj.save()
                    
                    # Create new location entry linked to the route
                    location_obj = shield_models.Location.objects.create(
                        location=location or f"En route at {lat}, {long}", 
                        shield=shield_obj,
                        userprofile=member_obj,
                        lat=lat, 
                        long=long, 
                        route=route_obj
                    )
                    locations_created.append(location_obj)
                    
                elif point_of_interest_exists and not route_obj:
                    # User just arrived at a POI and no active route - create a new route
                    route = shield_models.Route.objects.create(
                        shield=shield_obj, 
                        member=member_obj, 
                        max_speed=speed or '0',
                        minimum_phone_battery=phone_battery or '100',
                        starting_poi=point_of_interest_exists
                    )
                    
                    # Create location entry linked to the new route and POI
                    location_obj = shield_models.Location.objects.create(
                        location=location or point_of_interest_exists.poi_tag_name, 
                        shield=shield_obj,
                        userprofile=member_obj,
                        lat=lat, 
                        long=long, 
                        route=route,
                        point_of_interest=point_of_interest_exists
                    )
                    locations_created.append(location_obj)
                    
                elif point_of_interest_exists and route_obj:
                    # User is on a route and arrived at a POI - complete the route
                    route_obj.route_completed = True
                    route_obj.ending_poi = point_of_interest_exists
                    route_obj.route_ending_time = datetime.now().time()
                    
                    # Update max speed if higher
                    try:
                        if speed and float(speed) > float(route_obj.max_speed or '0'):
                            route_obj.max_speed = speed
                    except (ValueError, TypeError):
                        pass
                        
                    # Update minimum battery if lower
                    try:
                        if phone_battery and (
                            not route_obj.minimum_phone_battery or 
                            float(phone_battery) < float(route_obj.minimum_phone_battery)
                        ):
                            route_obj.minimum_phone_battery = phone_battery
                    except (ValueError, TypeError):
                        pass
                        
                    route_obj.save()
                    
                    # Create final location entry for the completed route
                    location_obj = shield_models.Location.objects.create(
                        location=location or point_of_interest_exists.poi_tag_name, 
                        shield=shield_obj,
                        userprofile=member_obj,
                        lat=lat, 
                        long=long, 
                        route=route_obj,
                        point_of_interest=point_of_interest_exists
                    )
                    locations_created.append(location_obj)
            
            # Return appropriate response
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data={
                        "locations_created": len(locations_created),
                        "shields_updated": shield_objs.count(),
                        "lat": lat,
                        "long": long,
                        "speed": speed,
                        "battery": phone_battery
                    },
                    msg="Location updated successfully in all shields"
                ),
                status.HTTP_200_OK
            )
            
        except Exception as e:
            print(f"Error in MemberLocation: {str(e)}")
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg=f"Error updating location: {str(e)}"
                ),
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# class MemberLocation(APIView):
#     permission_classes = (IsAuthenticated,)
#     serializer_class = shield_serialziers.ShieldMemberLocationSerializer
#
#     def post(self, request):
#         # this API creates member location entry in the db
#         serializer = self.serializer_class(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         member_id = request.user.id
#         lat = request.data.get('lat')
#         print("=====22341")
#         long = request.data.get('long')
#         location = request.data.get('location')
#         speed = request.data.get('speed')
#         phone_battery = request.data.get('phone_battery')
#         # get all the shields to which the user belongs
#         all_locations_added = []
#         member_obj = account_models.UserProfile.objects.filter(user=request.user)
#
#         shield_objs = shield_models.ShieldModel.objects.filter(members__id=request.user.userprofile.id)
#
#         for shield_obj in shield_objs:
#             print("===ok====")
#             # check if member is on route already
#             user_on_poi = shield_obj.locations.filter(poi_lat=lat).filter(poi_long=long).last()
#             user_route_obj = shield_models.Route.objects.filter(shield=shield_obj).filter(member_id=member_id).filter(
#                 route_completed=False).last()
#             if user_on_poi:
#                 print("=====1==")
#                 # user is on poi of shield
#                 if user_route_obj:
#                     now = datetime.now()
#                     current_time = now.strftime("%H:%M:%S")
#                     # if user has already started the route
#
#                     max_speed = speed if speed > user_route_obj.max_speed else user_route_obj.max_speed
#                     minimum_phone_battery = phone_battery if phone_battery < user_route_obj.minimum_phone_battery else user_route_obj.minimum_phone_battery
#
#                     shield_models.Route.objects.filter(shield=shield_obj).filter(member_id=member_id).filter(
#                         route_completed=False).update(ending_poi=user_on_poi, route_completed=True,
#                                                       route_ending_time=current_time,
#                                                       minimum_phone_battery=minimum_phone_battery, max_speed=max_speed)
#                 else:
#                     # start a route for the user as he is already on POI
#                     user_route_obj = shield_models.Route.objects.create(shield=shield_obj, member_id=member_id,
#                                                                         starting_poi=user_on_poi,
#                                                                         route_completed=False,
#                                                                         minimum_phone_battery=phone_battery,
#                                                                         max_speed=speed)
#                 last_location = shield_models.Location.objects.filter(route=user_route_obj, userprofile_id=member_id,
#                                                                       shield=shield_obj).last()
#                 if last_location and last_location.location != location:
#                     shield_models.Location.objects.filter(id=last_location.id).update(location_changed=True)
#                 location_obj = shield_models.Location.objects.create(route=user_route_obj, location=location,
#                                                                      lat=lat, long=long, userprofile_id=member_id,
#                                                                      point_of_interest=user_on_poi, shield=shield_obj)
#                 all_locations_added.append(location_obj)
#             else:
#                 print("=====2")
#                 # user is not on any poi
#                 if user_route_obj:
#                     print("=====3", user_route_obj)
#                     max_speed = speed if speed > user_route_obj.max_speed else user_route_obj.max_speed
#                     minimum_phone_battery = phone_battery if phone_battery < user_route_obj.minimum_phone_battery else user_route_obj.minimum_phone_battery
#                     shield_models.Route.objects.filter(shield=shield_obj).filter(member_id=member_id).filter(
#                         route_completed=False).update(minimum_phone_battery=minimum_phone_battery, max_speed=max_speed)
#                     last_location = shield_models.Location.objects.filter(route=user_route_obj,
#                                                                           userprofile_id=member_id,
#                                                                           shield=shield_obj).last()
#                     if last_location and last_location.location != location:
#                         shield_models.Location.objects.filter(id=last_location.id).update(location_changed=True)
#                     location_obj = shield_models.Location.objects.create(route=user_route_obj, location=location,
#                                                                          lat=lat, long=long, userprofile_id=member_id,
#                                                                          shield=shield_obj)
#                     all_locations_added.append(location_obj)
#         if not all_locations_added:
#             return Response(
#                 backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
#                                                msg='User did not start the route from any poi yet'),
#                 status.HTTP_400_BAD_REQUEST)
#
#         location_serializer = shield_serialziers.MemberLocationSerializer(all_locations_added, many=True)
#         return Response(backend_utils.success_response(status_code=status.HTTP_200_OK,
#                                                        data=location_serializer.data,
#                                                        msg="Location created in all shields of the user"),
#                         status.HTTP_200_OK)


class RouteDetail(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.GetRouteDetailSerializer

    def get(self, request):
        from django.db.models import Q
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        route_id_param = request.query_params.get('route_id')
        # Try to find by id or by route_id string
        route_obj = shield_models.Route.objects.filter(Q(id=route_id_param) | Q(route_id=route_id_param)).last()
        
        if route_obj and route_obj.shield:
            shield = route_obj.shield
            result = {}
            
            # Basic route information
            result["route_id"] = route_obj.route_id
            result["member_name"] = route_obj.member.full_name if route_obj.member else "Unknown"
            result["member_id"] = route_obj.member.id if route_obj.member else None
            result["member_image"] = route_obj.member.image_url if route_obj.member else None
            
            # Route status
            result["route_completed"] = route_obj.route_completed
            result["route_status"] = "Completed" if route_obj.route_completed else "In Progress"
            
            # Route date and time information
            result["route_date"] = str(route_obj.route_date) if route_obj.route_date else None
            
            # Starting location information
            if route_obj.starting_poi:
                result["starting_poi"] = {
                    "name": route_obj.starting_poi.poi_tag_name or "Unknown",
                    "address": route_obj.starting_poi.poi_address or "Unknown",
                    "lat": route_obj.starting_poi.poi_lat,
                    "long": route_obj.starting_poi.poi_long
                }
            else:
                result["starting_poi"] = {
                    "name": "Unknown",
                    "address": "Unknown",
                    "lat": None,
                    "long": None
                }
            
            # Format starting_time to ISO 8601 with Z
            if route_obj.route_date and route_obj.route_starting_time:
                from datetime import datetime
                start_dt = datetime.combine(route_obj.route_date, route_obj.route_starting_time)
                result["starting_time"] = start_dt.strftime("%Y-%m-%dT%H:%M:%SZ")
                
                # Add formatted time for display
                try:
                    time_obj = datetime.strptime(str(route_obj.route_starting_time), '%H:%M:%S.%f')
                    result["starting_time_formatted"] = time_obj.strftime('%I:%M %p')
                except ValueError:
                    try:
                        time_obj = datetime.strptime(str(route_obj.route_starting_time), '%H:%M:%S')
                        result["starting_time_formatted"] = time_obj.strftime('%I:%M %p')
                    except ValueError:
                        result["starting_time_formatted"] = str(route_obj.route_starting_time)
            else:
                result["starting_time"] = None
                result["starting_time_formatted"] = None
            
            # Max speed with units
            max_speed = route_obj.max_speed
            if max_speed is not None:
                try:
                    max_speed_val = float(max_speed)
                    result["max_speed"] = f"{max_speed_val:.0f} km/h"
                except Exception:
                    result["max_speed"] = f"{max_speed} km/h"
            else:
                result["max_speed"] = "0 km/h"
                
            # Minimum phone battery with %
            min_battery = route_obj.minimum_phone_battery
            if min_battery is not None:
                try:
                    min_battery_val = int(float(min_battery))
                    result["minimum_phone_battery"] = f"{min_battery_val}%"
                    
                    # Add battery status based on value
                    if min_battery_val == 0:
                        result["battery_status"] = "critical"
                    elif min_battery_val < 20:
                        result["battery_status"] = "low"
                    elif min_battery_val < 50:
                        result["battery_status"] = "medium"
                    else:
                        result["battery_status"] = "good"
                        
                except Exception:
                    result["minimum_phone_battery"] = f"{min_battery}%"
                    result["battery_status"] = "unknown"
            else:
                result["minimum_phone_battery"] = "0%"
                result["battery_status"] = "unknown"
                
            # Ending POI and time
            if route_obj.route_completed:
                if route_obj.ending_poi:
                    result["ending_poi"] = {
                        "name": route_obj.ending_poi.poi_tag_name or "Unknown",
                        "address": route_obj.ending_poi.poi_address or "Unknown",
                        "lat": route_obj.ending_poi.poi_lat,
                        "long": route_obj.ending_poi.poi_long
                    }
                else:
                    result["ending_poi"] = {
                        "name": "Unknown",
                        "address": "Unknown",
                        "lat": None,
                        "long": None
                    }
                
                if route_obj.route_date and route_obj.route_ending_time:
                    from datetime import datetime
                    end_dt = datetime.combine(route_obj.route_date, route_obj.route_ending_time)
                    result["ending_time"] = end_dt.strftime("%Y-%m-%dT%H:%M:%SZ")
                    
                    # Add formatted time for display
                    try:
                        time_obj = datetime.strptime(str(route_obj.route_ending_time), '%H:%M:%S.%f')
                        result["ending_time_formatted"] = time_obj.strftime('%I:%M %p')
                    except ValueError:
                        try:
                            time_obj = datetime.strptime(str(route_obj.route_ending_time), '%H:%M:%S')
                            result["ending_time_formatted"] = time_obj.strftime('%I:%M %p')
                        except ValueError:
                            result["ending_time_formatted"] = str(route_obj.route_ending_time)
                else:
                    result["ending_time"] = None
                    result["ending_time_formatted"] = None
            else:
                result["ending_poi"] = None
                result["ending_time"] = None
                result["ending_time_formatted"] = None
            
            # Get all route points with chronological details
            locations = shield_models.Location.objects.filter(route_id=route_obj.id).order_by('created_at')
            all_locations = []
            
            for index, location in enumerate(locations):
                loc = {}
                # ISO 8601 with Z
                loc['time'] = location.created_at.strftime("%Y-%m-%dT%H:%M:%SZ") if location.created_at else None
                
                # Format time for display
                if location.created_at:
                    try:
                        loc['time_formatted'] = location.created_at.strftime('%I:%M %p')
                    except:
                        loc['time_formatted'] = str(location.created_at.time())
                else:
                    loc['time_formatted'] = None
                    
                # Sequence number
                loc['sequence'] = index + 1
                
                # Location information
                loc['location'] = location.location
                loc['address'] = location.location  # Full address
                loc['lat'] = str(location.lat) if hasattr(location, 'lat') and location.lat is not None else None
                loc['long'] = str(location.long) if hasattr(location, 'long') and location.long is not None else None
                
                # Point of interest
                if location.point_of_interest:
                    loc['poi'] = {
                        'name': location.point_of_interest.poi_tag_name,
                        'address': location.point_of_interest.poi_address,
                        'lat': location.point_of_interest.poi_lat,
                        'long': location.point_of_interest.poi_long
                    }
                else:
                    loc['poi'] = None
                
                all_locations.append(loc)
                
            result["route_points"] = all_locations
            result["total_points"] = len(all_locations)
            
            # Calculate route duration if completed
            if route_obj.route_completed and route_obj.route_starting_time and route_obj.route_ending_time:
                try:
                    from datetime import datetime
                    start_time = datetime.strptime(str(route_obj.route_starting_time), '%H:%M:%S.%f')
                    end_time = datetime.strptime(str(route_obj.route_ending_time), '%H:%M:%S.%f')
                    duration_minutes = (end_time - start_time).seconds // 60
                    result["route_duration_minutes"] = duration_minutes
                    result["route_duration_formatted"] = f"{duration_minutes // 60}h {duration_minutes % 60}m"
                except ValueError:
                    try:
                        start_time = datetime.strptime(str(route_obj.route_starting_time), '%H:%M:%S')
                        end_time = datetime.strptime(str(route_obj.route_ending_time), '%H:%M:%S')
                        duration_minutes = (end_time - start_time).seconds // 60
                        result["route_duration_minutes"] = duration_minutes
                        result["route_duration_formatted"] = f"{duration_minutes // 60}h {duration_minutes % 60}m"
                    except:
                        result["route_duration_minutes"] = None
                        result["route_duration_formatted"] = "Unknown"
            else:
                result["route_duration_minutes"] = None
                result["route_duration_formatted"] = "In progress"

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=result,
                    msg='Route details fetched successfully'),
                status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No routes available'), status.HTTP_400_BAD_REQUEST)


class DownloadRouteHistory(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = shield_serialziers.ShieldJoiningRequestSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        shield_id = request.query_params.get('shield_id')
        starting_date = request.query_params.get('starting_date')
        ending_date = request.query_params.get('ending_date')
        member_id = request.user.id
        shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
        if shield:
            member_obj = shield.members.filter(id=member_id)
            if member_obj:
                routes = shield_models.Route.objects.filter(member=member_obj[0]).filter(shield_id=shield_id).filter(
                    route_date__gte=starting_date).filter(route_date__lte=ending_date).order_by('-id')
                routes_serializer = shield_serialziers.MemberRouteSerializer(routes, many=True)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                   data=routes_serializer.data,
                                                   msg='All routes of this member in this shield'), status.HTTP_200_OK)
            else:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                                   msg='El escudo actual fue eliminado'),
                    status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,

                                               msg='No Shield of this ID found'), status.HTTP_400_BAD_REQUEST)
