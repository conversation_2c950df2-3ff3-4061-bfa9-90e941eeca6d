from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    # path('api/account/', include('Account.urls')),
    path('api/alert/', include('AdminSide.Alert.urls')),
    path('api/admin/', include('AdminSide.AdminAPi.urls')),
    path('api/Membership/', include('AdminSide.Membership.urls')),
    path('api/faq/', include('AdminSide.Faq.urls')),
    path('api/about/', include('AdminSide.About.urls')),
    path('api/ticket/', include('AdminSide.Ticket.urls')),
    path('api/dashboard/', include('AdminSide.ControlPanelDashboard.urls')),
    path('api/shield/', include('AdminSide.Shield.urls')),
    path('api/company/', include('AdminSide.CompanyDashboard.urls')),
    path('api/roles/', include('AdminSide.Roles.urls')),
]
