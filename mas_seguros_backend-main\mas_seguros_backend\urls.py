"""mas_seguros_backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
                  path('admin/', admin.site.urls),
                  path('api/account/', include('Account.urls')),
                  path('api/alert/', include('Alert.urls')),
                  # path('api/admin/', include('AdminAPi.urls')),
                  path('api/Membership/', include('Membership.urls')),
                  path('api/faq/', include('Faq.urls')),
                  path('api/about/', include('About.urls')),
                  path('api/ticket/', include('Ticket.urls')),
                  path('api/dashboard/', include('AdminSide.ControlPanelDashboard.urls')),
                  path('api/shield/', include('Shield.urls')),
                  # path('api/company/', include('CompanyDashboard.urls')),
                  path('adminside/', include('AdminSide.urls')),
                  path('api/sos/', include('Sos.urls')),
              ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

