from datetime import datetime

from rest_framework import serializers
from django.contrib.auth.models import User
from Account import models as account_models
from Membership import models as membership_models
from Shield import models as shield_models
from Alert import models as alert_models
from Ticket import models as ticket_models


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializerForDashboard(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role',
                  'lat', 'long', 'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend',
                  'created_at', 'updated_at', 'image_url']


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card',
                  'verification_code', 'user_type']


class ShieldsSerializerForDashboard(serializers.ModelSerializer):
    # members = UserProfileSerializerForDashboard(many=True)
    admin = UserProfileSerializerForDashboard()
    shield_type = serializers.CharField(required=False)
    admin = serializers.CharField(required=False)

    class Meta:
        model = shield_models.ShieldModel
        fields = ['shield_name', 'logo_url', 'shield_code', 'admin', 'shield_type', 'members_count', 'created_at',
                  'condition', 'id']


class PointOfInterestSerializer(serializers.ModelSerializer):
    # members = UserProfileSerializerForDashboard(many=True)

    # admin = UserProfileSerializerForDashboard()

    class Meta:
        model = shield_models.PointsOfInterest
        fields = ['id', 'poi_address', 'poi_lat', 'poi_long', 'poi_tag_name']


class ShieldMemberLocationSerializer(serializers.ModelSerializer):
    # members = UserProfileSerializerForDashboard(many=True)
    # admin = UserProfileSerializerForDashboard()

    class Meta:
        model = shield_models.Location
        fields = ['location', 'created_at', 'lat_long']


class MemberLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = shield_models.Location
        fields = "__all__"


class MemberRouteSerializer(serializers.ModelSerializer):
    starting_poi = serializers.SerializerMethodField()
    ending_poi = serializers.SerializerMethodField()

    def get_starting_poi(self, obj):
        poi = getattr(obj, 'starting_poi', None)
        if poi is not None:
            try:
                return PointOfInterestSerializer(poi).data
            except Exception:
                return None
        return None

    def get_ending_poi(self, obj):
        # If route is completed, use the saved ending_poi
        if getattr(obj, 'route_completed', False):
            poi = getattr(obj, 'ending_poi', None)
            if poi is not None:
                try:
                    return PointOfInterestSerializer(poi).data
                except Exception:
                    return None
            return None
        # If route is not completed, use the latest Location's point_of_interest for this route
        from Shield import models as shield_models
        latest_location = shield_models.Location.objects.filter(route=obj).order_by('-created_at').first()
        if latest_location:
            if latest_location.point_of_interest:
                try:
                    return PointOfInterestSerializer(latest_location.point_of_interest).data
                except Exception:
                    return None
            # If not at a POI, return coordinates and label as 'En Route', include timestamp for clarity
            return {
                'id': None,
                'poi_address': latest_location.location,
                'poi_lat': latest_location.lat,
                'poi_long': latest_location.long,
                'poi_tag_name': 'En Route',
                'timestamp': latest_location.created_at.strftime('%Y-%m-%dT%H:%M:%SZ') if latest_location.created_at else None
            }
        # If no location, return None (user has not moved or no data)
        return None

    class Meta:
        model = shield_models.Route
        fields = "__all__"


class ShieldMemberRouteSerializer(serializers.ModelSerializer):
    class Meta:
        model = shield_models.Route
        fields = "__all__"


class AlertCategorySerialzier(serializers.ModelSerializer):
    class Meta:
        model = alert_models.AlertCategories
        fields = "__all__"


class UserLocationSerialzier(serializers.ModelSerializer):
    class Meta:
        model = shield_models.Location
        fields = "__all__"


class ShieldAlertSerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializerForDashboard()
    category = AlertCategorySerialzier()

    class Meta:
        model = alert_models.AlertModel
        fields = "__all__"


class ShieldBiometricsSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    def get_image_url(self, record):
        record: shield_models.Biometric
        return record.image_url

    class Meta:
        model = shield_models.Biometric
        fields = "__all__"


class WalkieTalkieSerializer(serializers.ModelSerializer):
    class Meta:
        model = shield_models.WalkieTalkie
        fields = "__all__"


class ShieldMemberLocationSerializer(serializers.Serializer):
    location = serializers.CharField(max_length=255, required=True)
    lat = serializers.CharField(max_length=40, required=True)
    long = serializers.CharField(max_length=40, required=True)
    speed = serializers.CharField(max_length=40, required=False)
    phone_battery = serializers.CharField(max_length=40, required=False)


class GetShieldIdSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    # poi_id = serializers.IntegerField(required=True)


class GetRouteDetailSerializer(serializers.Serializer):
    route_id = serializers.IntegerField(required=True)


class MemberAudioStatusSerializer(serializers.Serializer):
    status = serializers.BooleanField(required=True)
    shield_id = serializers.IntegerField(required=True)


class ChangeShieldNameSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    name = serializers.CharField(max_length=50, required=True)


class GetMemberIDSerializer(serializers.Serializer):
    # shield_id = serializers.IntegerField(required=True)
    member_id = serializers.IntegerField(required=True)
    month = serializers.CharField(required=False)
    year = serializers.CharField(required=False)


class GetShieldIdAndMemberIDSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    member_id = serializers.IntegerField(required=False)


class GetMonthlySerializer(serializers.Serializer):
    month = serializers.CharField(required=True)


class sheildMembershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = membership_models.MembershipModel
        fields = "__all__"


class getMemberIdSerializer(serializers.Serializer):
    member_id = serializers.IntegerField(required=False)


class CreateShieldSerializer(serializers.Serializer):
    users_id = serializers.ListField(required=False)
    shield_name = serializers.CharField(max_length=50, required=True)
    tag_name = serializers.CharField(max_length=50, required=True)
    locations = serializers.DictField(required=True)


class PointOfInterestSerializerInShield(serializers.ModelSerializer):
    class Meta:
        model = shield_models.PointsOfInterest
        fields = "__all__"


class AddPointOfInterestShieldSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    # poi_id = serializers.IntegerField(required=True)
    point_of_interest_location = serializers.CharField(max_length=200, required=True)
    point_of_interest_lat = serializers.CharField(max_length=50, required=True)
    point_of_interest_long = serializers.CharField(max_length=50, required=True)
    point_of_interest_tag = serializers.CharField(max_length=50, required=True)


class EditPointOfInterestShieldSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    poi_id = serializers.IntegerField(required=True)
    point_of_interest_location = serializers.CharField(max_length=200, required=True)
    point_of_interest_lat = serializers.CharField(max_length=50, required=True)
    point_of_interest_long = serializers.CharField(max_length=50, required=True)
    point_of_interest_tag = serializers.CharField(max_length=50, required=True)


class ShieldsSerializerInCreateShield(serializers.ModelSerializer):
    members = UserProfileSerializerForDashboard(many=True, read_only=True)
    admin = UserProfileSerializerForDashboard(many=True, read_only=True)
    # Replace the locations field with a SerializerMethodField to handle it properly
    locations = serializers.SerializerMethodField()
    participent_count = serializers.SerializerMethodField()
    logo_url = serializers.SerializerMethodField()
    walkie_talkie_status = serializers.SerializerMethodField()
    # Add fields that might need to be null instead of empty strings
    shield_type = serializers.SerializerMethodField()
    shield_code = serializers.SerializerMethodField()
    shield_name = serializers.SerializerMethodField()
    shield_joining_code = serializers.SerializerMethodField()

    def get_walkie_talkie_status(self, record):
        try:
            if 'request' not in self.context:
                return None

            user = self.context['request'].user
            if not hasattr(user, 'userprofile') or user.userprofile is None:
                return None

            status = shield_models.WalkieTalkie.objects.filter(shield=record, member=user.userprofile).last()
            if status:
                return status.listen_audio
            return None
        except Exception as e:
            print(f"Error in get_walkie_talkie_status: {str(e)}")
            return None

    def get_logo_url(self, record):
        try:
            return record.logo_url
        except Exception as e:
            print(f"Error in get_logo_url: {str(e)}")
            return None

    def get_participent_count(self, record):
        try:
            record: shield_models.ShieldModel
            return record.members.all().count()
        except Exception as e:
            print(f"Error in get_participent_count: {str(e)}")
            return 0

    def get_locations(self, record):
        try:
            # Get the first location or return an empty dict if none exists
            # This ensures the mobile app receives a Map/Dictionary instead of a List
            location = record.locations.first()
            if location:
                # Return a single location as a dictionary
                return {
                    'id': location.id,
                    'poi_address': location.poi_address,
                    'poi_tag_name': location.poi_tag_name,
                    'poi_lat': location.poi_lat,
                    'poi_long': location.poi_long
                }
            return {}  # Return empty dict if no locations
        except Exception as e:
            print(f"Error in get_locations: {str(e)}")
            return {}

    def get_shield_type(self, record):
        try:
            return record.shield_type if record.shield_type else None
        except Exception as e:
            print(f"Error in get_shield_type: {str(e)}")
            return None

    def get_shield_code(self, record):
        try:
            return record.shield_code if record.shield_code else None
        except Exception as e:
            print(f"Error in get_shield_code: {str(e)}")
            return None

    def get_shield_name(self, record):
        try:
            return record.shield_name if record.shield_name else None
        except Exception as e:
            print(f"Error in get_shield_name: {str(e)}")
            return None

    def get_shield_joining_code(self, record):
        try:
            return record.shield_joining_code if record.shield_joining_code else None
        except Exception as e:
            print(f"Error in get_shield_joining_code: {str(e)}")
            return None

    class Meta:
        model = shield_models.ShieldModel
        fields = [
            'id', 'members', 'admin', 'shield_super_admin',
            'shield_code', 'shield_joining_code', 'shield_type',
            'shield_name', 'locations', 'logo', 'condition',
            'suspend', 'created_at', 'updated_at', 'logo_url',
            'participent_count', 'walkie_talkie_status'
        ]


class GetUserIDSerializer(serializers.Serializer):
    user_id = serializers.IntegerField(required=True)


class AssignAdminShieldSerializer(serializers.Serializer):
    users_id = serializers.ListField(required=True)
    shield_id = serializers.CharField(max_length=10, required=True)
    single_user = serializers.IntegerField(required=False)


class ShieldAdminSerializer(serializers.ModelSerializer):
    admin = UserProfileSerializerForDashboard(many=True)

    class Meta:
        model = shield_models.ShieldModel
        fields = ['admin', ]


class ShieldImageChangeSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    image = serializers.ImageField(required=True)


class AddMemberToShieldSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    users_id = serializers.ListField(required=True)


class RemoveMemberToShieldSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    user_id = serializers.CharField(max_length=10, required=True)


class GetPointOfInterestIdSerializer(serializers.Serializer):
    id = serializers.CharField(max_length=20, required=True)
    poi_id = serializers.CharField(max_length=20, required=True)


class HierarchySerializerShield(serializers.ModelSerializer):
    member = UserProfileSerializerForDashboard()
    # shield = ShieldsSerializerInCreateShield()
    admin = serializers.SerializerMethodField()
    shield_id = serializers.SerializerMethodField()

    def get_shield_id(self, record):
        record: shield_models.Hierarchie
        shield_id = record.shield.id
        return shield_id

    def get_admin(self, record):
        record: shield_models.Hierarchie
        shield = shield_models.ShieldModel.objects.filter(id=record.shield.id, admin=record.member)
        if shield:
            return True
        return False

    class Meta:
        model = shield_models.Hierarchie
        fields = ['member', 'hierarchy', 'admin', 'shield_id']


class GetShieldJoiningCodeSerializer(serializers.Serializer):
    shield_joining_code = serializers.CharField(max_length=4, required=True)


class GetShieldIdAndMemberIDHierarchySerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    member_id = serializers.IntegerField(required=True)
    hierarchy_name = serializers.CharField(max_length=20, required=True)


class BiometricUserShieldSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    user_pic = serializers.ImageField(required=True)
    lat = serializers.CharField(max_length=40, required=True)
    long = serializers.CharField(max_length=40, required=True)
    address = serializers.CharField(max_length=300, required=True)
    type = serializers.ChoiceField(choices=[('ENTRADA', 'ENTRADA'), ('SALIDA', 'SALIDA')], required=False, default='SALIDA')


class BiometricSerializer(serializers.ModelSerializer):
    class Meta:
        model = shield_models.Biometric
        fields = "__all__"


class ShieldJoiningRequestSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)


class ShieldUserRouteProfileSerializer(serializers.Serializer):
    shield_id = serializers.CharField(max_length=10, required=True)
    user_id = serializers.CharField(max_length=10, required=True)


class ShieldUpdateJoiningStatusSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    requester_id = serializers.IntegerField(required=True)
    status = serializers.CharField(max_length=10, required=True)


class ShieldBiometricsReportSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)
    start_date = serializers.CharField(max_length=10, required=True)
    end_date = serializers.CharField(max_length=10, required=True)
    member_ids = serializers.ListField(required=True)
    report_type = serializers.CharField(required=True)


class RequestShieldSerialzier(serializers.ModelSerializer):
    class Meta:
        model = shield_models.ShieldJoinRequest
        fields = "__all__"
        # depth = 1


class RequestShieldSerialzierForOneTimeAPi(serializers.ModelSerializer):
    class Meta:
        model = shield_models.ShieldJoinRequest
        fields = "__all__"


class UserProfileRouteSerializer(serializers.Serializer):
    last_poi = serializers.CharField(max_length=255, required=False, allow_null=True, allow_blank=True)
    last_poi_time = serializers.DateTimeField(required=False, allow_null=True)
    current_location = serializers.CharField(max_length=500, required=False, allow_null=True, allow_blank=True)
    current_location_since = serializers.DateTimeField(required=False, allow_null=True)
    on_route = serializers.BooleanField(required=False, default=False)
    on_route_since = serializers.CharField(max_length=100, required=False, allow_null=True, allow_blank=True)
    max_speed = serializers.CharField(max_length=40, required=False, allow_null=True, allow_blank=True)
    min_battery = serializers.CharField(max_length=40, required=False, allow_null=True, allow_blank=True)
    member = UserProfileSerializerForDashboard(required=False)
    current_status = serializers.CharField(max_length=100, required=False, allow_null=True, allow_blank=True)
    current_speed = serializers.CharField(max_length=40, required=False, allow_null=True, allow_blank=True)
    current_address = serializers.CharField(max_length=500, required=False, allow_null=True, allow_blank=True)
    battery_percentage = serializers.CharField(max_length=40, required=False, allow_null=True, allow_blank=True)
