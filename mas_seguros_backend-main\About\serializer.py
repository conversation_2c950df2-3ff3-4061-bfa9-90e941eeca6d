from rest_framework.serializers import ModelSerializer
from About import models as about_models
from rest_framework import serializers

class DataPoliciesSerializer(serializers.ModelSerializer):
    class Meta:
        model = about_models.DataPolicie
        fields = ('description','title')


class TermsAndConditionSerializer(serializers.ModelSerializer):
    class Meta:
        model = about_models.TermsAndCondition
        fields = ('description','title')