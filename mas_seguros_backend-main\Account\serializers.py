from time import strftime
from datetime import datetime
from rest_framework.serializers import ModelSerializer
from Account import models as account_models
from rest_framework import serializers
from django.contrib.auth.models import User


class RegistrationSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)
    full_name = serializers.CharField(required=True)
    email = serializers.CharField(required=True)
    identification_card = serializers.CharField(required=False)
    birth_date = serializers.DateField(format=strftime("%d/%m/%Y"), input_formats=['%d/%m/%Y', ], required=False)
    phone = serializers.CharField(required=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account!")
        return lower_email
    
    def validate_phone(self, value):
        if account_models.UserProfile.objects.filter(phone__exact=value).exists():
            raise serializers.ValidationError("Phone number already registered to other account!")
        return value
        
    def validate_password(self, value):
        if len(value) < 6:
            raise serializers.ValidationError("Password must be at least 6 characters long.")
        return value

    class Meta:
        fields = [
            "full_name",
            "identification_card",
            "birth_date",
            "email",
            "password",
            "phone",
        ]


class LoginSerializer(serializers.Serializer):
    phone = serializers.CharField(required=True)
    password = serializers.CharField(required=True, style={'input_type': 'password'})

    def validate_phone(self, value):
        if not account_models.UserProfile.objects.filter(phone=value).exists():
            raise serializers.ValidationError("Este número no está registrado.")
        return value
        
    def validate_password(self, value):
        if len(value) < 6:
            raise serializers.ValidationError("La contraseña debe tener al menos 6 caracteres.")
        return value
        
    def validate(self, data):
        phone = data.get('phone')
        password = data.get('password')
        
        # If phone exists and password is valid length, check if credentials are correct
        if phone and password and len(password) >= 6:
            try:
                user_profile = account_models.UserProfile.objects.get(phone=phone)
                if not user_profile.user.check_password(password):
                    raise serializers.ValidationError({"non_field_errors": ["La contraseña que ha ingresado es incorrecta."]})
            except account_models.UserProfile.DoesNotExist:
                # This should be caught by validate_phone, but just in case
                pass
                
        return data


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = account_models.User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    birth_date = serializers.SerializerMethodField()

    # image_url = serializers.SerializerMethodField()
    def get_birth_date(self, obj):
        try:
            old_date = datetime.strptime(str(obj.birth_date), '%Y-%m-%d')
            return old_date
        except:
            return None

    class Meta:
        model = account_models.UserProfile
        fields = ['user',
                  'firebase_uid', 'phone', 'full_name', 'identification_card', 'birth_date', 'role', 'lat', 'long',
                  'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend', 'created_at',
                  'updated_at', 'image_url']


class UserProfileSerializerForLogin(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = ['user',
                  'firebase_uid', 'phone', 'full_name', 'identification_card', 'birth_date', 'role', 'lat', 'long',
                  'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend', 'created_at',
                  'updated_at', 'image_url']


class SendVerificationCode(serializers.Serializer):
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if not User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("El e-mail ingresado no está registrado.")
        return lower_email


class PhoneVerifySerializer(serializers.Serializer):
    code = serializers.IntegerField(required=True)
    email = serializers.EmailField(required=True)


class ConfirmCurrentPasswordSerializer(serializers.Serializer):
    current_password = serializers.CharField(max_length=20, required=True)


class PasswordCreationSerializer(serializers.Serializer):
    new_password = serializers.CharField(max_length=20, required=True)


class SetNewPasswordSerializer(serializers.Serializer):
    new_password = serializers.CharField(max_length=20, required=True)
    email = serializers.EmailField(required=True)


# class EmailVerify(serializers.Serializer):
#     code = serializers.IntegerField(required=True)
#     email = serializers.EmailField(required=True)
#


class EnableLocationSerializer(serializers.Serializer):
    enable_location = serializers.BooleanField(required=True)


class RealTimeLocationSerializer(serializers.Serializer):
    lat = serializers.CharField(max_length=40, required=True)
    long = serializers.CharField(max_length=40, required=True)


class EditProfileSerializer(serializers.Serializer):
    full_name = serializers.CharField(required=True)
    email = serializers.CharField(required=True)
    identification_card = serializers.CharField(required=True)
    # birth_date = serializers.DateField(required=True)
    birth_date = serializers.DateField(format=strftime("%d/%m/%Y"), input_formats=['%d/%m/%Y', ], required=True)
    image = serializers.ImageField(required=False)

    # def validate(self, data):
    #     lower_email = data['email'].lower()
    #     if User.objects.filter(email__iexact=lower_email).exists():
    #         raise serializers.ValidationError("Email already registered to other account! ")
    #     return lower_email

    # class Meta:
    #     fields = [
    #         "full_name",
    #         "identification_card",
    #         "birth_date",
    #         "email",
    #         'image'
    #     ]


class ChangePhoneSerializer(serializers.Serializer):
    phone = serializers.IntegerField(required=True)


class FcmTokenSerializer(serializers.Serializer):
    device_id = serializers.CharField(max_length=1000, required=True)

# class ChangeProfileImageSerializer(serializers.Serializer):
#     image = serializers.ImageField(required=True)




