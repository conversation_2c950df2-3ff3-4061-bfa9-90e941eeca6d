import Table from "@/components/Table";
import InputGroup from "@/components/utility/InputGroup";
import React, { useState } from "react";
import useAxios from "@/hooks/useAxios";
import { useQuery } from "react-query";
import { useSelector } from "react-redux";

const PointHistoryCard = ({ selectedPointId }) => {
  const [tempDate, setTempDate] = useState('');
  const [date, setDate] = useState('');
  const { axios } = useAxios();

  // Get authentication state
  const token = useSelector((state) => state.user.token);

  // Fetch visit history data
  const fetchVisitHistory = async () => {
    if (!selectedPointId) {
      return { data: [], success: true };
    }

    const params = { poi_id: selectedPointId };
    if (date) {
      params.date = date;
    }

    const response = await axios.get(`adminside/api/shield/point-of-interest-visit-history/`, {
      params
    });

    // Handle different response formats from backend
    if (response.data) {
      if (typeof response.data.success !== 'undefined') {
        return response.data;
      }
      if (Array.isArray(response.data)) {
        return { data: response.data, success: true };
      }
      return { data: response.data, success: true };
    }

    return { data: [], success: false, msg: 'No data received from server' };
  };

  const {
    data: apiResponse,
    isLoading,
    error: queryError
  } = useQuery(
    ['point-visit-history', selectedPointId, date],
    fetchVisitHistory,
    {
      enabled: !!selectedPointId && !!token,
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false;
        }
        return failureCount < 2;
      }
    }
  );

  // Process the API response
  const visitHistory = apiResponse?.success ? (apiResponse.data || []) : [];

  // Error handling
  let error = null;
  if (!token && selectedPointId) {
    error = 'Por favor, inicia sesión para ver el historial de visitas';
  } else if (queryError) {
    if (queryError.response?.status === 401) {
      error = 'Sesión expirada. Por favor, inicia sesión nuevamente';
    } else if (queryError.response?.status === 403) {
      error = 'No tienes permisos para acceder a esta información';
    } else if (queryError.response?.status === 404) {
      error = 'Punto de interés no encontrado';
    } else if (queryError.response?.data?.msg) {
      error = queryError.response.data.msg;
    } else if (queryError.response?.data?.detail) {
      error = queryError.response.data.detail;
    } else {
      error = 'Error al cargar el historial de visitas';
    }
  } else if (apiResponse && !apiResponse.success) {
    error = apiResponse.msg || 'Error al obtener los datos del servidor';
  }

  // Handle date search
  const handleDateSearch = () => {
    setDate(tempDate);
  };

  const handleClearDate = () => {
    setDate('');
    setTempDate('');
  };

  return (
    <div className="flex h-[800px] flex-col space-y-5 bg-white p-5">
      <h2 className="text-lg font-bold">Historial de Punto</h2>

      <div className="flex items-center justify-end gap-2 text-sm">
        <span className="text-gray-600">Buscar</span>
        <div>
          <InputGroup>
            <InputGroup.Input
              value={tempDate}
              onChange={e => setTempDate(e.target.value)}
              type="date"
              className="bg-accent"
              max={new Date().toISOString().split('T')[0]} // Prevent future dates
            />
          </InputGroup>
        </div>
        <button
          onClick={handleDateSearch}
          type="button"
          disabled={!tempDate}
          className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Buscar
        </button>
        {!!date && (
          <button
            type="button"
            onClick={handleClearDate}
            className="self-stretch rounded bg-gray-500 px-3 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-gray-600 transition-colors"
          >
            Limpiar
          </button>
        )}
      </div>

      <Table
        wrapperClassName="px-3 bg-accent flex-grow overflow-auto"
        className="relative"
      >
        <Table.Thead className="sticky top-0 bg-accent">
          <Table.Tr>
            <Table.Th className="">Miembro</Table.Th>
            <Table.Th className="">Fecha</Table.Th>
            <Table.Th className="">Horario</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {isLoading ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8">
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  <span>Cargando historial de visitas...</span>
                </div>
              </Table.Td>
            </Table.Tr>
          ) : error ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8 text-red-600">
                <div className="flex flex-col items-center space-y-2">
                  <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{error}</span>
                </div>
              </Table.Td>
            </Table.Tr>
          ) : !selectedPointId ? (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8 text-gray-500">
                <div className="flex flex-col items-center space-y-2">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span>Selecciona un punto de interés para ver su historial de visitas</span>
                </div>
              </Table.Td>
            </Table.Tr>
          ) : visitHistory.length > 0 ? (
            visitHistory.map((visit, index) => (
              <Table.Tr key={visit.id || index} className="hover:bg-gray-50">
                <Table.Td className="capitalize font-medium">{visit.member_name || 'N/A'}</Table.Td>
                <Table.Td className="text-gray-600">{visit.date || 'N/A'}</Table.Td>
                <Table.Td className="text-gray-600">{visit.time || 'N/A'}</Table.Td>
              </Table.Tr>
            ))
          ) : (
            <Table.Tr>
              <Table.Td colSpan={3} className="text-center py-8 text-gray-500">
                <div className="flex flex-col items-center space-y-2">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span>
                    {date ? "No hay visitas registradas para este punto en la fecha seleccionada." : "No hay visitas registradas para este punto."}
                  </span>
                </div>
              </Table.Td>
            </Table.Tr>
          )}
        </Table.Tbody>
      </Table>
    </div >
  );
};

export default PointHistoryCard;
