import React, { useEffect, useState } from "react";
import Modal from "../utility/Modal";
import InputGroup from "../utility/InputGroup";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";

const AdminFormModal = ({
  open,
  setOpen,
  data,
  setData,
  submit,
  close,
  mode = "create",
  wasRecentlySuccessful = false,
  currentAdmin = null,
  isSubmitting = false,
}) => {
  const isCreateMode = !!(mode == "create");
  const isEditMode = !!(mode == "edit");

  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm();
  const [fullAccess, setFullAccess] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  // Watch the full_access checkbox
  const watchFullAccess = watch("full_access", false);

  // Handle image upload
  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type - prefer PNG as specified in user story
      if (!file.type.startsWith('image/')) {
        toast.error('Por favor seleccione un archivo de imagen válido');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('El archivo es demasiado grande. Máximo 5MB');
        return;
      }

      // Validate image dimensions - allow reasonable sizes, recommend 750x750px
      const img = new Image();
      img.onload = function() {
        // Check minimum dimensions (at least 200x200px)
        if (this.width < 200 || this.height < 200) {
          toast.error('La imagen debe ser de al menos 200x200 píxeles');
          return;
        }

        // Check maximum dimensions (max 2000x2000px to prevent huge files)
        if (this.width > 2000 || this.height > 2000) {
          toast.error('La imagen no debe exceder 2000x2000 píxeles');
          return;
        }

        // Recommend square aspect ratio but don't enforce it strictly
        const aspectRatio = this.width / this.height;
        if (aspectRatio < 0.8 || aspectRatio > 1.25) {
          toast.warning('Se recomienda usar una imagen cuadrada (750x750px) para mejor visualización');
        }

        setSelectedImage(file);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      };

      img.onerror = function() {
        toast.error('Error al cargar la imagen');
      };

      img.src = URL.createObjectURL(file);
    }
  };

  useEffect(() => {
    if (wasRecentlySuccessful) {
      reset();
      setFullAccess(false);
      setSelectedImage(null);
      // Don't reset imagePreview immediately in edit mode - let the updated data handle it
      if (!isEditMode) {
        setImagePreview(null);
      }
    }
  }, [wasRecentlySuccessful, isEditMode]);

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      reset();
      setSelectedImage(null);
      setImagePreview(null);
    }
  }, [open, reset]);

  // Handle full access toggle
  useEffect(() => {
    if (watchFullAccess) {
      setValue("users_access", true);
      setValue("shields_access", true);
      setValue("alerts_sos_access", true);
      setValue("payment_history_access", true);
      setValue("support_access", true);
      setValue("roles_access", true);
    }
  }, [watchFullAccess, setValue]);

  // Populate form when in edit mode
  useEffect(() => {
    if (isEditMode && currentAdmin && open) {
      const nameParts = currentAdmin.full_name?.split(' ') || ['', ''];
      setValue("first_name", nameParts[0] || '');
      setValue("last_name", nameParts.slice(1).join(' ') || '');
      setValue("email", currentAdmin.user?.email || '');
      setValue("phone", currentAdmin.phone || '');
      setValue("identification_card", currentAdmin.identification_card || '');
      // Pre-fill birth_date in YYYY-MM-DD format for date input
      if (currentAdmin.birth_date) {
        try {
          let dateStr = currentAdmin.birth_date;
          let yyyy, mm, dd;
          if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
            // DD/MM/YYYY
            [dd, mm, yyyy] = dateStr.split('/');
          } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            // YYYY-MM-DD
            [yyyy, mm, dd] = dateStr.split('-');
          } else {
            // Try to parse as Date
            const d = new Date(dateStr);
            if (!isNaN(d.getTime())) {
              yyyy = d.getFullYear();
              mm = String(d.getMonth() + 1).padStart(2, '0');
              dd = String(d.getDate()).padStart(2, '0');
            }
          }
          if (yyyy && mm && dd) {
            setValue("birth_date", `${yyyy}-${mm}-${dd}`);
          }
        } catch (e) {
          // fallback: do not set
        }
      }

      // Set permissions if available
      if (currentAdmin.admin_permissions) {
        const permissions = currentAdmin.admin_permissions;
        setValue("full_access", permissions.full_access || false);
        setValue("users_access", permissions.users_access || false);
        setValue("shields_access", permissions.shields_access || false);
        setValue("alerts_sos_access", permissions.alerts_sos_access || false);
        setValue("payment_history_access", permissions.payment_history_access || false);
        setValue("support_access", permissions.support_access || false);
        setValue("roles_access", permissions.roles_access || false);
      }

      // Clear image preview when admin data is updated (after successful save)
      if (wasRecentlySuccessful) {
        setImagePreview(null);
        setSelectedImage(null);
      }
    }
  }, [isEditMode, currentAdmin, open, setValue, wasRecentlySuccessful]);

  return (
    <Modal
      open={open}
      close={close}
      className="w-full max-w-screen-md overflow-hidden bg-white shadow-xl"
    >
      <Modal.Wrapper as="form" onSubmit={handleSubmit((data) => {
        // Ensure identification_card is always a string
        if (typeof data.identification_card !== 'string') {
          data.identification_card = data.identification_card ? String(data.identification_card) : '';
        }
        // Prevent undefined/null
        if (!data.identification_card) {
          data.identification_card = '';
        }
        submit(data, selectedImage);
      })}>
        {/* Header */}
        <Modal.Header className="bg-accent">
          <h2 className="text-lg font-medium">
            {isCreateMode && "Crear Administrador"}
            {isEditMode && "Editar información"}
          </h2>
          <Modal.XBtn onClick={close} />
        </Modal.Header>

        {/* Body */}
        <Modal.Body>
          <div className="flex flex-col gap-5 text-sm sm:flex-row">
            <div className="flex-shrink-0">
              <h4 className="font-semibold">Foto de perfil</h4>
              <img
                src={imagePreview || currentAdmin?.image_url || "/assets/img/default-profile-pic-1.jpg"}
                className="mt-3 block aspect-square w-40 rounded-lg border object-cover"
                alt="User"
                onError={(e) => {
                  e.target.src = "/assets/img/default-profile-pic-1.jpg";
                }}
              />
              <p className="mt-2 text-secondary">
                Subir una foto de perfil <br />
                Recomendado: 750x750px (cuadrada) <br />
                Mínimo: 200x200px, Máximo: 2000x2000px
              </p>
              <div className="mt-3">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                  id="profile-image-upload"
                />
                <label
                  htmlFor="profile-image-upload"
                  className="cursor-pointer font-semibold underline text-primary hover:text-primary-dark"
                >
                  Cargar imagen
                </label>
              </div>
              {selectedImage && (
                <p className="mt-1 text-sm text-green-600 break-all whitespace-pre-line">
                  Archivo: {selectedImage.name}
                </p>
              )}
            </div>
            <div className="flex-grow">
              <div className="grid grid-cols-2 gap-5">
                <div>
                  <InputGroup.Label>Nombre *</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      {...register("first_name", {
                        required: "El nombre es requerido",
                        minLength: {
                          value: 2,
                          message: "El nombre debe tener al menos 2 caracteres"
                        }
                      })}
                    />
                  </InputGroup>
                  {errors.first_name && (
                    <p className="mt-1 text-sm text-red-600">{errors.first_name.message}</p>
                  )}
                </div>
                <div>
                  <InputGroup.Label>Apellido *</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      {...register("last_name", {
                        required: "El apellido es requerido",
                        minLength: {
                          value: 2,
                          message: "El apellido debe tener al menos 2 caracteres"
                        }
                      })}
                    />
                  </InputGroup>
                  {errors.last_name && (
                    <p className="mt-1 text-sm text-red-600">{errors.last_name.message}</p>
                  )}
                </div>
                <div>
                  <InputGroup.Label>Correo *</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      {...register("email", {
                        required: "El correo electrónico es requerido",
                        pattern: {
                          value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: "El formato del correo electrónico es inválido"
                        }
                      })}
                      disabled={isEditMode}
                      className={isEditMode ? "bg-gray-100 cursor-not-allowed" : ""}
                      readOnly={isEditMode}
                    />
                  </InputGroup>
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                  {isEditMode && (
                    <p className="mt-1 text-xs text-gray-500">El correo no se puede modificar</p>
                  )}
                </div>
                {!isEditMode && (
                  <div>
                    <InputGroup.Label>Contraseña *</InputGroup.Label>
                    <InputGroup>
                      <InputGroup.Input
                        type="password"
                        {...register("password", {
                          required: "La contraseña es requerida",
                          minLength: {
                            value: 6,
                            message: "La contraseña debe tener al menos 6 caracteres"
                          }
                        })}
                      />
                    </InputGroup>
                    {errors.password && (
                      <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                    )}
                  </div>
                )}
                <div>
                  <InputGroup.Label>Teléfono *</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      type="tel"
                      {...register("phone", {
                        required: "El teléfono es requerido",
                        pattern: {
                          value: /^[\+]?[0-9\s\-\(\)]{7,20}$/,
                          message: "El formato del número de teléfono es inválido"
                        }
                      })}
                    />
                  </InputGroup>
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>
                <div className="col-span-2">
                  <InputGroup.Label>N°. de Identificación</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      {...register("identification_card")}
                    />
                  </InputGroup>
                </div>
                <div className="col-span-2">
                  <InputGroup.Label>Fecha de nacimiento</InputGroup.Label>
                  <InputGroup>
                    <InputGroup.Input
                      type="date"
                      {...register("birth_date")}
                    />
                  </InputGroup>
                </div>
              </div>

              {/* Permissions Section */}
              {(isCreateMode || isEditMode) && (
                <div className="mt-6">
                  <h4 className="font-semibold mb-3">Asignar Permisos de Acceso</h4>

                  {/* Full Access Option */}
                  <div className="mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        {...register("full_access")}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="ml-2 text-sm font-medium">Acceso completo a todas las secciones</span>
                    </label>
                  </div>

                  {/* Individual Permissions */}
                  {!watchFullAccess && (
                    <div className="grid grid-cols-2 gap-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("users_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Usuarios</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("shields_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Escudos</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("alerts_sos_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Alertas y SOS</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("payment_history_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Historial de Pagos</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("support_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Soporte</span>
                      </label>

                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          {...register("roles_access")}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                        <span className="ml-2 text-sm">Roles</span>
                      </label>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Modal.Body>

        {/* Footer */}
        <Modal.Footer className="bg-accent">
          <Modal.FooterBtn
            type="button"
            onClick={close}
            className="bg-white text-black"
            disabled={isSubmitting}
          >
            Cancelar
          </Modal.FooterBtn>
          <Modal.FooterBtn
            type="submit"
            className="bg-black text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Guardando...' : 'Guardar'}
          </Modal.FooterBtn>
        </Modal.Footer>
      </Modal.Wrapper>
    </Modal>
  );
};

export default AdminFormModal;
