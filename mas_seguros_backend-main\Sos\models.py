from django.db import models
from Account import models as account_models
from Shield import models as shield_models

# Create your models here.

Sos_SENT = 'Sos enviada'
HELP_SENT = 'Ayuda enviada'
Sos_SOLVED = 'Sos resuelta'

sos_status = [
    (Sos_SENT, Sos_SENT),
    (HELP_SENT, HELP_SENT),
    (Sos_SOLVED, Sos_SOLVED)
]


class Sos(models.Model):
    sender = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    status = models.CharField(max_length=100, choices=sos_status, null=True, blank=True, db_index=True)
    active = models.BooleanField(default=True, db_index=True)
    lat = models.CharField(max_length=40, null=True, blank=True)
    long = models.CharField(max_length=40, null=True, blank=True)
    location = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    shield = models.ForeignKey(shield_models.ShieldModel, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['sender', '-created_at']),
            models.Index(fields=['status', '-created_at']),
            models.Index(fields=['shield', '-created_at']),
            models.Index(fields=['sender', 'status', '-created_at']),
            models.Index(fields=['active', '-created_at']),
            models.Index(fields=['shield', 'active', '-created_at']),
        ]
        ordering = ['-created_at']

    class Meta:
        indexes = [
            models.Index(fields=['sender', '-created_at']),
            models.Index(fields=['shield', '-created_at']),
            models.Index(fields=['active', '-created_at']),
        ]


class SosEvidence(models.Model):
    evidence = models.FileField(null=True, blank=True, upload_to='sos_evidence')
    sos = models.ForeignKey(Sos, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    sender = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['sos', '-created_at']),
        ]


class SosModifyHistory(models.Model):
    sos = models.ForeignKey(Sos, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=50, choices=sos_status, null=True, blank=True)
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)


class SosComment(models.Model):
    sos = models.ForeignKey(Sos, on_delete=models.SET_NULL, null=True, blank=True, db_index=True)
    comment = models.TextField(null=True, blank=True)
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['sos', '-created_at']),
        ]

    def __str__(self):
        return self.sos.sender.user.get_full_name()
