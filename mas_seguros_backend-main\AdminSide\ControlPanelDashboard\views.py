import io
import os

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from Account import models as account_models
from . import serializers as dashboard_serialzier, utils as dashboard_utils
from Membership import models as membership_models
from Shield import models as shield_models, serializers as shield_serialziers
from Alert import models as alert_models
from AdminSide.AdminAPi import models as admin_model
from Ticket import models as ticket_models
from django.http import JsonResponse
import random
import string
import csv
from django.http import FileResponse
from reportlab.pdfgen import canvas
from django.core.files.storage import default_storage
from django.core.files.storage import default_storage
from django.http import HttpResponse
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from mas_seguros_backend import settings as backend_setting
from Sos import models as sos_models
from datetime import datetime
import openpyxl
from io import BytesIO
# from django.http import HttpResponse
from django.core.files.storage import FileSystemStorage


# Create your views here.

class ListUsers(APIView):
    # permission_classes = (IsAuthenticated,)

    def get(self, request):
        data = account_models.UserProfile.objects.filter(role=account_models.normal_user)
        user_profile_serializer = dashboard_serialzier.UserProfileSerializerForDashboard(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=user_profile_serializer.data,
                                           msg='All Users'), status.HTTP_200_OK)


class RegisteredUsers(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = account_models.UserProfile.objects.filter(created_at__month=month, created_at__year=year)
        user_profile_serializer = dashboard_serialzier.UserProfileSerializerForDashboard(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'count': user_profile_serializer.data.__len__(),
                                                 'total_users': user_profile_serializer.data.__len__(),
                                                 'individuals_users': user_profile_serializer.data.__len__(),
                                                 'companies': user_profile_serializer.data.__len__(),
                                                 'data': user_profile_serializer.data, },
                                           msg='All Users'), status.HTTP_200_OK)


class PurchasedMemberships(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = membership_models.MembershipModel.objects.filter(date__month=month, date__year=year)
        membership_serializer = dashboard_serialzier.MembershipSerializerForDashboard(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'count': membership_serializer.data.__len__(),
                                                 'level_1': membership_serializer.data.__len__(),
                                                 'level_2': membership_serializer.data.__len__(),
                                                 'level_3': membership_serializer.data.__len__(),
                                                 'level_4': membership_serializer.data.__len__(),
                                                 'data': membership_serializer.data, },
                                           msg='All Memberships'), status.HTTP_200_OK)


class ShieldsCreated(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = shield_models.ShieldModel.objects.filter(created_at__month=month, created_at__year=year)
        count_data = data.count()
        if data:
            shield_serializer = dashboard_serialzier.ShieldsSerializerForDashboard(data, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data={'individual_count': count_data,
                                                     'companies_count': count_data,
                                                     'data': shield_serializer.data},
                                               msg='All Shields'), status.HTTP_200_OK)
        # Return empty data with 200 status code instead of 400 error when no shields are found
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'individual_count': 0,
                                                 'companies_count': 0,
                                                 'data': []},
                                           msg='No Shields Found'), status.HTTP_200_OK)


class TicketsCreated(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = ticket_models.Ticket.objects.filter(created_at__month=month, created_at__year=year)
        resolved_data = ticket_models.Ticket.objects.filter(created_at__month=month, resolved=True).count()
        ticket_serializer = dashboard_serialzier.TicketSerializerForDashboard(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'support_tickets': ticket_serializer.data.__len__(),
                                                 'resolved_tickets': resolved_data,
                                                 'data': ticket_serializer.data, },
                                           msg='All Tickets'), status.HTTP_200_OK)


class AlertsGeneratedWithSOS(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = alert_models.AlertModel.objects.filter(created_at__month=month, created_at__year=year)
        data_sos = sos_models.Sos.objects.filter(created_at__month=month, created_at__year=year)
        alert_serializer = dashboard_serialzier.AlertSerializer(data, many=True)
        # sos_serializer = dashboard_serialzier.SosSerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'generated_alerts_count': alert_serializer.data.__len__(),
                                                 'sos_count': data_sos.count(),
                                                 'data': alert_serializer.data,
                                                 },
                                           msg='All Alerts and Sos'), status.HTTP_200_OK)


class AlertsGeneratedWithExpiry(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        data = alert_models.AlertModel.objects.filter(created_at__month=month, created_at__year=year)
        alert_serializer = dashboard_serialzier.AlertSerializer(data, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'generated_alerts_count': alert_serializer.data.__len__(),
                                                 'expiry_count': alert_serializer.data.__len__(),
                                                 'data': alert_serializer.data, },
                                           msg='All Alerts'), status.HTTP_200_OK)


class PromoCode(APIView):
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        month = request.query_params.get('month')
        year = request.query_params.get('year')
        total_data = admin_model.PromoCode.objects.filter(created_at__month=month, created_at__year=year).count()
        due_data = admin_model.PromoCode.objects.filter(created_at__month=month, created_at__year=year,
                                                        state=False).count()
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data={'total': total_data,
                                                 'due': due_data,
                                                 },
                                           msg='All Alerts'), status.HTTP_200_OK)


class UserRegisteredPerMonth(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        all_users = account_models.UserProfile.objects.all()
        data = dashboard_utils.get_data_per_month(all_users)

        # Format data for frontend charts
        formatted_data = []
        for i in range(12):  # Only use the first 12 months (exclude the duplicate month)
            formatted_data.append({
                'month': i + 1,  # Month number (1-12)
                'count': data[0][i]  # User count for that month
            })

        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=formatted_data,
                                           msg='User Registered Per Month'), status.HTTP_200_OK)


class SheildsCreatedPerMonth(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        all_shields = shield_models.ShieldModel.objects.all()
        data = dashboard_utils.get_data_per_month(all_shields)

        # Format data for frontend charts
        formatted_data = []
        for i in range(12):  # Only use the first 12 months (exclude the duplicate month)
            formatted_data.append({
                'month': i + 1,  # Month number (1-12)
                'count': data[0][i]  # Shield count for that month
            })

        # Print debug info
        print(f"ShieldsCreatedPerMonth: {formatted_data}")
        print(f"Total shields: {sum([item['count'] for item in formatted_data])}")

        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK,
                                           data=formatted_data,
                                           msg='Shields Created Per Month'), status.HTTP_200_OK)


# class UsersRegisteredPerMonthGraph(APIView):
#     permission_classes = (IsAuthenticated,)
#
#     def get(self, request):
#         all_shields = shield_models.ShieldModel.objects.all()
#         data = dashboard_utils.get_month_users(all_shields)
#         # alert_serializer = dashboard_serialzier.AlertSerializer(data, many=True)
#         return Response(
#             backend_utils.success_response(status_code=status.HTTP_200_OK,
#                                            data={'total_users': sum(data),
#                                                  'data': data, },
#                                            msg='User Registered Per Month'), status.HTTP_200_OK)


class UserLocations(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetUserIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        member_id = request.query_params.get('user_id')
        if member_id:
            locations = shield_models.Route.objects.filter(member__user_id=member_id)
            members_serializer = dashboard_serialzier.UserLocationSerializer(locations, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=members_serializer.data,
                                               msg='data'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No User of this ID found'), status.HTTP_400_BAD_REQUEST)


class UserShields(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetUserIDSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        id = request.query_params.get('user_id')
        user = account_models.UserProfile.objects.filter(user_id=id).last()
        if user:
            user: account_models.UserProfile
            shields = shield_models.ShieldModel.objects.filter(members=user)
            print("all shields===", shields)
            serializer = dashboard_serialzier.UserShieldsSerializer(shields, many=True)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               data=serializer.data,
                                               msg='All Shields of the user'), status.HTTP_200_OK)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No User Found'), status.HTTP_400_BAD_REQUEST)


class UserBiometricReportDownload(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.UserDownloadBiometricSerializer

    def get(self, request):
        serializer = self.serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        user_id = request.query_params.get('member_id')
        date = request.query_params.get('date', None)
        report_type = request.query_params.get('report_type', None)

        userprofile = account_models.UserProfile.objects.filter(user_id=user_id).last()
        if date:
            biometrics = shield_models.Biometric.objects.filter(userprofile=userprofile, created_at__date=date)
        else:
            biometrics = shield_models.Biometric.objects.filter(userprofile=userprofile)
        if biometrics:
            if report_type == 'csv':
                file_name = ''.join(random.choices(string.ascii_uppercase +
                                                   string.digits, k=7)) + '.csv'
                path = "/" + file_name
                with open(file_name, 'w', newline='') as file:
                    writer = csv.writer(file)
                    writer.writerow(
                        ["Shield_name", "Member_name", "Biometric_code", "Image", "lat", "long", "address", "Type"])
                    for biometric in biometrics:
                        writer.writerow(
                            [biometric.shield.shield_name, biometric.userprofile.name, biometric.biometric_code,
                             biometric.image_url, biometric.lat, biometric.long, biometric.address, biometric.type])
                file.close()
                latest_file = backend_setting.Base_url_path.format(url=path)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=latest_file,
                                                   msg='All Reports of Biometric'), status.HTTP_200_OK)
            if report_type == 'pdf':
                queryset = biometrics
                buffer = io.BytesIO()

                # Create the PDF object, using the buffer as its "file."
                doc = SimpleDocTemplate(buffer, pagesize=letter)

                # Set the style for the PDF file
                styles = getSampleStyleSheet()
                style_heading = styles["Heading1"]
                style_body = styles["Normal"]

                # Create a list to hold the data for the table
                data = []

                # Add the headers to the table
                headings = ['User', 'Biometric Code', 'Shield', 'Address', 'Type']
                data.append(headings)

                # Add the data for each object in the queryset
                for obj in queryset:
                    row = []
                    row.append(obj.userprofile.user.get_full_name())
                    row.append(obj.biometric_code)
                    row.append(obj.shield.shield_name)
                    row.append(obj.address)
                    row.append(obj.type)
                    data.append(row)

                # Create the table and set its style
                table = Table(data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), '#c8c8c8'),
                    ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
                    ('TEXTCOLOR', (0, 1), (-1, -1), '#000000'),
                    ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 12),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 10),
                ]))

                # Add the table to the PDF
                elements = []
                elements.append(Paragraph("Biometric Report", style_heading))
                elements.append(Spacer(1, 0.25 * inch))
                elements.append(table)
                doc.build(elements)
                file_name = 'biometric_reports/adminside/{}'.format(''.join(random.choices(string.ascii_uppercase +
                                                                                           string.digits,
                                                                                           k=7)) + '.pdf')
                default_storage.save(file_name, buffer)
                file_url = default_storage.url(file_name)

                routes_serializer = shield_serialziers.ShieldBiometricsSerializer(biometrics, many=True)
                routes_serializer_data = routes_serializer.data
                routes_serializer_data.append({'downloadable_file': backend_setting.Base_url_path.format(url=file_url)})
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, msg="all biometrics",
                                                   data=routes_serializer_data), status.HTTP_200_OK)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No report type matched'), status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='No Biometrics available'), status.HTTP_400_BAD_REQUEST)


class SuspendUsers(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.SuspendSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        user_id = request.data.get('id')
        suspended = request.data.get('suspended')
        user_obj = account_models.UserProfile.objects.filter(user__id=user_id).last()
        if user_obj:
            if suspended.lower() == "true":
                user_obj.suspend = True
            elif suspended.lower() == "false":
                user_obj.suspend = False
            user_obj.save()
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg='user suspended Status Updated'), status.HTTP_200_OK)
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='User not found'), status.HTTP_400_BAD_REQUEST)


class DownloadExcelFileDashboard(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        # Get month and year from request parameters, default to current month/year if not provided
        month = request.query_params.get('month', str(datetime.now().month))
        year = request.query_params.get('year', str(datetime.now().year))

        try:
            month = int(month)
            year = int(year)
        except (ValueError, TypeError):
            month = datetime.now().month
            year = datetime.now().year

        excel_file = BytesIO()
        workbook = openpyxl.Workbook()

        # Get data for the specified month and year
        users_data = account_models.UserProfile.objects.filter(created_at__month=month,
                                                               created_at__year=year).count()
        shield_data = shield_models.ShieldModel.objects.filter(created_at__month=month,
                                                               created_at__year=year).count()
        ticket_data = ticket_models.Ticket.objects.filter(created_at__month=month,
                                                          created_at__year=year).count()
        resolved_ticket_data = ticket_models.Ticket.objects.filter(created_at__month=month,
                                                                   created_at__year=year, resolved=True).count()

        alerts_data = alert_models.AlertModel.objects.filter(created_at__month=month,
                                                             created_at__year=year).count()
        sos_data = sos_models.Sos.objects.filter(created_at__month=month, created_at__year=year).count()

        # Check if there's any data for the selected month
        if users_data == 0 and shield_data == 0 and ticket_data == 0 and alerts_data == 0 and sos_data == 0:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                               msg='¡No hay datos en el período de tiempo seleccionado!'),
                status.HTTP_404_NOT_FOUND)

        # Get data for monthly charts with error handling
        try:
            all_users = account_models.UserProfile.objects.all()
            per_month_users_data = dashboard_utils.get_data_per_month(all_users)
            all_shields = shield_models.ShieldModel.objects.all()
            per_month_shields_data = dashboard_utils.get_data_per_month(all_shields)

            # Debug information
            print(f"per_month_users_data: {per_month_users_data}")
            print(f"per_month_shields_data: {per_month_shields_data}")

            # Validate data structure to prevent IndexError
            if not per_month_users_data or len(per_month_users_data) < 2:
                per_month_users_data = [[0] * 13, [""] * 13]  # Default empty data

            if not per_month_shields_data or len(per_month_shields_data) < 2:
                per_month_shields_data = [[0] * 13, [""] * 13]  # Default empty data

            # Ensure each array has enough elements
            if len(per_month_users_data[0]) < 13:
                per_month_users_data[0].extend([0] * (13 - len(per_month_users_data[0])))
            if len(per_month_users_data[1]) < 13:
                per_month_users_data[1].extend([""] * (13 - len(per_month_users_data[1])))

            if len(per_month_shields_data[0]) < 13:
                per_month_shields_data[0].extend([0] * (13 - len(per_month_shields_data[0])))
            if len(per_month_shields_data[1]) < 13:
                per_month_shields_data[1].extend([""] * (13 - len(per_month_shields_data[1])))
        except Exception as e:
            print(f"Error preparing monthly data: {e}")
            # Provide default empty data in case of error
            per_month_users_data = [[0] * 13, [""] * 13]
            per_month_shields_data = [[0] * 13, [""] * 13]

        sheet1 = workbook.active
        sheet1.title = "Users Details"
        sheet2 = workbook.create_sheet("Shield Details")
        sheet3 = workbook.create_sheet("Tickets Details")
        sheet4 = workbook.create_sheet("Alert & SOS Details")
        sheet5 = workbook.create_sheet("Users registered month wise")
        sheet6 = workbook.create_sheet("Shield created month wise")
        # add data to the sheets
        sheet1["A1"] = "Total Users"
        sheet1["B1"] = users_data
        sheet2["A1"] = 'Total Shields'
        sheet2["B1"] = shield_data
        sheet3["A1"] = "Total Tickets"
        sheet3["B1"] = ticket_data
        sheet3["A2"] = "Resolved Tickets"
        sheet3["B2"] = resolved_ticket_data
        sheet4["A1"] = "Total Alerts"
        sheet4["B1"] = alerts_data
        sheet4["A2"] = "Total SOS"
        sheet4["B2"] = sos_data
        sheet5["A1"] = per_month_users_data[1][0]
        sheet5["A2"] = per_month_users_data[1][1]
        sheet5["A3"] = per_month_users_data[1][2]
        sheet5["A4"] = per_month_users_data[1][3]
        sheet5["A5"] = per_month_users_data[1][4]
        sheet5["A6"] = per_month_users_data[1][5]
        sheet5["A7"] = per_month_users_data[1][6]
        sheet5["A8"] = per_month_users_data[1][7]
        sheet5["A9"] = per_month_users_data[1][8]
        sheet5["A10"] = per_month_users_data[1][9]
        sheet5["A11"] = per_month_users_data[1][10]
        sheet5["A12"] = per_month_users_data[1][11]
        sheet5["A13"] = per_month_users_data[1][12]
        sheet5["B1"] = per_month_users_data[0][0]
        sheet5["B2"] = per_month_users_data[0][1]
        sheet5["B3"] = per_month_users_data[0][2]
        sheet5["B4"] = per_month_users_data[0][3]
        sheet5["B5"] = per_month_users_data[0][4]
        sheet5["B6"] = per_month_users_data[0][5]
        sheet5["B7"] = per_month_users_data[0][6]
        sheet5["B8"] = per_month_users_data[0][7]
        sheet5["B9"] = per_month_users_data[0][8]
        sheet5["B10"] = per_month_users_data[0][9]
        sheet5["B11"] = per_month_users_data[0][10]
        sheet5["B12"] = per_month_users_data[0][11]
        sheet5["B13"] = per_month_users_data[0][12]
        sheet6["A1"] = per_month_shields_data[1][0]
        sheet6["A2"] = per_month_shields_data[1][1]
        sheet6["A3"] = per_month_shields_data[1][2]
        sheet6["A4"] = per_month_shields_data[1][3]
        sheet6["A5"] = per_month_shields_data[1][4]
        sheet6["A6"] = per_month_shields_data[1][5]
        sheet6["A7"] = per_month_shields_data[1][6]
        sheet6["A8"] = per_month_shields_data[1][7]
        sheet6["A9"] = per_month_shields_data[1][8]
        sheet6["A10"] = per_month_shields_data[1][9]
        sheet6["A11"] = per_month_shields_data[1][10]
        sheet6["A12"] = per_month_shields_data[1][11]
        sheet6["A13"] = per_month_shields_data[1][12]
        sheet6["B1"] = per_month_shields_data[0][0]
        sheet6["B2"] = per_month_shields_data[0][1]
        sheet6["B3"] = per_month_shields_data[0][2]
        sheet6["B4"] = per_month_shields_data[0][3]
        sheet6["B5"] = per_month_shields_data[0][4]
        sheet6["B6"] = per_month_shields_data[0][5]
        sheet6["B7"] = per_month_shields_data[0][6]
        sheet6["B8"] = per_month_shields_data[0][7]
        sheet6["B9"] = per_month_shields_data[0][8]
        sheet6["B10"] = per_month_shields_data[0][9]
        sheet6["B11"] = per_month_shields_data[0][10]
        sheet6["B12"] = per_month_shields_data[0][11]
        sheet6["B13"] = per_month_shields_data[0][12]
        # Random number for file naming (if needed)
        # randon_num = random.randint(11111,99999)

        # Ensure directory exists
        directory = "media/adminside_dashboard"
        if not os.path.exists(directory):
            os.makedirs(directory)

        # Save the workbook to a BytesIO object instead of a file
        excel_buffer = BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)

        # Create the HTTP response with the Excel file
        response = HttpResponse(
            excel_buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Set the appropriate headers for file download
        month_names = {
            1: "Enero", 2: "Febrero", 3: "Marzo", 4: "Abril", 5: "Mayo", 6: "Junio",
            7: "Julio", 8: "Agosto", 9: "Septiembre", 10: "Octubre", 11: "Noviembre", 12: "Diciembre"
        }
        month_name = month_names.get(month, "Mes")

        response['Content-Disposition'] = f'attachment; filename="Dashboard_{month_name}_{year}.xlsx"'
        return response


class DownloadPdfFileDashboard(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = dashboard_serialzier.GetMonthlySerializer

    def get(self, request):
        # Get month and year from request parameters, default to current month/year if not provided
        month = request.query_params.get('month', str(datetime.now().month))
        year = request.query_params.get('year', str(datetime.now().year))

        try:
            month = int(month)
            year = int(year)
        except (ValueError, TypeError):
            month = datetime.now().month
            year = datetime.now().year

        # Get data for the specified month and year
        users_data = account_models.UserProfile.objects.filter(created_at__month=month,
                                                               created_at__year=year).count()
        shield_data = shield_models.ShieldModel.objects.filter(created_at__month=month,
                                                               created_at__year=year).count()
        ticket_data = ticket_models.Ticket.objects.filter(created_at__month=month,
                                                          created_at__year=year).count()
        resolved_ticket_data = ticket_models.Ticket.objects.filter(created_at__month=month,
                                                                   created_at__year=year, resolved=True).count()

        alerts_data = alert_models.AlertModel.objects.filter(created_at__month=month,
                                                             created_at__year=year).count()
        sos_data = sos_models.Sos.objects.filter(created_at__month=month, created_at__year=year).count()

        # Check if there's any data for the selected month
        if users_data == 0 and shield_data == 0 and ticket_data == 0 and alerts_data == 0 and sos_data == 0:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                               msg='¡No hay datos en el período de tiempo seleccionado!'),
                status.HTTP_404_NOT_FOUND)

        # Create a PDF document
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        styles = getSampleStyleSheet()
        style_heading = styles["Heading1"]
        # style_normal is used for normal text
        style_normal = styles["Normal"]

        # Create elements for the PDF
        elements = []

        # Add title
        month_names = {
            1: "Enero", 2: "Febrero", 3: "Marzo", 4: "Abril", 5: "Mayo", 6: "Junio",
            7: "Julio", 8: "Agosto", 9: "Septiembre", 10: "Octubre", 11: "Noviembre", 12: "Diciembre"
        }
        month_name = month_names.get(month, "Mes")
        elements.append(Paragraph(f"Dashboard {month_name} {year}", style_heading))
        elements.append(Spacer(1, 0.25 * inch))

        # Add user data
        elements.append(Paragraph("Detalles de Usuarios", style_heading))
        elements.append(Spacer(1, 0.1 * inch))
        user_data = [["Total Usuarios", str(users_data)]]
        user_table = Table(user_data)
        user_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), '#f2f2f2'),
            ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
            ('GRID', (0, 0), (-1, -1), 1, '#888888'),
        ]))
        elements.append(user_table)
        elements.append(Spacer(1, 0.25 * inch))

        # Add shield data
        elements.append(Paragraph("Detalles de Escudos", style_heading))
        elements.append(Spacer(1, 0.1 * inch))
        shield_data_table = [["Total Escudos", str(shield_data)]]
        shield_table = Table(shield_data_table)
        shield_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), '#f2f2f2'),
            ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
            ('GRID', (0, 0), (-1, -1), 1, '#888888'),
        ]))
        elements.append(shield_table)
        elements.append(Spacer(1, 0.25 * inch))

        # Add ticket data
        elements.append(Paragraph("Detalles de Tickets", style_heading))
        elements.append(Spacer(1, 0.1 * inch))
        ticket_data_table = [
            ["Total Tickets", str(ticket_data)],
            ["Tickets Resueltos", str(resolved_ticket_data)]
        ]
        ticket_table = Table(ticket_data_table)
        ticket_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), '#f2f2f2'),
            ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
            ('GRID', (0, 0), (-1, -1), 1, '#888888'),
        ]))
        elements.append(ticket_table)
        elements.append(Spacer(1, 0.25 * inch))

        # Add alerts and SOS data
        elements.append(Paragraph("Detalles de Alertas y SOS", style_heading))
        elements.append(Spacer(1, 0.1 * inch))
        alerts_data_table = [
            ["Total Alertas", str(alerts_data)],
            ["Total SOS", str(sos_data)]
        ]
        alerts_table = Table(alerts_data_table)
        alerts_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), '#f2f2f2'),
            ('TEXTCOLOR', (0, 0), (-1, 0), '#000000'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), '#ffffff'),
            ('GRID', (0, 0), (-1, -1), 1, '#888888'),
        ]))
        elements.append(alerts_table)

        # Build the PDF document
        doc.build(elements)
        buffer.seek(0)

        # Create the HTTP response with the PDF file
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="Dashboard_{month_name}_{year}.pdf"'

        return response
