import PromoCodesTable from "@/components/promo-codes/PromoCodesTable";
import TopBar from "@/components/promo-codes/TopBar";
import useTableData from "@/hooks/useTableData";
import Admin from "@/components/layouts/Admin";
import Pagination from "@/components/Pagination";


const pageSize = 10;

export default function PromoCodes() {

  const {
    search,
    setSearch,
    currentTableData,
    tempFilters,
    setTempFilters,
    applyFilters,
    isLoading,
    isError,
    error,
    sort,
    setSort,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess,
    resetPage,
    refetch
  } = useTableData({
    dataUrl: "adminside/api/admin/promo-code/",
    pageSize: pageSize,
    queryKeys: ["promo-codes-table-data"],
    dataCallback: (resp) => {
      // The API returns an array directly, not nested in data.data
      return Array.isArray(resp?.data) ? resp.data : [];
    }
  })

  return (
    <Admin pageTitle="Códigos de Promo" headerTitle="Códigos de Promo">
      <TopBar
        refetch={refetch}
        search={search}
        setSearch={setSearch}
        tempFilters={tempFilters}
        setTempFilters={setTempFilters}
        applyFilters={applyFilters}
        resetPage={resetPage}
      />

      <div className="container-padding">
        <PromoCodesTable
          refetch={refetch}
          promoCodes={currentTableData}
          isLoading={isLoading}
          isError={isError}
          error={error}
          sort={sort}
          setSort={setSort}
        />
        {isSuccess && (
          <div className="mt-3.5 -translate-y-96">
            <Pagination
              totalCount={allData.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </Admin>
  );
}
