from django.db.models import Q
from django.shortcuts import render
from Membership import models as membership_models
from . import serializer as membership_seri
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils
from rest_framework import status
from django.http import HttpResponse,JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework import permissions
from .pagination import customPagination
from rest_framework.generics import ListAPIView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework import generics
from datetime import datetime
import django_filters


# Create your views here.

class MembershipPaymentFilter(django_filters.FilterSet):
    """Custom filter for membership payments with date range support"""
    date_from = django_filters.DateFilter(field_name='date', lookup_expr='gte')
    date_to = django_filters.DateFilter(field_name='date', lookup_expr='lte')
    date_range = django_filters.DateFromToRangeFilter(field_name='date')

    class Meta:
        model = membership_models.MembershipModel
        fields = {
            'membership': ['exact'],
            'conditions': ['exact'],
            'order_id': ['icontains'],
            'transaction_id': ['icontains'],
            'userprofile': ['exact'],
        }
class membership_get(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id=None):
        try:
            if not id:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        msg='ID de pago requerido'
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                item = membership_models.MembershipModel.objects.get(id=id)
            except membership_models.MembershipModel.DoesNotExist:
                return Response(
                    backend_utils.failure_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        msg='Pago no encontrado'
                    ),
                    status=status.HTTP_404_NOT_FOUND
                )

            serializer = membership_seri.membershipSerializer(item)
            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=serializer.data,
                    msg='Detalles del pago cargados correctamente'
                )
            )
        except Exception as e:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg='Error al obtener información del pago'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class membership_payment(generics.ListAPIView):
    permission_classes = [IsAuthenticated]
    queryset = membership_models.MembershipModel.objects.all().select_related('userprofile__user').order_by('-date')
    serializer_class = membership_seri.membershipSerializer
    pagination_class = customPagination
    filter_backends = (DjangoFilterBackend, OrderingFilter, SearchFilter)
    filterset_class = MembershipPaymentFilter
    search_fields = ('order_id', 'transaction_id', 'unitary_amount', 'membership', 'conditions', 'payment_address', 'ipaddress', 'total_amount')
    ordering_fields = ("date", "order_id", "transaction_id", "membership", "total_amount", "conditions")
    ordering = ['-date']

    def get_queryset(self):
        """Override to add custom filtering and validation"""
        queryset = super().get_queryset()

        # Add any additional custom filtering here if needed
        return queryset

    def list(self, request, *args, **kwargs):
        """Override to add custom response format and error handling"""
        try:
            response = super().list(request, *args, **kwargs)

            # Check if no results found
            if hasattr(response, 'data') and isinstance(response.data, dict):
                results = response.data.get('results', [])
                if not results:
                    return Response(
                        backend_utils.success_response(
                            status_code=status.HTTP_200_OK,
                            data=[],
                            msg='No se encontraron resultados'
                        )
                    )

            return Response(
                backend_utils.success_response(
                    status_code=status.HTTP_200_OK,
                    data=response.data.get('results', response.data) if hasattr(response, 'data') else [],
                    msg='Pagos de membresías cargados correctamente'
                )
            )
        except Exception as e:
            return Response(
                backend_utils.failure_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    msg='No se pudieron cargar los detalles del pago'
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

