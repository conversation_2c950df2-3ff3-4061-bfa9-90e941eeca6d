import Admin from "@/components/layouts/Admin";
import RightCard from "@/components/support/RightCard";
import DividerText from "@/components/utility/DividerText";
import InputGroup from "@/components/utility/InputGroup";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/solid";
import classNames from "classnames";
import React, { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { useQuery, useMutation } from "react-query";
import useAxios from "@/hooks/useAxios";
import { format } from "date-fns";
import { toast } from "react-hot-toast";
import SupportService from "@/services/supportService";
import FirebaseService from "@/services/firebaseService";

export default function index() {
  const { axios } = useAxios();
  const token = useSelector((state) => state.user.token);
  const messagesEndRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [message, setMessage] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  // Fetch all tickets
  const fetchTickets = () => {
    return axios.get("adminside/api/ticket/ticket/");
  };

  const {
    isLoading,
    data: ticketsResponse,
    isError,
    error,
    refetch: refetchTickets
  } = useQuery(["support-tickets"], fetchTickets, {
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      if (data?.data?.data?.length > 0 && !selectedTicket) {
        setSelectedTicket(data.data.data[0]);
      }
    }
  });

  // Get user tickets when a ticket is selected
  const fetchUserTickets = () => {
    if (!selectedTicket?.user?.id) return null;
    return axios.get("adminside/api/ticket/getusertickets/", {
      params: { user_id: selectedTicket.user.id }
    });
  };

  const {
    data: userTicketsResponse,
    refetch: refetchUserTickets
  } = useQuery(
    ["user-tickets", selectedTicket?.user?.id],
    fetchUserTickets,
    {
      refetchOnWindowFocus: false,
      enabled: !!selectedTicket?.user?.id,
    }
  );

  // Handle errors
  useEffect(() => {
    if (isError) {
      toast.error(error.message || "Error fetching tickets");
    }
  }, [isError, error]);

  // Filter tickets based on search term
  const tickets = ticketsResponse?.data?.data || [];
  const filteredTickets = tickets.filter(ticket => {
    // Trim the search term to handle cases with extra spaces
    const trimmedSearchTerm = searchTerm.trim().toLowerCase();

    // Only filter if there's actually a search term
    if (trimmedSearchTerm.length === 0) {
      return true; // Show all tickets when no search term
    }

    return ticket.title.toLowerCase().includes(trimmedSearchTerm) ||
      (ticket.ticket_num && ticket.ticket_num.toLowerCase().includes(trimmedSearchTerm));
  });

  // Get user tickets
  const userTickets = userTicketsResponse?.data?.data || [];

  // Fetch ticket messages
  const fetchTicketMessages = async (ticketId) => {
    if (!ticketId) return [];
    try {
      const response = await SupportService.getTicketMessages(token, ticketId);
      return response.data || [];
    } catch (error) {
      console.error("Error fetching ticket messages:", error);
      return [];
    }
  };

  // We don't need to query for initial ticket messages anymore, as we're using Firebase only

  // Subscribe to real-time updates for ticket messages
  useEffect(() => {
    if (!selectedTicket?.id) return;

    console.log(`Setting up message subscription for ticket ${selectedTicket.id}`);

    // Reset messages and set loading state when ticket changes
    setTicketMessages([]);
    setIsLoadingMessages(true);

    // Variable to store the unsubscribe function
    let unsubscribeFunc = () => {};

    // Subscribe to real-time updates with Firebase only
    console.log(`Subscribing to Firebase updates for ticket ${selectedTicket.id}`);

    // Since subscribeToTicketMessages is now async, we need to handle it properly
    FirebaseService.subscribeToTicketMessages(
      selectedTicket.id.toString(), // Ensure ticketId is a string
      (changes, fullSnapshotMessages) => {
        console.log(`Received Firestore changes for ticket ${selectedTicket.id}`, changes);

        setTicketMessages(prevMessages => {
          // Handle initial load/reset or significant changes by replacing the state
          if (prevMessages.length === 0 && fullSnapshotMessages.length > 0 || changes.length > prevMessages.length * 0.5) { // Heuristic for significant changes
             console.log('Replacing messages state due to initial load or significant changes');
             return fullSnapshotMessages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          }

          let updatedMessages = [...prevMessages];

          changes.forEach(change => {
            const normalizedMsg = change.doc; // Already normalized by FirebaseService

            if (change.type === 'added') {
               // Only add if it doesn't already exist to avoid duplicates
               if (!updatedMessages.some(msg => msg.id === normalizedMsg.id)) {
                  updatedMessages.splice(change.newIndex, 0, normalizedMsg);
               }
            } else if (change.type === 'modified') {
              // Find and replace the modified message
              const index = updatedMessages.findIndex(msg => msg.id === normalizedMsg.id);
              if (index > -1) {
                updatedMessages[index] = normalizedMsg;
              }
            } else if (change.type === 'removed') {
              // Filter out the removed message
              updatedMessages = updatedMessages.filter(msg => msg.id !== normalizedMsg.id);
            }
          });

          // Re-sort the messages after applying changes
          updatedMessages.sort((a, b) => {
            try {
              return new Date(a.createdAt) - new Date(b.createdAt);
            } catch {
              return 0;
            }
          });

          return updatedMessages;
        });

        // Auto-scroll only if new messages were added or on initial load, and user is near bottom
        if (changes.some(change => change.type === 'added') || (messagesEndRef.current && messagesEndRef.current.getBoundingClientRect().top <= window.innerHeight + 100)) {
             setTimeout(scrollToBottom, 50);
        }

        // Always set loading to false after the first snapshot is received
        if (isLoadingMessages) {
           setIsLoadingMessages(false);
        }
      }
    ).then(unsubscribe => {
      // Store the unsubscribe function for cleanup
      if (typeof unsubscribe === 'function') {
        unsubscribeFunc = unsubscribe;
      }
       // Set loading to false after a timeout even if no messages arrive initially
       setTimeout(() => {
           setIsLoadingMessages(false);
       }, 3000);
    }).catch(error => {
      console.error(`Error setting up subscription for ticket ${selectedTicket.id}:`, error);
      setIsLoadingMessages(false);
    });

    // Cleanup subscription when component unmounts or ticket changes
    return () => {
      console.log(`Cleaning up subscription for ticket ${selectedTicket.id}`);
      if (typeof unsubscribeFunc === 'function') {
        unsubscribeFunc();
      }
      // Reset messages when unmounting to prevent showing old messages
      setTicketMessages([]);
    };
  }, [selectedTicket?.id]);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Handle ticket selection
  const handleTicketSelect = (ticket) => {
    // Reset messages before changing the selected ticket
    setTicketMessages([]);
    setIsLoadingMessages(true);
    setSelectedTicket(ticket);
    // Reset message input
    setMessage("");
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    // Set the raw value and let the filter function handle trimming
    setSearchTerm(e.target.value);
  };

  // Handle message input change
  const handleMessageChange = (e) => {
    setMessage(e.target.value);
  };

  // Send message mutation
  const sendMessageMutation = useMutation(
    (messageData) => SupportService.sendTicketMessage(
      token,
      messageData.ticketId,
      messageData.message
    ),
    {
      onSuccess: (response, variables) => {
        console.log("Message sent successfully:", response?.data);
        toast.success("Mensaje enviado con éxito");
        setMessage("");

        // Add message to Firebase for real-time updates
        try {
          const messageData = response?.data;
          if (messageData) {
            console.log(`Adding message to Firebase for ticket ${variables.ticketId}`);
            // Add to Firebase Firestore
            FirebaseService.addTicketMessage(variables.ticketId.toString(), {
              id: messageData.id || new Date().getTime(),
              ticket: variables.ticketId,
              message: variables.message,
              is_admin: true,
              sender_name: "Administrador"
              // Note: created_at is automatically set to serverTimestamp() in the FirebaseService
            })
            .then(() => {
              console.log("Message successfully added to Firebase");
              setTimeout(scrollToBottom, 100);
            })
            .catch(error => {
              console.error("Error adding message to Firebase:", error);
            });
          }
        } catch (error) {
          console.error("Error processing message for Firebase:", error);
        }
      },
      onError: (error) => {
        console.error("Error sending message:", error);
        toast.error(error?.response?.data?.message || "Error al enviar el mensaje");
      }
    }
  );

  // Handle sending a message
  const handleSendMessage = () => {
    // Check if message is empty
    if (!message.trim()) {
      toast.error("El mensaje no puede estar vacío");
      return;
    }

    // Check if a ticket is selected
    if (!selectedTicket) {
      toast.error("Selecciona un ticket para enviar un mensaje");
      return;
    }

    sendMessageMutation.mutate({
      ticketId: selectedTicket.id,
      message: message.trim()
    });
  };

  // Handle key down in message input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle marking a ticket as resolved
  const handleResolveTicket = () => {
    if (!selectedTicket) return;

    setIsProcessing(true);
    axios.post("adminside/api/ticket/resolve_ticket/", { id: selectedTicket.id })
      .then(() => {
        toast.success("Ticket marcado como resuelto");

        // Clean up old messages in Firebase when a ticket is resolved
        try {
          FirebaseService.cleanupOldMessages(selectedTicket.id, 7) // Keep messages for 7 days
            .then((deletedCount) => {
              if (deletedCount > 0) {
                console.log(`Cleaned up ${deletedCount} old messages for ticket ${selectedTicket.id}`);
              }
            })
            .catch((error) => {
              console.error("Error cleaning up old messages:", error);
            });
        } catch (error) {
          console.error("Error cleaning up old messages:", error);
        }

        refetchTickets();
        refetchUserTickets();
      })
      .catch((error) => {
        toast.error(error?.response?.data?.message || "Error al resolver el ticket");
      })
      .finally(() => {
        setIsProcessing(false);
      });
  };

  // Handle pagination
  const handleNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  // Calculate pagination
  useEffect(() => {
    const itemsPerPage = 20;
    setTotalPages(Math.ceil(filteredTickets.length / itemsPerPage));
  }, [filteredTickets]);

  // Get paginated tickets
  const getPaginatedTickets = () => {
    const itemsPerPage = 20;
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredTickets.slice(startIndex, endIndex);
  };

  const paginatedTickets = getPaginatedTickets();
  const pendingTicketsCount = tickets.filter(ticket => !ticket.resolved).length;

  return (
    <Admin pageTitle="Soporte" headerTitle="Soporte">
      <div className="h-full lg:h-[calc(100vh-60px)] lg:max-h-full">
        <div className="flex h-full flex-col lg:flex-row">
          {/* Chat sidebar */}
          <div className="flex max-h-96 w-full flex-col border bg-white lg:max-h-full lg:max-w-xs">
            <div className="flex gap-2 p-4">
              <h2 className="font-medium">BANDEJA ({pendingTicketsCount})</h2>
              <span className="ml-auto text-sm text-gray-400">
                {page} de {totalPages}
              </span>
              <div className="flex gap-1">
                <button
                  className={`flex h-5 w-5 items-center justify-center border border-black ${page > 1 ? 'border-opacity-60 text-black opacity-60' : 'border-opacity-40 text-black opacity-40'}`}
                  onClick={handlePrevPage}
                  disabled={page <= 1}
                >
                  <ChevronLeftIcon className="h-4 w-4" />
                </button>
                <button
                  className={`flex h-5 w-5 items-center justify-center border border-black ${page < totalPages ? 'border-opacity-60 text-black opacity-60' : 'border-opacity-40 text-black opacity-40'}`}
                  onClick={handleNextPage}
                  disabled={page >= totalPages}
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="p-4 pt-0">
              <InputGroup className="relative">
                <div className="absolute inset-y-0 left-0 flex w-9 items-center justify-center p-1 px-1.5 pl-3 text-secondary">
                  <MagnifyingGlassIcon className="aspect-square w-full" />
                </div>
                <InputGroup.Input
                  id="search"
                  type="search"
                  name="search"
                  className="bg-accent pl-9"
                  placeholder="Buscar"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </InputGroup>
            </div>

            <ul className="flex-grow divide-y overflow-auto">
              {isLoading ? (
                <li className="p-4 text-center">Cargando tickets...</li>
              ) : paginatedTickets.length > 0 ? (
                paginatedTickets.map((ticket) => (
                  <li
                    key={ticket.id}
                    className={classNames(
                      "space-y-2.5 p-4 text-sm cursor-pointer",
                      selectedTicket?.id === ticket.id && "bg-neutral"
                    )}
                    onClick={() => handleTicketSelect(ticket)}
                  >
                    <div className="flex justify-between gap-2">
                      <dd>{ticket.ticket_num || `Ticket #${ticket.id}`}</dd>
                      {!ticket.resolved && (
                        <dd className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-white">
                          <span>!</span>
                        </dd>
                      )}
                    </div>
                    <dd className="font-semibold">
                      {ticket.title}
                    </dd>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-xs">
                        {ticket.created_at && format(new Date(ticket.created_at), "hh:mm a, dd/MM/yy")}
                      </span>
                      {ticket.resolved ? (
                        <span className="font-semibold text-success">
                          Resuelto
                        </span>
                      ) : (
                        <span className="font-semibold text-danger">
                          Pendiente
                        </span>
                      )}
                    </div>
                  </li>
                ))
              ) : (
                <li className="p-4 text-center">No se encontraron tickets</li>
              )}
            </ul>
          </div>

          <div className="mt-7 flex flex-grow flex-col-reverse gap-5 px-4 md:px-5 lg:flex-row">
            {selectedTicket ? (
              <div className="flex flex-grow flex-col bg-white p-4">
                <div className="flex flex-col justify-between gap-4 text-sm lg:flex-row lg:items-center">
                  <div className="flex items-center gap-4">
                    <div className="h-11 w-11">
                      <img
                        className="h-11 w-11"
                        src="/assets/img/sample/user-2.png"
                        alt="User"
                      />
                    </div>
                    <div>
                      <dd className="font-semibold">{selectedTicket.user?.full_name || "Usuario"}</dd>
                      <dd>{selectedTicket.user?.id ? `UI${selectedTicket.user.id}` : ""}</dd>
                    </div>
                  </div>
                  <button
                    className="rounded bg-primary px-4 py-2.5 text-white"
                    onClick={handleResolveTicket}
                    disabled={selectedTicket.resolved || isProcessing}
                  >
                    {isProcessing ? "Procesando..." : "Marcar resuelto"}
                  </button>
                </div>
                <div className="mt-5 flex-grow space-y-3 overflow-auto bg-accent px-4">
                  <div className="px-4 py-3">
                    <DividerText
                      text={selectedTicket.created_at ? format(new Date(selectedTicket.created_at), "dd/MM/yy") : "Fecha"}
                      textClassName="bg-accent"
                    />
                  </div>

                  {/* Initial ticket message */}
                  <div className="bg-white p-4 rounded">
                    <h3 className="font-bold mb-2">{selectedTicket.title}</h3>
                    <div dangerouslySetInnerHTML={{ __html: selectedTicket.description }} />
                  </div>

                  {/* Chat messages */}
                  {isLoadingMessages ? (
                    <div className="text-center py-4">
                      <p>Cargando mensajes...</p>
                    </div>
                  ) : ticketMessages && ticketMessages.length > 0 ? (
                    <>
                      {ticketMessages.map((msg, index) => {
                        if (!msg || typeof msg !== 'object') return null;
                        const { id, type, content, sender, createdAt } = msg;
                        let messageBody = null;
                        let badge = null;
                        switch (type) {
                          case 'voice':
                            messageBody = content ? (
                              <audio controls src={content} className="w-full mt-2">
                                Tu navegador no soporta el elemento de audio.
                              </audio>
                            ) : (
                              <span className="italic text-gray-400">Audio no disponible</span>
                            );
                            badge = <span className="ml-2 px-2 py-1 rounded bg-blue-200 text-blue-800 text-xs">Audio</span>;
                            break;
                          case 'sos':
                            messageBody = (
                              <div className="p-2 bg-red-50 border-l-4 border-red-400 rounded">
                                <span className="font-bold text-red-700">SOS:</span> {content?.description || 'Mensaje SOS recibido'}
                              </div>
                            );
                            badge = <span className="ml-2 px-2 py-1 rounded bg-red-200 text-red-800 text-xs">SOS</span>;
                            break;
                          case 'alert':
                            messageBody = (
                              <div className="p-2 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                                <span className="font-bold text-yellow-700">Alerta:</span> {content?.description || 'Mensaje de alerta recibido'}
                              </div>
                            );
                            badge = <span className="ml-2 px-2 py-1 rounded bg-yellow-200 text-yellow-800 text-xs">Alerta</span>;
                            break;
                          case 'text':
                          default:
                            // Ensure content is a string before rendering
                            messageBody = <span>{typeof content === 'string' ? content : JSON.stringify(content)}</span>;
                            break;
                        }
                        return (
                          <div
                            key={id || `msg-${index}`}
                            className={`p-4 rounded ${sender && sender.role === 'Admin' ? 'bg-blue-50 ml-12' : 'bg-white mr-12'}`}
                          >
                            <div className="flex justify-between mb-2 items-center">
                              <span className="font-semibold">{sender ? sender.name : 'Usuario'}</span>
                              {badge}
                              <span className="text-xs text-gray-500 ml-auto">
                                {(() => {
                                  try {
                                    if (!createdAt) return "Fecha desconocida";
                                    const date = new Date(createdAt);
                                    if (isNaN(date.getTime())) return "Fecha desconocida";
                                    return format(date, "hh:mm a, dd/MM/yy");
                                  } catch {
                                    return "Fecha desconocida";
                                  }
                                })()}
                              </span>
                            </div>
                            {messageBody}
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </>
                  ) : (
                    <div className="text-center py-4">
                      <p>No hay mensajes para mostrar</p>
                    </div>
                  )}
                </div>
                <div className="flex border border-t-2 border-t-black px-5 pt-5">
                  <div className="flex-shrink-0 flex-grow">
                    <textarea
                      className={`w-full border-none focus:ring-0 ${selectedTicket?.resolved ? 'bg-gray-100' : ''}`}
                      name="message"
                      id="message"
                      rows="6"
                      placeholder={selectedTicket?.resolved ? "No se pueden enviar mensajes a tickets resueltos" : "Escribe tu mensaje"}
                      value={message}
                      onChange={handleMessageChange}
                      onKeyDown={handleKeyDown}
                      disabled={selectedTicket?.resolved}
                    />
                  </div>
                  <div>
                    <button
                      className={`rounded px-4 py-2.5 text-white ${
                        !message.trim() || selectedTicket?.resolved
                          ? 'bg-gray-400 cursor-not-allowed'
                          : 'bg-black hover:bg-gray-800'
                      }`}
                      onClick={handleSendMessage}
                      disabled={!message.trim() || sendMessageMutation.isLoading || isProcessing || selectedTicket?.resolved}
                      title={
                        !message.trim()
                          ? "El mensaje no puede estar vacío"
                          : selectedTicket?.resolved
                            ? "No se pueden enviar mensajes a tickets resueltos"
                            : ""
                      }
                    >
                      {sendMessageMutation.isLoading ? "Enviando..." : "Enviar"}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-grow flex-col bg-white p-4 items-center justify-center">
                <p>Selecciona un ticket para ver los detalles</p>
              </div>
            )}

            <RightCard
              selectedTicket={selectedTicket}
              userTickets={userTickets}
            />
          </div>
        </div>
      </div>
    </Admin>
  );
}
