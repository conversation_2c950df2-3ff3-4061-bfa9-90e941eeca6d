from django.urls import path
from . import views as alert_views

urlpatterns = [
    path('changealertstatus/', alert_views.ChangeAlertStatus.as_view(), name='change-alert-status'),
    path("alertall/<int:pk>/", alert_views.getAllAlert.as_view(), name="getAllAlert"),
    path("alertall/", alert_views.getAllAlert.as_view(), name="getAllAlert"),
    path("getalertmodifyhistory/", alert_views.GetAlertModifyHistorty.as_view(), name="get-alert-modify-history"),
    path("getsosmodifyhistory/", alert_views.GetSosModifyHistorty.as_view(), name="get-sos-modify-history"),
    path('changesosstatus/', alert_views.ChangeSosStatus.as_view(), name='change-sos-status'),
    path('postcommentalertsos/', alert_views.PostCommentAlertSos.as_view(), name='post-comment-alert-sos'),
    path('getcommentalertsos/', alert_views.GetCommentAlertSos.as_view(), name='get-comment-alert-sos'),
    path('user-alerts/', alert_views.GetUserAlerts.as_view(), name='get-user-alerts'),
    path('user-alerts-and-sos/', alert_views.GetUserAlertsAndSos.as_view(), name='get-user-alerts-and-sos'),

]
