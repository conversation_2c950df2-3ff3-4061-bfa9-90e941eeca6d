# Generated by Django 5.2.1 on 2025-06-17 11:11

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('AdminSide', '0003_adminpermission'),
    ]

    operations = [
        migrations.RunSQL(
            """
            -- Drop the old table and recreate with correct structure
            DROP TABLE IF EXISTS "AdminSide_adminpermission" CASCADE;

            CREATE TABLE "AdminSide_adminpermission" (
                "id" bigserial NOT NULL PRIMARY KEY,
                "admin_id" bigint NOT NULL UNIQUE,
                "users_access" boolean NOT NULL DEFAULT false,
                "shields_access" boolean NOT NULL DEFAULT false,
                "alerts_sos_access" boolean NOT NULL DEFAULT false,
                "payment_history_access" boolean NOT NULL DEFAULT false,
                "support_access" boolean NOT NULL DEFAULT false,
                "roles_access" boolean NOT NULL DEFAULT false,
                "full_access" boolean NOT NULL DEFAULT false,
                "created_at" timestamp with time zone NOT NULL DEFAULT NOW(),
                "updated_at" timestamp with time zone NOT NULL DEFAULT NOW(),
                CONSTRAINT "AdminSide_adminpermission_admin_id_fkey"
                FOREIGN KEY ("admin_id") REFERENCES "Account_userprofile" ("id")
                DEFERRABLE INITIALLY DEFERRED
            );

            -- Create index for foreign key
            CREATE INDEX "AdminSide_adminpermission_admin_id_idx" ON "AdminSide_adminpermission" ("admin_id");
            """,
            reverse_sql="DROP TABLE IF EXISTS \"AdminSide_adminpermission\" CASCADE;"
        ),
    ]
