import classNames from "classnames";
import React from "react";
import { format } from "date-fns";

const TicketHistoryCard = ({ userTickets = [] }) => {
  if (!userTickets || userTickets.length === 0) {
    return (
      <div className="bg-white p-5 flex items-center justify-center">
        <p>No hay historial de tickets disponible</p>
      </div>
    );
  }

  return (
    <ul className="max-h-96 flex-grow divide-y overflow-auto border bg-neutral lg:max-h-full">
      {userTickets.map((ticket) => (
        <li key={ticket.id} className={classNames("space-y-2.5 p-4 text-sm", "bg-white")}>
          <div className="flex justify-between gap-2">
            <dd>{ticket.ticket_num || `Ticket #${ticket.id}`}</dd>
          </div>
          <dd className="font-semibold">{ticket.title}</dd>
          <div className="flex items-center justify-between text-sm">
            <span className="text-xs">
              {ticket.created_at && format(new Date(ticket.created_at), "hh:mm a, dd/MM/yy")}
            </span>
            {ticket.resolved ? (
              <span className="font-semibold text-success">Resuelto</span>
            ) : (
              <span className="font-semibold text-danger">Pendiente</span>
            )}
          </div>
        </li>
      ))}
    </ul>
  );
};

export default TicketHistoryCard;
