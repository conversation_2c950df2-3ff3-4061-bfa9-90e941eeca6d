from . import models as sos_models
from rest_framework import serializers
from Account import models as account_models
from django.contrib.auth.models import User


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'username', 'email']


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role',
                  'lat', 'long', 'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend',
                  'created_at', 'updated_at', 'image_url']


class CreateSosRequestSerializer(serializers.Serializer):
    lat = serializers.CharField(required=True)
    long = serializers.CharField(required=True)
    location = serializers.CharField(required=True)
    shield_id = serializers.IntegerField(required=True)


class CreateSosSerializer(serializers.ModelSerializer):
    class Meta:
        model = sos_models.Sos
        fields = "__all__"


class SosEvidenceSerializer(serializers.ModelSerializer):
    evidence_url = serializers.SerializerMethodField()

    class Meta:
        model = sos_models.SosEvidence
        fields = "__all__"

    def get_evidence_url(self, obj):
        if obj.evidence:
            try:
                from mas_seguros_backend import settings as backend_setting
                return backend_setting.Base_url_path.format(url=obj.evidence.url)
            except:
                return None
        return None


class CreateSosEvidenceSerializer(serializers.Serializer):
    evidence = serializers.FileField(required=True)
    sos_id = serializers.IntegerField(required=True)


class SosSerializer(serializers.Serializer):
    sos_id = serializers.IntegerField(required=True)


class CancelSosRequestSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)
    sos_id = serializers.IntegerField(required=True)


class ShieldSerializer(serializers.Serializer):
    shield_id = serializers.IntegerField(required=True)


class SosCommentHistorySerializer(serializers.ModelSerializer):
    userprofile = serializers.SerializerMethodField()

    def get_userprofile(self, obj):
        if obj.userprofile:
            return {
                'full_name': obj.userprofile.full_name,
                'user': {
                    'id': obj.userprofile.user.id if obj.userprofile.user else None
                }
            }
        return None

    class Meta:
        model = sos_models.SosComment
        fields = ['id', 'comment', 'userprofile', 'created_at', 'updated_at']


class SosDetailSerializer(serializers.ModelSerializer):
    sender = UserProfileSerializer()
    status_name = serializers.SerializerMethodField()
    alert_datetime = serializers.DateTimeField(source='created_at')
    alert_date = serializers.SerializerMethodField()
    alert_time = serializers.SerializerMethodField()
    description = serializers.SerializerMethodField()
    evidence = serializers.SerializerMethodField()
    evidence_url = serializers.SerializerMethodField()
    evidence_number = serializers.SerializerMethodField()
    shield = serializers.SerializerMethodField()

    class Meta:
        model = sos_models.Sos
        fields = [
            'id',
            'sender',
            'status',
            'status_name',
            'location',
            'lat',
            'long',
            'alert_datetime',
            'alert_date',
            'alert_time',
            'description',
            'shield',
            'evidence',
            'evidence_url',
            'evidence_number',
            'created_at'
        ]

    def get_status_name(self, obj):
        return obj.status or ''

    def get_alert_date(self, obj):
        if obj.created_at:
            return obj.created_at.strftime('%d/%m/%Y')
        return None

    def get_alert_time(self, obj):
        if obj.created_at:
            return obj.created_at.strftime('%H:%M')
        return None

    def get_description(self, obj):
        # Get the latest comment if available using prefetched data
        try:
            comments = getattr(obj, '_prefetched_objects_cache', {}).get('soscomment_set', None)
            if comments is not None:
                latest_comment = comments[0] if comments else None
            else:
                latest_comment = obj.soscomment_set.order_by('-created_at').first()
            return latest_comment.comment if latest_comment else "No hay comentarios disponibles para este SOS"
        except:
            return "No hay comentarios disponibles para este SOS"

    def get_evidence(self, obj):
        # Get the latest evidence if available using prefetched data
        try:
            evidences = getattr(obj, '_prefetched_objects_cache', {}).get('sosevidence_set', None)
            if evidences is not None:
                latest_evidence = evidences[0] if evidences else None
            else:
                latest_evidence = obj.sosevidence_set.order_by('-created_at').first()

            if latest_evidence and latest_evidence.evidence:
                from mas_seguros_backend import settings as backend_setting
                return backend_setting.Base_url_path.format(url=latest_evidence.evidence.url)
        except Exception as e:
            print(f"Error getting SOS evidence: {str(e)}")
        return None

    def get_evidence_url(self, obj):
        # Reuse the same evidence lookup to avoid duplicate queries
        return self.get_evidence(obj)

    def get_evidence_number(self, obj):
        return f"S{obj.id}"

    def get_shield(self, obj):
        if obj.shield:
            return {
                'id': obj.shield.id,
                'shield_code': obj.shield.shield_code,
                'shield_name': obj.shield.shield_name
            }
        return None