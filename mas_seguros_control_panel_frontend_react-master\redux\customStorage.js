// Custom storage implementation for redux-persist that works in both server and client environments
// and is production-ready with robust error handling
const createCustomStorage = () => {
  // Check if we're in a browser environment
  const isClient = typeof window !== 'undefined';

  // If we're on the client, use localStorage with robust error handling
  if (isClient) {
    // First check if localStorage is actually available (some browsers may have it disabled)
    let localStorageAvailable = false;
    try {
      // Test localStorage availability
      window.localStorage.setItem('redux_test', 'test');
      window.localStorage.removeItem('redux_test');
      localStorageAvailable = true;
    } catch (e) {
      // localStorage is not available or permission denied
      console.warn('localStorage is not available, using in-memory storage for redux-persist');
    }

    // If localStorage is available, use it
    if (localStorageAvailable) {
      return {
        getItem: (key) => {
          try {
            const item = window.localStorage.getItem(key);
            return Promise.resolve(item);
          } catch (error) {
            console.warn(`Error getting item ${key} from localStorage:`, error);
            return Promise.resolve(null);
          }
        },
        setItem: (key, value) => {
          try {
            window.localStorage.setItem(key, value);
            return Promise.resolve(true);
          } catch (error) {
            console.warn(`Error setting item ${key} in localStorage:`, error);
            return Promise.resolve(false);
          }
        },
        removeItem: (key) => {
          try {
            window.localStorage.removeItem(key);
            return Promise.resolve();
          } catch (error) {
            console.warn(`Error removing item ${key} from localStorage:`, error);
            return Promise.resolve();
          }
        }
      };
    }
  }

  // If we're on the server or localStorage is not available, use a noop storage
  // This is a silent fallback that won't affect the application's functionality
  return {
    getItem: () => Promise.resolve(null),
    setItem: () => Promise.resolve(),
    removeItem: () => Promise.resolve()
  };
};

const customStorage = createCustomStorage();

export default customStorage;
