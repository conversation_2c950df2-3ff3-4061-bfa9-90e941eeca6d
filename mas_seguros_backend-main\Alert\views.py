from django.db.models import Q
from django.shortcuts import render
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework.generics import get_object_or_404
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status
from rest_framework.response import Response
from mas_seguros_backend import utils as backend_utils, prefixes as backend_prefixes
from . import models as alert_models, utils as alert_utils, serializers as alert_serializers
from django.http import JsonResponse
from Account import fcm as fcm_file, models as account_models


# Create your views here.


class Alert(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.CreateAlertSerializer
    get_alert_serializer_class = alert_serializers.GetAlertIdSerializer
    get_complete_alert_serializer_class = alert_serializers.GetAlertSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=True)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        not_exist_already = alert_utils.check_if_unsolved_alert_exist(request.user)
        if not_exist_already:
            location = request.data.get('address', None)
            if not location:
                address = 'Quitor,Ecudor'
                lat = '0.1807'
                long = '78.4678'
            alert_obj = serializer.save()
            alert_obj.userprofile = request.user.userprofile
            alert_status = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SENT).last()
            alert_obj.status = alert_status
            alert_obj.num = alert_utils.generate_prefix_number(request.user.userprofile)
            alert_obj.evidence_number = alert_utils.generate_unique_evidence_number('dummy')
            alert_obj.save()
            if not location:
                alert_obj: alert_models.AlertModel
                alert_obj.address = address
                alert_obj.lat = lat
                alert_obj.long = long
                alert_obj.save()
            serializer = self.get_complete_alert_serializer_class(alert_obj)
            user_registration_id = account_models.UserProfile.objects.filter(user__is_staff=True).last()
            registration_id = account_models.FcmDeviceRegistration.objects.filter(
                userprofile=user_registration_id).last()
            print("this is device id:=====", registration_id)
            # Only send FCM notification if a valid device registration exists
            if registration_id and registration_id.device_id:
                fcm_file.send_fcm_notification_for_generating_alert(registration_id.device_id, 'New Alert SOS',
                                                                    'New Alert from {user}'.format(
                                                                        user=request.user.get_full_name()))
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Alert Sent Successfully'))
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg='La alerta anterior debe resolverse para crear una nueva alerta.'),
                status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        serializer = self.get_alert_serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        alert_id = request.data.get('id')
        alert_obj = alert_models.AlertModel.objects.filter(id=alert_id, userprofile=request.user.userprofile).last()
        if alert_obj:
            serializer = alert_serializers.GetAlertSerializer(alert_obj)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Alert Sent Successfully'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST, msg='Alert not found'))


class GetAlerts(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            # Make sure the user has a userprofile
            if not hasattr(request.user, 'userprofile') or request.user.userprofile is None:
                return Response(
                    backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                                  msg='User profile not found'),
                    status=status.HTTP_400_BAD_REQUEST)

            # Get alerts for this user with related objects to avoid N+1 queries
            alert_obj = alert_models.AlertModel.objects.filter(
                userprofile=request.user.userprofile
            ).select_related('status', 'category', 'userprofile__user').order_by('-created_at')

            # Check for alerts with null status and set default if needed
            alerts_with_null_status = alert_obj.filter(status__isnull=True)
            if alerts_with_null_status.exists():
                print(f"Found {alerts_with_null_status.count()} alerts with null status")
                # Set default status for alerts without status
                default_status = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SENT).first()
                if default_status:
                    alerts_with_null_status.update(status=default_status)
                    print(f"Updated alerts with default status: {default_status.name}")

            # Re-fetch the alerts after potential updates
            alert_obj = alert_models.AlertModel.objects.filter(
                userprofile=request.user.userprofile
            ).select_related('status', 'category', 'userprofile__user').order_by('-created_at')

            # Serialize the data with error handling
            serializer = alert_serializers.GetAlertSerializer(alert_obj, many=True)

            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                              msg='All Alerts'))
        except Exception as e:
            # Log the error
            print(f"Error in GetAlerts.get: {str(e)}")

            # Return a proper error response instead of 500
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                              msg='An error occurred while retrieving alerts'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AlertCategories(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        alert_categories = alert_models.AlertCategories.objects.all()
        serializer = alert_serializers.AlertCategories(alert_categories, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='All Alerts Categories'))


class AlertStatuses(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        alert_statuses = alert_models.AlertStatus.objects.all()
        serializer = alert_serializers.AlertStatuesSerializer(alert_statuses, many=True)
        return Response(
            backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                           msg='All Alerts Categories'))


def get_alert(request):
    if request.method == "GET":  # get request to fetch the data
        codes = alert_models.AlertModel.objects.all()

        serializer = alert_serializers.getallAlertSerilizer(codes, many=True)
        return JsonResponse(serializer.data, safe=False)


class AlertReviews(APIView):
    # permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.AlertReviewsSerializer
    get_alert_serializer_class = alert_serializers.GetAlertSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        alert_obj = alert_models.AlertModel.objects.filter(id=request.data.get('id')).last()
        if alert_obj:
            alert_obj.rating = request.data.get('rating')
            alert_obj.rating_description = request.data.get('rating_description')
            alert_obj.save()
            serializer = self.get_alert_serializer_class(alert_obj)
            user_registration_id = account_models.UserProfile.objects.filter(user__is_staff=True).last()
            registration_id = account_models.FcmDeviceRegistration.objects.filter(
                userprofile=user_registration_id).last()
            print("this is device id:=====", registration_id)
            # Only send FCM notification if a valid device registration exists
            if registration_id and registration_id.device_id:
                fcm_file.send_fcm_notification_for_generating_reviews_of_alert(registration_id.device_id,
                                                                               'Alert SOS Ratings', request)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                               msg='Alert Status Changed Successfully'))
        return Response(
            backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                           msg='Alert Not Found'))


class UnresolvedAlert(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        alert_sent_obj = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SENT).last()
        help_sent_obj = alert_models.AlertStatus.objects.filter(name=alert_models.HELP_SENT).last()
        if alert_sent_obj and help_sent_obj:
            alert_obj = alert_models.AlertModel.objects.filter(Q(status=alert_sent_obj) | Q(status=help_sent_obj),
                                                               userprofile=request.user.userprofile).last()
            if alert_obj:
                serializer = alert_serializers.GetAlertSerializer(alert_obj)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Unresolved Alert'))
            else:
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                   msg='No Unresolved Alerts'))
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                               msg='Alert statuses not found'))


class GetresolvedAlert(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        alert_sent_obj = alert_models.AlertStatus.objects.filter(name=alert_models.ALERT_SOLVED).last()
        if alert_sent_obj:
            alert_obj = alert_models.AlertModel.objects.filter(status=alert_sent_obj,
                                                               userprofile=request.user.userprofile,
                                                               alert_opened=False).last()
            if alert_obj:
                serializer = alert_serializers.GetAlertSerializer(alert_obj)
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK, data=serializer.data,
                                                   msg='Resolved Alert'))
            else:
                return Response(
                    backend_utils.success_response(status_code=status.HTTP_200_OK,
                                                   msg='No resolved Alerts Found'))
        else:
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_404_NOT_FOUND,
                                               msg='Alert status not found'))


class AlertOpened(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetAlertIdSerializer
    get_alert_serializer_class = alert_serializers.GetAlertSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        alert_id = request.data.get('id')
        alert_obj = alert_models.AlertModel.objects.filter(id=alert_id).last()
        if alert_obj:
            alert_obj.alert_opened = True
            alert_obj.save()
            # serializer = alert_serializers.GetAlertSerializer(alert_obj)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=None,
                                               msg='Resolved Alert Opened'))
        else:
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg='No Alerts Found'))


class AlertSeened(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = alert_serializers.GetAlertIdSerializer
    get_alert_serializer_class = alert_serializers.GetAlertSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        response = serializer.is_valid(raise_exception=False)
        if response:
            pass
        else:
            print(serializer.error_messages)
            return Response(
                backend_utils.failure_response(status_code=status.HTTP_400_BAD_REQUEST,
                                               msg=list(serializer.errors.items())[0][1]), status.HTTP_400_BAD_REQUEST)
        alert_id = request.data.get('id')
        alert_obj = alert_models.AlertModel.objects.filter(id=alert_id).last()
        if alert_obj:
            alert_obj.alert_seen = True
            alert_obj.save()
            # serializer = alert_serializers.GetAlertSerializer(alert_obj)
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK, data=None,
                                               msg='Alert Seened'))
        else:
            return Response(
                backend_utils.success_response(status_code=status.HTTP_200_OK,
                                               msg='No Alerts Found'))
