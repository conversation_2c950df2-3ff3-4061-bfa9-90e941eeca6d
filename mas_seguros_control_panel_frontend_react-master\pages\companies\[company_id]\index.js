import React from "react";
import Table from "@/components/Table";
import CompanyLayout from "@/components/layouts/CompanyLayout";
import Link from "next/link";
import useTableData from "@/hooks/useTableData";
import { useRouter } from "next/router";
import { format } from "date-fns";
import Pagination from "@/components/Pagination";

const pageSize = 10

export default function index() {
  const router = useRouter();
  const { company_id } = router.query;

  const {
    currentTableData,
    isLoading,
    isError,
    error,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess
  } = useTableData({
    baseURL: process.env.NEXT_PUBLIC_BACKEND_URL_2,
    dataUrl: `adminside/api/company/company-shields/?id=${company_id}`,
    pageSize: pageSize,
    queryKeys: [`company-${company_id}-shields-table-data`],
    enabled: !!company_id,
    noAuth: false,
    dataCallback: (response) => {
      // The API returns the shields data directly as an array in response.data
      // Handle both possible response structures for robustness
      if (response?.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      return [];
    },
  })


  return (
    <CompanyLayout pageTitle="Empresas" headerTitle="Empresas">
      <div className="mt-5">
        <Table
          dataCount={currentTableData.length}
          isLoading={isLoading}
          isError={isError}
          error={error}
        >
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Nombre del escudo</Table.Th>
              <Table.Th>ID del escudo</Table.Th>
              <Table.Th>N° de miembros</Table.Th>
              <Table.Th>Fecha de Creación</Table.Th>
              <Table.Th>Tipo de membresía</Table.Th>
              <Table.Th>Administrador del escudo</Table.Th>
              <Table.Th></Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {isSuccess && currentTableData.map((shield) => (
              <Table.Tr key={shield.id}>
                <Table.Td>
                  <div className="flex min-w-fit items-center gap-4">
                    <img
                      src={shield.logo_url || '/default-shield.png'}
                      className="aspect-square w-11 rounded-full object-cover"
                      alt={shield.shield_name}
                      onError={(e) => {
                        e.target.src = '/default-shield.png';
                      }}
                    />
                    <div>
                      <p className="font-semibold capitalize">{shield.shield_name || 'N/A'}</p>
                    </div>
                  </div>
                </Table.Td>
                <Table.Td>
                  <p>{shield.shield_code || 'N/A'}</p>
                </Table.Td>
                <Table.Td>
                  <p>{shield.members_count || 0}</p>
                </Table.Td>
                <Table.Td>
                  {shield.created_at ? format(new Date(shield.created_at), 'dd/MM/yyyy') : 'N/A'}
                </Table.Td>
                <Table.Td>
                  <p>{shield.membership_level || 'N/A'}</p>
                </Table.Td>
                <Table.Td className="text-secondary">
                  <p className="capitalize font-medium">{shield.admin?.full_name || 'N/A'}</p>
                  <p className="text-sm">ID: {shield.admin?.user?.id || 'N/A'}</p>
                </Table.Td>
                <Table.Td>
                  <Link
                    href={`/shields/${shield.id}`}
                    className="font-semibold text-primary hover:underline"
                  >
                    Ver detalles
                  </Link>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
        {isSuccess && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </CompanyLayout>
  );
}
