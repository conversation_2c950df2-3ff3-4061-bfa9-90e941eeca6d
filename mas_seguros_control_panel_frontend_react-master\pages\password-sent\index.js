import Auth from "@/components/layouts/Auth";
import ResendBtn from "@/components/password-sent/ResendBtn";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect } from "react";

const PasswordSent = () => {
  const router = useRouter();
  const { email } = router.query;

  // If no email in query params, redirect back to forgot-password
  useEffect(() => {
    if (!email && router.isReady) {
      router.push("/forgot-password");
    }
  }, [email, router.isReady]);

  return (
    <Auth>
      <div className="block w-full max-w-md rounded border bg-white px-10 pt-14 pb-12 shadow-md">
        <h1 className="mt-3 text-2xl font-semibold">
          Enviamos la nueva contraseña a tu correo
        </h1>
        <p className="mt-4 text-secondary">
          Utiliza la nueva contraseña para iniciar sesión. Te recomendamos cambiarla después de iniciar sesión por motivos de seguridad. No olvides revisar tu carpeta de Correos no deseados o Spam.
        </p>

        <Link
          href="/"
          className="mt-16 block w-full rounded-xl bg-primary py-5 px-4 text-center text-base text-white ring-primary focus:outline-none focus:ring-2 focus:ring-offset-2"
        >
          Volver a Iniciar Sesión
        </Link>

        <div className="mt-9 flex justify-center">
          <ResendBtn email={email} />
        </div>
      </div>
    </Auth>
  );
};

export default PasswordSent;

