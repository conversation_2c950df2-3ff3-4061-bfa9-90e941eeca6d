import React, { createElement, useState } from "react";
import InputGroup from "../utility/InputGroup";
import Modal from "../utility/Modal";
import { useForm } from "react-hook-form";
import useAxios from "@/hooks/useAxios";
import { toast } from "react-hot-toast";

const PasswordFormModalBtn = ({ as = "button", className = "", currentAdmin, ...props }) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { register, handleSubmit, reset, formState: { errors }, watch } = useForm();
  const { axios } = useAxios();

  const close = () => {
    setOpen(false);
    reset();
  };

  const onSubmit = async (data) => {
    if (!currentAdmin) {
      toast.error('No se pudo identificar el administrador');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post('/adminside/api/roles/admin/change-password/', {
        admin_id: currentAdmin.id,
        current_password: data.current_password,
        new_password: data.new_password,
        confirm_password: data.confirm_password
      });

      if (response.data.success) {
        toast.success(response.data.message);
        close();
      } else {
        toast.error(response.data.message || 'Error al cambiar la contraseña');
      }
    } catch (error) {
      const errorMessage = error?.response?.data?.message ||
                         error?.response?.data?.msg ||
                         'Error al cambiar la contraseña';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Watch password fields for validation
  const newPassword = watch("new_password");
  const confirmPassword = watch("confirm_password");

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-sm overflow-hidden bg-white shadow-xl"
      >
        <Modal.Wrapper as="form" onSubmit={handleSubmit(onSubmit)}>
          {/* Header */}
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Actualizar contraseña</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="space-y-7">
            <div>
              <InputGroup.Label>Contraseña actual</InputGroup.Label>
              <InputGroup isInvalid={!!errors.current_password}>
                <InputGroup.Input
                  type="password"
                  {...register("current_password", {
                    required: "La contraseña actual es requerida"
                  })}
                />
              </InputGroup>
              {errors.current_password && (
                <p className="text-red-600 text-sm mt-1">{errors.current_password.message}</p>
              )}
            </div>
            <hr className="my-5" />
            <div>
              <InputGroup.Label>Nueva contraseña</InputGroup.Label>
              <InputGroup isInvalid={!!errors.new_password}>
                <InputGroup.Input
                  type="password"
                  {...register("new_password", {
                    required: "La nueva contraseña es requerida",
                    minLength: {
                      value: 6,
                      message: "La contraseña debe tener al menos 6 caracteres"
                    }
                  })}
                />
              </InputGroup>
              {errors.new_password && (
                <p className="text-red-600 text-sm mt-1">{errors.new_password.message}</p>
              )}
            </div>
            <div>
              <InputGroup.Label>Confirmar contraseña</InputGroup.Label>
              <InputGroup isInvalid={!!errors.confirm_password}>
                <InputGroup.Input
                  type="password"
                  {...register("confirm_password", {
                    required: "Confirme la nueva contraseña",
                    validate: value => value === newPassword || "Las contraseñas no coinciden"
                  })}
                />
              </InputGroup>
              {errors.confirm_password && (
                <p className="text-red-600 text-sm mt-1">{errors.confirm_password.message}</p>
              )}
            </div>
          </Modal.Body>

          {/* Footer */}
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn
              type="button"
              onClick={close}
              className="bg-white text-black"
              disabled={loading}
            >
              Cancelar
            </Modal.FooterBtn>
            <Modal.FooterBtn
              type="submit"
              className="bg-black text-white"
              disabled={loading}
            >
              {loading ? 'Guardando...' : 'Guardar'}
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        type: "button",
        onClick: () => setOpen(true),
        className: className,
        ...props,
      })}
    </>
  );
};

export default PasswordFormModalBtn;
