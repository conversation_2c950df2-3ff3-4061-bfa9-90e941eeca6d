import React, { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import useAxios from "@/hooks/useAxios";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const options = {
  responsive: true,
  interaction: {
    intersect: false,
  },
  scales: {
    x: {
      border: {
        display: false,
      },
      ticks: {
        font: {
          size: 14,
          weight: 700,
          color: "black",
        },
      },
      grid: {
        display: false,
      },
    },
  },
  plugins: {
    legend: {
      display: false,
    },
  },
};

const labels = [
  "ENE",
  "FEB",
  "MAR",
  "ABR",
  "MAY",
  "JUN",
  "JUL",
  "AGO",
  "SEP",
  "OCT",
  "NOV",
  "DIC",
  "ENE",
];

const UserCountBarChart = () => {
  const [chartData, setChartData] = useState({
    labels,
    datasets: [
      {
        label: "Escudos",
        data: Array(13).fill(0),
        backgroundColor: labels.map((label, index, row) =>
          index + 1 === row.length ? "black" : "#1555ED"
        ),
      },
    ],
  });

  const [totalShields, setTotalShields] = useState(0);
  const { axios } = useAxios();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get("/api/dashboard/created-shields-per-month/");
        if (response.data && response.data.data) {
          const shieldsData = response.data.data;

          // Calculate total shields
          let total = 0;
          const monthlyData = Array(13).fill(0);

          // Map API data to chart data
          shieldsData.forEach(item => {
            const month = parseInt(item.month);
            if (month >= 1 && month <= 12) {
              monthlyData[month-1] = item.count;
              total += item.count;
            }
          });

          // Add January of next year as the last point (for continuity)
          monthlyData[12] = monthlyData[0];

          setTotalShields(total);
          setChartData({
            labels,
            datasets: [
              {
                label: "Escudos",
                data: monthlyData,
                backgroundColor: labels.map((label, index, row) =>
                  index + 1 === row.length ? "black" : "#1555ED"
                ),
              },
            ],
          });
        }
      } catch (error) {
        console.error("Error fetching shields chart data:", error);
        setTotalShields(0);
      }
    };

    fetchData();
  }, []);

  return (
    <div>
      <h3 className="text-right mb-2">Total Escudos: {totalShields}</h3>
      <Bar options={options} data={chartData} />
    </div>
  );
};

export default UserCountBarChart;
