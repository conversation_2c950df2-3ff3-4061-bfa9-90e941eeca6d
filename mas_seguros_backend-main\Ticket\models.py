import random

from django.db import models

# Create your models here.

from tinymce import models as tinymce_models
from Account.models import UserProfile


# Create your models here.

# random_number = random.randint(0, 99999)
class TicketSubject(models.Model):
    title = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    description = tinymce_models.HTMLField(null=True, blank=True)
    resolved = models.BooleanField(default=False, null=True, blank=True)

    def __str__(self):
        return self.title


class Ticket(models.Model):
    description = tinymce_models.HTMLField(null=True, blank=True)
    ticket_description = models.TextField(null=True, blank=True)
    title = models.ForeignKey(TicketSubject, on_delete=models.SET_NULL, null=True, blank=True)
    resolved = models.BooleanField(default=False, null=True, blank=True)
    ticket_num = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    user = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"Ticket #{self.id} - {self.title.title if self.title else 'No Title'}"


class TicketMessage(models.Model):
    ticket = models.ForeignKey(Ticket, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True)
    message = models.TextField()
    is_admin = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender} on Ticket #{self.ticket.id}"
