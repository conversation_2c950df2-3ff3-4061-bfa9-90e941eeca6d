import React, { useState } from "react";
import { useRouter } from "next/router";
import { useQuery } from "react-query";
import UserLayout from "@/components/layouts/UserLayout";
import InputGroup from "@/components/utility/InputGroup";
import Table from "@/components/Table";
import ViewPhotoBtn from "@/components/ViewPhotoBtn";
import DownloadReportModal from "@/components/biometric/DownloadReportModal";
import useAxios from "@/hooks/useAxios";
import { format } from "date-fns";
import { toast } from "react-hot-toast";

const Biometric = () => {
  const router = useRouter();
  const { user_id } = router.query;
  const { axios } = useAxios();
  const [searchDate, setSearchDate] = useState("");
  const [filteredData, setFilteredData] = useState([]);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const fetchBiometricData = async () => {
    if (!user_id) return [];
    try {
      // Use the optimized endpoint for better performance
      const response = await axios.get(`adminside/api/shield/user-biometrics-optimized/`, {
        params: { user_id }
      });

      const biometrics = response.data?.data || [];
      console.log(`Fetched ${biometrics.length} biometric records for user ${user_id}`);

      return biometrics;
    } catch (error) {
      console.error("Error fetching biometric data:", error);

      // Fallback to the old method if the new endpoint fails
      try {
        console.log("Falling back to multi-shield fetch method...");
        const shieldsResponse = await axios.get(`adminside/api/dashboard/user-shields/`, {
          params: { user_id }
        });

        const shields = shieldsResponse.data?.data || [];
        let allBiometrics = [];

        for (const shield of shields) {
          try {
            const biometricResponse = await axios.get(`adminside/api/shield/shield-biometrics/`, {
              params: { id: shield.id }
            });
            const biometrics = biometricResponse.data?.data || [];
            const userBiometrics = biometrics.filter(bio => bio.userprofile?.user?.id == user_id);
            allBiometrics = [...allBiometrics, ...userBiometrics];
          } catch (error) {
            console.error(`Error fetching biometrics for shield ${shield.id}:`, error);
          }
        }

        return allBiometrics;
      } catch (fallbackError) {
        console.error("Fallback method also failed:", fallbackError);
        return [];
      }
    }
  };

  const { data: biometricData = [], isLoading, isError } = useQuery(
    [`user-${user_id}-biometric`],
    fetchBiometricData,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  // Fetch user data for the download modal
  const fetchUserData = async () => {
    if (!user_id) return null;
    try {
      const response = await axios.get(`adminside/api/roles/userprofile/?id=${user_id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user data:", error);
      return null;
    }
  };

  const { data: userData } = useQuery(
    [`user-${user_id}-profile`],
    fetchUserData,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  const handleSearch = () => {
    if (!searchDate) {
      setFilteredData([]);
      setHasSearched(false);
      return;
    }

    setHasSearched(true);

    try {
      const filtered = biometricData.filter(bio => {
        if (!bio.created_at) return false;

        try {
          const bioDate = new Date(bio.created_at);
          if (isNaN(bioDate.getTime())) return false;

          const bioDateFormatted = format(bioDate, 'yyyy-MM-dd');
          return bioDateFormatted === searchDate;
        } catch (error) {
          console.error("Error parsing date:", error);
          return false;
        }
      });

      setFilteredData(filtered);
    } catch (error) {
      console.error("Error filtering data:", error);
      setFilteredData([]);
    }
  };

  // Clear search function
  const clearSearch = () => {
    setSearchDate('');
    setFilteredData([]);
    setHasSearched(false);
  };

  // Determine what data to display - this is the key fix
  const displayData = hasSearched && searchDate ? filteredData : biometricData;

  return (
    <UserLayout pageTitle="Usuarios" headerTitle="Usuarios">
      <div className="mt-5 space-y-6">
        <div className="flex items-center gap-2 text-sm">
          <span>Buscar</span>
          <div>
            <InputGroup>
              <InputGroup.Input
                type="date"
                className="!border-none bg-accent"
                value={searchDate}
                onChange={(e) => {
                  const newDate = e.target.value;
                  setSearchDate(newDate);
                  // If date is cleared manually, reset search
                  if (!newDate) {
                    clearSearch();
                  }
                }}
              />
            </InputGroup>
          </div>
          <button
            onClick={handleSearch}
            className="self-stretch rounded bg-primary px-3 font-medium text-white ring-offset-2 focus:ring-2"
          >
            Buscar
          </button>

          <button
            onClick={() => setShowDownloadModal(true)}
            className="flex items-center gap-2 self-stretch rounded bg-white border border-gray-300 px-3 font-medium text-black hover:bg-gray-50 ring-offset-2 focus:ring-2"
          >
            <svg
              className="w-4 h-4 opacity-100"
              viewBox="0 0 16 16"
              fill="currentColor"
            >
              <path
                d="M8 2v8l3-3h-2V2H6v5H4l4 4 4-4H8z"
                fill="currentColor"
              />
              <rect x="2" y="13" width="12" height="2" fill="currentColor"/>
            </svg>
            Descargar reporte
          </button>
        </div>

        <div className="overflow-x-auto w-full">
          {/* Show a helper message on small screens */}
          <div className="block sm:hidden text-xs text-gray-500 mb-2">Desliza hacia la derecha para ver toda la tabla</div>
          <Table className="min-w-full">
            <Table.Thead>
              <Table.Tr>
                <Table.Th className="min-w-[80px]">ID</Table.Th>
                <Table.Th className="min-w-[200px]">Nombre</Table.Th>
                <Table.Th className="min-w-[120px]">Horario</Table.Th>
                <Table.Th className="min-w-[220px]">Ubicación</Table.Th>
                <Table.Th className="min-w-[100px]">Tipo</Table.Th>
                <Table.Th className="min-w-[140px]">Archivo adjunto</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {isLoading ? (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center py-4">
                    Cargando datos biométricos...
                  </Table.Td>
                </Table.Tr>
              ) : isError ? (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center py-4 text-red-500">
                    Error al cargar los datos biométricos
                  </Table.Td>
                </Table.Tr>
              ) : displayData.length > 0 ? (
                displayData.map((bio, index) => (
                  <Table.Tr key={bio.id || index}>
                    <Table.Td className="font-semibold">
                      #{bio.biometric_code || `B${bio.id}`}
                    </Table.Td>
                    <Table.Td className="flex items-center gap-4">
                      <img
                        src={
                          bio.userprofile?.image_url
                            ? (bio.userprofile.image_url.startsWith('http')
                                ? bio.userprofile.image_url
                                : `${process.env.NEXT_PUBLIC_BACKEND_URL}${bio.userprofile.image_url}`)
                            : "/assets/img/default-profile-pic-1.jpg"
                        }
                        alt=""
                        className="block aspect-square w-11 rounded-full object-cover"
                        onError={(e) => {
                          e.target.src = "/assets/img/default-profile-pic-1.jpg";
                        }}
                      />
                      <div>
                        <p className="font-medium">{bio.userprofile?.full_name || "Usuario"}</p>
                        <p className="text-sm text-gray-600">UI{bio.userprofile?.id || bio.userprofile?.user?.id}</p>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <dd>{bio.created_at ? format(new Date(bio.created_at), 'HH:mm') : '--'} hrs</dd>
                      <dd>{bio.created_at ? format(new Date(bio.created_at), 'dd/MM/yyyy') : '--'}</dd>
                    </Table.Td>
                    <Table.Td>
                      <dd>{bio.address || 'Ubicación no disponible'}</dd>
                      <dd className="font-semibold">
                        {bio.lat && bio.long ? `${bio.lat}, ${bio.long}` : 'Coordenadas no disponibles'}
                      </dd>
                    </Table.Td>
                    <Table.Td>
                      <span className={`inline-flex items-center rounded-full px-3 py-1.5 text-sm font-semibold ${
                        bio.type === 'ENTRADA'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        <svg
                          className={`mr-1.5 h-2 w-2 ${
                            bio.type === 'ENTRADA' ? 'text-green-800' : 'text-red-800'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 8 8"
                        >
                          <circle cx={5} cy={4} r={3} />
                        </svg>
                        {bio.type || 'SALIDA'}
                      </span>
                    </Table.Td>
                    <Table.Td>
                      {bio.image_url ? (
                        <ViewPhotoBtn
                          headerTitle={`Biométrico #${bio.biometric_code || bio.id}`}
                          user={{
                            id: bio.userprofile?.id || bio.userprofile?.user?.id,
                            name: bio.userprofile?.full_name || "Usuario",
                            full_name: bio.userprofile?.full_name || "Usuario",
                            user_id: bio.userprofile?.user?.id,
                            biometric_code: bio.biometric_code,
                            userprofile: bio.userprofile
                          }}
                          evidenceImage={bio.image_url}
                          className="font-semibold text-primary hover:underline"
                        >
                          Ver foto
                        </ViewPhotoBtn>
                      ) : (
                        <span className="text-gray-500">Sin foto</span>
                      )}
                    </Table.Td>
                  </Table.Tr>
                ))
              ) : (
                <Table.Tr>
                  <Table.Td colSpan={6} className="text-center py-4">
                    {hasSearched && searchDate ? "No se encontraron registros para la fecha seleccionada" : "No hay registros biométricos disponibles"}
                  </Table.Td>
                </Table.Tr>
              )}
            </Table.Tbody>
          </Table>
        </div>
      </div>

      {/* Download Report Modal */}
      <DownloadReportModal
        open={showDownloadModal}
        close={() => setShowDownloadModal(false)}
        userData={userData?.data}
        biometricData={biometricData}
      />
    </UserLayout>
  );
};

export default Biometric;
