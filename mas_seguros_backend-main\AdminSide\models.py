from django.db import models
from Account import models as account_models

# Create your models here.

class AdminPermission(models.Model):
    """Model to handle admin permissions for different sections"""
    admin = models.OneToOneField(account_models.UserProfile, on_delete=models.CASCADE, related_name='admin_permissions')
    
    # Section permissions
    users_access = models.BooleanField(default=False, help_text="Access to Users section")
    shields_access = models.BooleanField(default=False, help_text="Access to Shields section")
    alerts_sos_access = models.BooleanField(default=False, help_text="Access to Alerts and SOS section")
    payment_history_access = models.BooleanField(default=False, help_text="Access to Payment History section")
    support_access = models.BooleanField(default=False, help_text="Access to Support section")
    roles_access = models.BooleanField(default=False, help_text="Access to Roles section")
    
    # Full access flag
    full_access = models.<PERSON>oleanField(default=False, help_text="Full access to all sections")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Permissions for {self.admin.full_name}"
    
    def save(self, *args, **kwargs):
        # If full access is granted, enable all section permissions
        if self.full_access:
            self.users_access = True
            self.shields_access = True
            self.alerts_sos_access = True
            self.payment_history_access = True
            self.support_access = True
            self.roles_access = True
        super().save(*args, **kwargs)
