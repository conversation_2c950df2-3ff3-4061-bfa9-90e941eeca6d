from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth.models import User
from Shield import models as shield_models


def check_member_in_shield(ShieldPointOfInterest):
    def decorator(request, *args, **kwargs):
        # shield_member = shield.members.filter(user=user)
        print("this is request=", request.data
              )
        print("this is user=", request.user)
        # if shield_member.exists():
        #     print("exist")
        #     return True
        # else:
        #     print("not exist")
        #     return False

    return decorator
