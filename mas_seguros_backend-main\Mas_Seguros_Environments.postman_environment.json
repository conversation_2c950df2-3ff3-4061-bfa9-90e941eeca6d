{"id": "mas-seguros-environments", "name": "Mas Seguros Environments", "values": [{"key": "base_url", "value": "http://************:8000", "description": "Production server URL", "type": "default", "enabled": true}, {"key": "local_url", "value": "http://127.0.0.1:8000", "description": "Local development server URL", "type": "default", "enabled": false}, {"key": "staging_url", "value": "https://masseguros.limacreativa.com", "description": "Staging server URL", "type": "default", "enabled": false}, {"key": "auth_token", "value": "", "description": "Authentication token obtained from login", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "description": "Current user ID", "type": "default", "enabled": true}, {"key": "shield_id", "value": "", "description": "Current shield ID for testing", "type": "default", "enabled": true}, {"key": "alert_id", "value": "", "description": "Current alert ID for testing", "type": "default", "enabled": true}, {"key": "sos_id", "value": "", "description": "Current SOS ID for testing", "type": "default", "enabled": true}, {"key": "ticket_id", "value": "", "description": "Current ticket ID for testing", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}