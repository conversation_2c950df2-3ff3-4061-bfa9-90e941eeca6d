import React from "react";
import Admin from "@/components/layouts/Admin";
import ShieldsTable from "@/components/shields/ShieldsTable";
import useTableData from "@/hooks/useTableData";
import Pagination from "@/components/Pagination";
import TopBar from "@/components/shields/TopBar";


const pageSize = 10

export default function Shields() {
  const {
    search,
    setSearch,
    currentTableData,
    tempFilters,
    setTempFilters,
    applyFilters,
    isLoading,
    isError,
    error,
    sort,
    setSort,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess,
    resetPage
  } = useTableData({
    dataUrl: "adminside/api/shield/all-shields/",
    pageSize: pageSize,
    queryKeys: ["shields-table-data"],
    dataCallback: (response) => {
      // The API returns shields data in response.data (not nested)
      // Handle both possible response structures for robustness
      if (response?.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response?.data?.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }
      return [];
    },
  })

  return (
    <Admin pageTitle="Escudos" headerTitle="Escudos">
      <TopBar
        search={search}
        setSearch={setSearch}
        resetPage={resetPage}
        tempFilters={tempFilters}
        setTempFilters={setTempFilters}
        applyFilters={applyFilters}
        allData={allData}
        currentData={currentTableData}
      />

      <div className="container-padding">
        <ShieldsTable
          shields={currentTableData}
          isLoading={isLoading}
          isError={isError}
          error={error}
          sort={sort}
          setSort={setSort}
        />
        {/* <SamplePagination /> */}
        {isSuccess && (
          <div className="mt-3.5 -translate-y-24">
            <Pagination
              totalCount={allData.length}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={setCurrentPage}
            />
          </div>
        )}
      </div>
    </Admin>
  );
}
