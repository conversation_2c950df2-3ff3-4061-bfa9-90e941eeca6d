import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  doc,
  addDoc,
  updateDoc,
  onSnapshot,
  query,
  serverTimestamp,
  getDocs,
  writeBatch,
  where,
  limit
} from 'firebase/firestore';

// Firebase configuration - Replace with your Firebase project details
const firebaseConfig = {
  apiKey: "AIzaSyAvWjgFutpCvylALfQ3iUUlBrRVF6CZChM",
  authDomain: "mas-seguros-acc65.firebaseapp.com",
  projectId: "mas-seguros-acc65",
  storageBucket: "mas-seguros-acc65.appspot.com",
  messagingSenderId: "56127803396",
  appId: "1:56127803396:web:f366133a9021677e2c6551",
  measurementId: "G-EP6NBQBX06"
};

// Collection names
const ROOMS_COLLECTION = 'rooms';
const MESSAGES_SUBCOLLECTION = 'messages';

// Room types
const ROOM_TYPE_TICKET = 'ticket';
const ROOM_TYPE_GROUP = 'group';

// Admin ID (used to identify admin messages)
const ADMIN_ID = '1';

// Initialize Firebase
let app;
let db;

try {
  app = initializeApp(firebaseConfig);
  db = getFirestore(app);
  console.log("Firebase Firestore initialized successfully");
} catch (error) {
  console.error("Error initializing Firebase:", error);
}

// Utility to normalize Firestore message documents
function normalizeMessage(doc) {
  const data = doc.data();
  const type = data.type || (data.message && typeof data.message === 'object' && data.message.type) || 'text';
  let content = '';
  let audioUrl = '';
  let sosData = null;
  let alertData = null;
  let sender = { name: 'Usuario', role: 'user' };
  let createdAt = data.createdAt || data.created_at || data.timestamp || null;

  // Convert Firestore timestamp to ISO string
  if (createdAt && typeof createdAt.toDate === 'function') {
    createdAt = createdAt.toDate().toISOString();
  } else if (typeof createdAt === 'number') {
    createdAt = new Date(createdAt).toISOString();
  } else if (typeof createdAt === 'string') {
    createdAt = new Date(createdAt).toISOString();
  }

  // Sender
  if (data.senderDetails && data.senderDetails.full_name) {
    sender = { name: data.senderDetails.full_name, role: data.senderDetails.role || 'user' };
  } else if (data.sender_name) {
    sender = { name: data.sender_name, role: data.is_admin ? 'admin' : 'user' };
  }

  // Content by type
  switch (type) {
    case 'voice':
      audioUrl = data.audioUrl || data.voiceUrl || data.url || (data.message && (data.message.audioUrl || data.message.voiceUrl || data.message.url)) || '';
      content = audioUrl;
      break;
    case 'sos':
      sosData = data.sos || data.message || data; // SOS data could be at root, under message, or the whole object
      content = sosData;
      break;
    case 'alert':
      alertData = data.alert || data.message || data; // Alert data could be at root, under message, or the whole object
      content = alertData;
      break;
    case 'text':
    default:
      // Prioritize specific content fields, then fallback to whole object if it contains a content field
      if (data.message && typeof data.message.content === 'string') {
        content = data.message.content;
      } else if (typeof data.content === 'string') {
        content = data.content;
      } else if (data.message && typeof data.message === 'object' && typeof data.message.content !== 'object') { // Handle cases where 'message' itself is the content and not an object with content
         content = JSON.stringify(data.message);
      } else if (typeof data === 'object' && typeof data.content !== 'object') { // Handle cases where the whole document is the content and not an object with content
         content = JSON.stringify(data);
      }
      // If still no string content, leave as empty or a default
      if (typeof content !== 'string') {
          content = ''; // Or a default like 'Mensaje no legible'
      }
      break;
  }

  // Ensure sender is always a valid object with a role
  const safeSender = (sender && typeof sender === 'object' && sender.role !== undefined)
    ? sender
    : { name: 'Usuario Desconocido', role: 'user' }; // Fallback sender

  return {
    id: doc.id,
    type,
    content,
    sender: safeSender, // Use the safe sender object
    createdAt,
    raw: data,
  };
}

/**
 * Service for handling Firebase Firestore operations
 */
class FirebaseService {
  /**
   * Find a room by ticket ID
   * @param {string} ticketId - Ticket ID
   * @returns {Promise<string|null>} - Room ID or null if not found
   */
  static async findRoomByTicketId(ticketId) {
    if (!db) {
      console.error("Firebase Firestore not initialized");
      return null;
    }
    try {
      console.log(`Finding room for ticket ${ticketId}`);
      const roomsRef = collection(db, ROOMS_COLLECTION);
      const q = query(
        roomsRef,
        where("type", "==", ROOM_TYPE_TICKET),
        where("tId", "==", parseInt(ticketId, 10))
      );
      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) {
        console.log(`No room found for ticket ${ticketId}, creating new room`);
        const newRoomRef = await addDoc(roomsRef, {
          type: ROOM_TYPE_TICKET,
          tId: parseInt(ticketId, 10),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          lastMessage: null
        });
        return newRoomRef.id;
      }
      const roomId = querySnapshot.docs[0].id;
      console.log(`Found room ${roomId} for ticket ${ticketId}`);
      return roomId;
    } catch (error) {
      console.error(`Error finding/creating room for ticket ${ticketId}:`, error);
      return null;
    }
  }

  /**
   * Find a room by shield ID
   * @param {string} shieldId - Shield ID
   * @returns {Promise<string|null>} - Room ID or null if not found
   */
  static async findRoomByShieldId(shieldId) {
    if (!db) {
      console.error("Firebase Firestore not initialized");
      return null;
    }

    try {
      console.log(`Finding room for shield ${shieldId}`);

      // Query rooms collection for a room with this shieldId
      const roomsRef = collection(db, ROOMS_COLLECTION);
      const q = query(
        roomsRef,
        where("type", "==", ROOM_TYPE_GROUP),
        where("shieldId", "==", shieldId.toString())
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        console.log(`No room found for shield ${shieldId}, creating new room`);
        // Create a new room for this shield
        const newRoomRef = await addDoc(roomsRef, {
          type: ROOM_TYPE_GROUP,
          shieldId: shieldId.toString(),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          lastMessage: null
        });
        return newRoomRef.id;
      }

      // Return the first matching room ID
      const roomId = querySnapshot.docs[0].id;
      console.log(`Found room ${roomId} for shield ${shieldId}`);
      return roomId;
    } catch (error) {
      console.error(`Error finding/creating room for shield ${shieldId}:`, error);
      return null;
    }
  }

  /**
   * Subscribe to room messages in real-time
   * @param {string} roomId - Room ID
   * @param {function} callback - Callback function to handle new messages
   * @returns {function} - Unsubscribe function
   */
  static subscribeToRoomMessages(roomId, callback) {
    if (!db) {
      console.error("Firebase Firestore not initialized");
      return () => {};
    }
    try {
      const messagesCollection = collection(db, ROOMS_COLLECTION, roomId, MESSAGES_SUBCOLLECTION);
      const messagesQuery = query(messagesCollection, limit(100)); // Limit to last 100 for performance

      const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
        // Process document changes incrementally
        const changes = snapshot.docChanges();
        const normalizedChanges = changes.map(change => ({
          type: change.type, // 'added', 'modified', or 'removed'
          doc: normalizeMessage(change.doc), // Normalized message data
          oldIndex: change.oldIndex, // Useful for reordering
          newIndex: change.newIndex // Useful for reordering
        }));

        // Pass normalized changes and the full snapshot (for initial data) to the callback
        callback(normalizedChanges, snapshot.docs.map(normalizeMessage));

      }, (error) => {
        console.error(`Error subscribing to room messages for room ${roomId}:`, error);
      });
      return unsubscribe;
    } catch (error) {
      console.error(`Error setting up message subscription for room ${roomId}:`, error);
      return () => {};
    }
  }

  /**
   * Add a new message to a room
   * @param {string} roomId - Room ID
   * @param {object} message - Message object
   * @returns {Promise} - Promise that resolves when the message is added
   */
  static async addRoomMessage(roomId, message) {
    if (!db) {
      console.error("Firebase Firestore not initialized");
      return null;
    }

    try {
      console.log(`Adding message to Firestore for room ${roomId}`);

      // Validate message object
      if (typeof message !== 'object' || message === null) {
        console.error("Invalid message object:", message);
        // Create a default message object in the expected format
        message = {
          message: {
            content: typeof message === 'string' ? message : "Invalid message",
            senderId: ADMIN_ID,
            receiverId: "0",
            messageSent: true,
            messageDelivered: false,
            messageSeen: false,
            id: null
          },
          senderDetails: {
            full_name: "Administrador",
            role: "Admin"
          },
          type: "text",
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          seenUserIds: [parseInt(ADMIN_ID, 10)]
        };
      }

      // Create a reference to the messages collection for this room
      const messagesCollection = collection(db, ROOMS_COLLECTION, roomId, MESSAGES_SUBCOLLECTION);

      // Add the message to Firestore
      const docRef = await addDoc(messagesCollection, message);
      console.log(`Message successfully added to Firestore for room ${roomId} with ID: ${docRef.id}`);

      // Update the lastMessage field in the room document
      try {
        const roomRef = doc(db, ROOMS_COLLECTION, roomId);
        const lastMessageData = {
          lastMessage: {
            createdAt: serverTimestamp(),
            message: message.message,
            senderDetails: message.senderDetails,
            type: message.type,
            updatedAt: serverTimestamp()
          }
        };

        // Update the room document with the last message
        await updateDoc(roomRef, lastMessageData);
        console.log(`Updated lastMessage for room ${roomId}`);
      } catch (updateError) {
        console.error(`Error updating lastMessage for room ${roomId}:`, updateError);
      }

      return docRef;
    } catch (error) {
      console.error(`Error adding message to Firestore for room ${roomId}:`, error);
      return null;
    }
  }

  /**
   * Subscribe to shield chat messages in real-time
   * @param {string} shieldId - Shield ID
   * @param {function} callback - Callback function to handle new messages
   * @returns {function} - Unsubscribe function
   */
  static async subscribeToShieldMessages(shieldId, callback) {
    console.log(`Setting up subscription for shield ${shieldId}`);

    try {
      // Find or create the room ID for this shield
      const roomId = await this.findRoomByShieldId(shieldId);

      if (!roomId) {
        console.log(`No room found/created for shield ${shieldId}, cannot subscribe to messages`);
        // Always call callback with empty array to ensure UI updates - adjust for new callback signature
        callback([], []); // Pass empty changes and empty full snapshot
        return () => {};
      }

      // Subscribe to messages for this room, passing the component's callback
      const unsubscribe = this.subscribeToRoomMessages(roomId, callback);

      return unsubscribe;
    } catch (error) {
      console.error(`Error setting up subscription for shield ${shieldId}:`, error);
      // Always call callback with empty array to ensure UI updates - adjust for new callback signature
      callback([], []); // Pass empty changes and empty full snapshot
      return () => {};
    }
  }

  /**
   * Add a new message to a shield chat
   * @param {string} shieldId - Shield ID
   * @param {object} message - Message object
   * @returns {Promise} - Promise that resolves when the message is added
   */
  static async addShieldMessage(shieldId, message) {
    try {
      const roomId = await this.findRoomByShieldId(shieldId);
      if (!roomId) return null;

      let formattedMessage;
      switch (message.type) {
        case 'voice':
          formattedMessage = {
            type: 'voice',
            audioUrl: message.content,
            senderDetails: { full_name: 'Administrador', role: 'Admin' },
            createdAt: serverTimestamp(),
          };
          break;
        case 'sos':
          formattedMessage = {
            type: 'sos',
            sos: message.content,
            senderDetails: { full_name: 'Administrador', role: 'Admin' },
            createdAt: serverTimestamp(),
          };
          break;
        case 'alert':
          formattedMessage = {
            type: 'alert',
            alert: message.content,
            senderDetails: { full_name: 'Administrador', role: 'Admin' },
            createdAt: serverTimestamp(),
          };
          break;
        case 'text':
        default:
          formattedMessage = {
            type: 'text',
            message: { content: message.content, senderId: ADMIN_ID },
            senderDetails: { full_name: 'Administrador', role: 'Admin' },
            createdAt: serverTimestamp(),
          };
          break;
      }
      return this.addRoomMessage(roomId, formattedMessage);
    } catch (error) {
      console.error(`Error adding message for shield ${shieldId}:`, error);
      return null;
    }
  }

  /**
   * Clean up old messages for a room
   * @param {string} roomId - Room ID
   * @param {number} olderThanDays - Delete messages older than this many days (default: 30)
   * @returns {Promise} - Promise that resolves when cleanup is complete
   */
  static async cleanupOldMessages(roomId, olderThanDays = 30) {
    if (!db) {
      console.error("Firebase Firestore not initialized");
      return null;
    }

    try {
      // Calculate the cutoff date
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // Create a reference to the messages collection for this room
      const messagesCollection = collection(db, ROOMS_COLLECTION, roomId, MESSAGES_SUBCOLLECTION);

      // Get all messages
      const messagesQuery = query(messagesCollection);
      const messagesSnapshot = await getDocs(messagesQuery);

      // Delete old messages
      const batch = writeBatch(db);
      let deletedCount = 0;

      messagesSnapshot.forEach((doc) => {
        const messageData = doc.data();

        // Check if the message has a valid timestamp
        if (messageData.created_at && typeof messageData.created_at.toDate === 'function') {
          const messageDate = messageData.created_at.toDate();

          // If the message is older than the cutoff date, delete it
          if (messageDate < cutoffDate) {
            batch.delete(doc.ref);
            deletedCount++;
          }
        } else {
          // If there's no valid timestamp, delete it anyway (it's probably old or invalid)
          batch.delete(doc.ref);
          deletedCount++;
        }
      });

      // Commit the batch delete
      if (deletedCount > 0) {
        await batch.commit();
        console.log(`Deleted ${deletedCount} old messages for room ${roomId}`);
      }

      return deletedCount;
    } catch (error) {
      console.error("Error cleaning up old messages:", error);
      return null;
    }
  }
  /**
   * Subscribe to ticket messages in real-time
   * @param {string} ticketId - Ticket ID
   * @param {function} callback - Callback function to handle new messages
   * @returns {function} - Unsubscribe function
   */
  static async subscribeToTicketMessages(ticketId, callback) {
    console.log(`Setting up subscription for ticket ${ticketId}`);

    try {
      // Find the room ID for this ticket
      const roomId = await this.findRoomByTicketId(ticketId);

      if (!roomId) {
        console.log(`No room found for ticket ${ticketId}, cannot subscribe to messages`);
        // Always call callback with empty array to ensure UI updates - adjust for new callback signature
        callback([], []); // Pass empty changes and empty full snapshot
        return () => {};
      }

      // Subscribe to messages for this room, passing the component's callback
      const unsubscribe = this.subscribeToRoomMessages(roomId, callback);

      return unsubscribe;
    } catch (error) {
      console.error(`Error setting up subscription for ticket ${ticketId}:`, error);
      // Always call callback with empty array to ensure UI updates - adjust for new callback signature
      callback([], []); // Pass empty changes and empty full snapshot
      return () => {};
    }
  }

  /**
   * Add a new message to a ticket
   * @param {string} ticketId - Ticket ID
   * @param {object} message - Message object
   * @returns {Promise} - Promise that resolves when the message is added
   */
  static async addTicketMessage(ticketId, message) {
    console.log(`Adding message for ticket ${ticketId}`);

    try {
      // Find the room ID for this ticket
      const roomId = await this.findRoomByTicketId(ticketId);

      if (!roomId) {
        console.log(`No room found for ticket ${ticketId}, cannot add message`);
        return null;
      }

      // Format the message for the room structure
      const formattedMessage = {
        message: {
          content: message.message,
          senderId: ADMIN_ID,
          receiverId: ticketId.toString(),
          messageSent: true,
          messageDelivered: false,
          messageSeen: false,
          id: null
        },
        senderDetails: {
          full_name: "Administrador",
          role: "Admin"
        },
        type: "text",
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        seenUserIds: [parseInt(ADMIN_ID, 10)]
      };

      // Add the message to the room
      return this.addRoomMessage(roomId, formattedMessage);
    } catch (error) {
      console.error(`Error adding message for ticket ${ticketId}:`, error);
      return null;
    }
  }
}

export default FirebaseService;
