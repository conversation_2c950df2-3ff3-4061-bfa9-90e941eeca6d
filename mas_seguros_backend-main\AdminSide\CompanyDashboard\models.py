from django.db import models
from Account import models as account_models
from Shield import models as shield_models
import random
from mas_seguros_backend import settings as backend_settings

def generate_company_code():
    unique_code = random.randint(0, 999999)
    return f'ESC{unique_code}'


# Create your models here.


class SuperAdmin(models.Model):
    userprofile = models.ForeignKey(account_models.UserProfile, on_delete=models.CASCADE, null=True, blank=True)


class CompanyProfileModel(models.Model):
    name = models.CharField(max_length=50, null=True, blank=True)
    admin = models.ManyToManyField(account_models.UserProfile, blank=True, related_name='admin_to_users')
    users = models.ManyToManyField(account_models.UserProfile, blank=True, related_name='users_to_users')
    shields = models.ManyToManyField(shield_models.ShieldModel, blank=True)
    # membership = models.ManyToManyField(membership_models.MembershipModel, blank=True)
    # promocode = models.ManyToManyField(admin_model.Promo_code, blank=True)
    image = models.ImageField(null=True, blank=True, upload_to='company_images')
    company_code = models.CharField(max_length=100, default=generate_company_code, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)  # Added state field
    suspended = models.BooleanField(null=True, blank=True, default=False)
    super_admin = models.ForeignKey(SuperAdmin, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    @property
    def image_url(self):
        try:
            return backend_settings.Base_url_path.format(url=self.image.url)
        except:
            return None
