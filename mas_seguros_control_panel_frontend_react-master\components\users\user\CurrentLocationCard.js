import React, { useEffect, useState } from "react";
import { useQuery } from "react-query";
import { format } from "date-fns";
import useAxios from "@/hooks/useAxios";
import GoogleMap from "@/components/maps/GoogleMap";
import { toast } from "react-hot-toast";

const CurrentLocationCard = ({ userId }) => {
  const { axios } = useAxios();
  const [mapError, setMapError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  const fetchUserData = async () => {
    if (!userId) return null;
    try {
      const response = await axios.get(`adminside/api/roles/userprofile/?id=${userId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user data:", error);
      return null;
    }
  };

  const fetchLatestLocation = async () => {
    if (!userId) return null;
    try {
      // Use shield endpoint to get Location objects with address data
      const response = await axios.get(`adminside/api/shield/shield-members-locations/`, {
        params: { member_id: userId }
      });
      // This endpoint returns Location objects directly (not wrapped in data)
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error("Error fetching location data:", error);
      return [];
    }
  };

  const fetchRouteData = async () => {
    if (!userId) return null;
    try {
      // Use dashboard endpoint to get Route objects for speed data
      const response = await axios.get(`adminside/api/dashboard/user-locations/`, {
        params: { user_id: userId }
      });
      return response.data?.data || [];
    } catch (error) {
      console.error("Error fetching route data:", error);
      return [];
    }
  };

  const { data: userData, isLoading: isLoadingUser, error: userError } = useQuery(
    [`user-${userId}-profile`],
    fetchUserData,
    {
      enabled: !!userId,
      refetchOnWindowFocus: false,
      onError: (error) => {
        console.error("Error loading user data:", error);
        toast.error("Error al cargar datos del usuario");
      }
    }
  );

  const { data: locationData, isLoading: isLoadingLocation, error: locationError } = useQuery(
    [`user-${userId}-latest-location`],
    fetchLatestLocation,
    {
      enabled: !!userId,
      refetchOnWindowFocus: false,
      refetchInterval: 30000, // Refresh every 30 seconds for real-time updates
      onError: (error) => {
        console.error("Error loading location data:", error);
        toast.error("Error al cargar datos de ubicación");
      }
    }
  );

  const { data: routeData, isLoading: isLoadingRoute, error: routeError } = useQuery(
    [`user-${userId}-route-data`],
    fetchRouteData,
    {
      enabled: !!userId,
      refetchOnWindowFocus: false,
      refetchInterval: 30000, // Refresh every 30 seconds for real-time updates
      onError: (error) => {
        console.error("Error loading route data:", error);
      }
    }
  );

  const user = userData?.data;
  const locations = Array.isArray(locationData) ? locationData : [];
  const routes = Array.isArray(routeData) ? routeData : [];
  const latestLocation = locations.length > 0 ? locations[locations.length - 1] : null;
  const latestRoute = routes.length > 0 ? routes[routes.length - 1] : null;

  const isLoading = isLoadingUser || isLoadingLocation || isLoadingRoute;
  const hasErrors = userError || locationError || routeError;

  // Get current location from latest location entry (with address) or user profile coordinates
  const currentLat = user?.lat;
  const currentLong = user?.long;
  const currentLocation = latestLocation?.location ||
    (currentLat && currentLong ? `${currentLat}, ${currentLong}` : "Ubicación no disponible");

  // Get current speed from latest route (rounded to remove decimals)
  const currentSpeed = latestRoute?.max_speed && !isNaN(latestRoute.max_speed) && latestRoute.max_speed > 0
    ? `${Math.round(latestRoute.max_speed)}km/h`
    : "No disponible";

  // Determine mobility status
  const getMobilityStatus = () => {
    if (!latestLocation && !latestRoute) return "Sin datos";
    if (latestRoute?.route_completed === false) return "En ruta";
    if (latestLocation && latestRoute) {
      const locationTime = new Date(latestLocation.created_at);
      const routeTime = new Date(latestRoute.created_at);
      const timeDiff = Math.abs(locationTime - routeTime) / (1000 * 60); // minutes
      if (timeDiff < 5) return "En movimiento";
    }
    return "Estático";
  };

  // Update last update time when data changes
  useEffect(() => {
    if (latestLocation || latestRoute) {
      setLastUpdate(new Date());
    }
  }, [latestLocation, latestRoute]);

  return (
    <div className="self-start bg-white p-5">
      <div>
        <h2 className="text-lg font-bold">Ubicación actual</h2>
        {isLoading ? (
          <div className="mt-3">
            <p className="text-sm text-secondary">Cargando ubicación...</p>
          </div>
        ) : (
          <>
            <p className="mt-3 text-sm font-semibold">Ubicación actual</p>
            <p className="mt-1 text-sm text-secondary">
              {currentLocation}
            </p>
          </>
        )}
      </div>

      <div className="mt-6 grid grid-cols-3 text-sm">
        <dd className="font-semibold">Hora</dd>
        <dd className="font-semibold">Velocidad</dd>
        <dd className="text-left font-semibold">Estado</dd>
        <dl className="text-secondary">
          {latestLocation?.created_at
            ? `Desde ${format(new Date(latestLocation.created_at), 'HH:mm a')}`
            : latestRoute?.created_at
              ? `Desde ${format(new Date(latestRoute.created_at), 'HH:mm a')}`
              : "No disponible"
          }
        </dl>
        <dl className="text-secondary">
          {currentSpeed}
        </dl>
        <dl className={`text-left font-semibold ${
          getMobilityStatus() === "En ruta" ? "text-[#1555ED]" :
          getMobilityStatus() === "En movimiento" ? "text-blue-600" :
          getMobilityStatus() === "Estático" ? "text-yellow-600" :
          "text-gray-600"
        }`}>
          {getMobilityStatus()}
        </dl>
      </div>

      <div className="mt-5">
        <div className="aspect-[540/601] bg-accent overflow-hidden rounded relative">
          {mapError ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <p className="text-red-500 text-sm font-medium">No se pudo cargar el mapa</p>
                <p className="text-gray-500 text-xs mt-1">Verifique la conexión a internet</p>
              </div>
            </div>
          ) : currentLat && currentLong ? (
            <>
              <GoogleMap
                lat={currentLat}
                lng={currentLong}
                zoom={15}
                className="w-full h-full"
                showMarker={true}
              />
              {/* Location info overlay */}
              <div className="absolute bottom-2 left-2 bg-white bg-opacity-90 rounded px-2 py-1 text-xs text-gray-700">
                {parseFloat(currentLat).toFixed(6)}, {parseFloat(currentLong).toFixed(6)}
              </div>
              {/* Link to open in external map */}
              <a
                href={`https://www.google.com/maps?q=${currentLat},${currentLong}&z=15`}
                target="_blank"
                rel="noopener noreferrer"
                className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors"
              >
                Ver en Google Maps
              </a>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <div className="text-center">
                <p className="text-gray-500 text-sm">Sin ubicación disponible</p>
                <p className="text-gray-400 text-xs mt-1">El usuario no ha compartido su ubicación</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CurrentLocationCard;
