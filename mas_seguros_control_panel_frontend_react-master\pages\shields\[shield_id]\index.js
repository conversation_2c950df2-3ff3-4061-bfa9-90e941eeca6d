import React, { useState } from "react";
import ShieldLayout from "@/components/layouts/ShieldLayout";
import PointsOfInterestCard from "@/components/shields/shield/PointsOfInterestCard";
import PointHistoryCard from "@/components/shields/shield/PointHistoryCard";
import useAxios from "@/hooks/useAxios";
import { useRouter } from "next/router";
import { useQuery } from "react-query";


export default function index() {
  const { axios } = useAxios();
  const router = useRouter();
  const { shield_id } = router.query;
  const [selectedPointId, setSelectedPointId] = useState(null);

  const fetchData = async () => {
    try {
      const res = await axios.get("adminside/api/shield/shield-point-of-interest/", {
        params: { id: shield_id }
      });
      return res;
    } catch (error) {
      console.error("Error fetching points of interest:", error);
      return { data: [] };
    }
  };

  const {
    isLoading,
    isError,
    data: response,
    error
  } = useQuery(
    [`shield-${shield_id}-point-of-interests`],
    fetchData,
    {
      refetchOnWindowFocus: false,
      enabled: !!shield_id,
    }
  );

  // Ensure pois is always an array
  const pois = Array.isArray(response?.data) ? response.data : [];

  return (
    <ShieldLayout pageTitle="Escudos" headerTitle="Escudos">
      <div className="mt-4 grid grid-cols-1 gap-5 lg:grid-cols-2">
        <PointsOfInterestCard
          items={pois}
          selectedPointId={selectedPointId}
          onPointSelect={setSelectedPointId}
        />
        <PointHistoryCard
          selectedPointId={selectedPointId}
        />
      </div>
    </ShieldLayout>
  );
}
