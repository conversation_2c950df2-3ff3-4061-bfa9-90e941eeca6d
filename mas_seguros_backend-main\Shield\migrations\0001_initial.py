# Generated by Django 5.2.3 on 2025-06-14 11:14

import Shield.models
import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
        ('AdminSide', '0001_initial'),
        ('Alert', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PointsOfInterest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('poi_address', models.CharField(blank=True, max_length=300, null=True)),
                ('poi_tag_name', models.CharField(blank=True, max_length=100, null=True)),
                ('poi_lat', models.CharField(blank=True, max_length=100, null=True)),
                ('poi_long', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('admin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='ShieldModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('shield_code', models.CharField(blank=True, default=Shield.models.generate_shield_code, max_length=12, null=True)),
                ('shield_joining_code', models.CharField(blank=True, default=Shield.models.generate_shield_join_code, max_length=12, null=True)),
                ('shield_type', models.CharField(choices=[('Corporate', 'corporate'), ('Individual', 'individual')], max_length=100)),
                ('shield_name', models.CharField(blank=True, max_length=100, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='shield_logo')),
                ('condition', models.BooleanField(default=False)),
                ('suspend', models.BooleanField(blank=True, default=False, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('admin', models.ManyToManyField(blank=True, related_name='shield_admin', to='Account.userprofile')),
                ('alert', models.ManyToManyField(blank=True, related_name='shield_alerts', to='Alert.alertmodel')),
                ('locations', models.ManyToManyField(blank=True, to='Shield.pointsofinterest')),
                ('members', models.ManyToManyField(blank=True, related_name='shield_members', to='Account.userprofile')),
                ('promo_code', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='AdminSide.promocode')),
                ('shield_super_admin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='shield_super_admin', to='Account.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='ShieldJoinRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('Pendiente', 'Pendiente'), ('Aprobado', 'Aprobado'), ('Rechazado', 'Rechazado')], default=False, max_length=80)),
                ('already_used_as_notification', models.BooleanField(blank=True, default=False, null=True)),
                ('shield_requester', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requester', to='Account.userprofile')),
                ('shield_super_admin', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='super_admin_shield', to='Account.userprofile')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
        ),
        migrations.CreateModel(
            name='Route',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('route_id', models.CharField(blank=True, db_index=True, default=Shield.models.generate_route_id, max_length=6, null=True)),
                ('max_speed', models.CharField(max_length=50)),
                ('minimum_phone_battery', models.CharField(max_length=100)),
                ('route_completed', models.BooleanField(db_index=True, default=False)),
                ('route_starting_time', models.TimeField(auto_now_add=True, null=True)),
                ('route_ending_time', models.TimeField(blank=True, null=True)),
                ('route_date', models.DateField(db_index=True, default=datetime.date.today)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('ending_poi', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ending_poi', to='Shield.pointsofinterest')),
                ('member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('starting_poi', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='starting_poi', to='Shield.pointsofinterest')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
            options={
                'ordering': ['-route_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(max_length=255)),
                ('location_changed', models.BooleanField(default=False, null=True)),
                ('lat', models.CharField(blank=True, max_length=40, null=True)),
                ('long', models.CharField(blank=True, max_length=40, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('point_of_interest', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.pointsofinterest')),
                ('route', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.route')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='Hierarchie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('hierarchy', models.CharField(blank=True, choices=[('Colaborativo', 'Colaborativo'), ('Solitario', 'Solitario'), ('Fantasma', 'Fantasma')], max_length=50, null=True)),
                ('member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
        ),
        migrations.CreateModel(
            name='Biometric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biometric_code', models.CharField(blank=True, db_index=True, default=Shield.models.generate_biometric_code, max_length=10, null=True)),
                ('lat', models.CharField(blank=True, max_length=40, null=True)),
                ('long', models.CharField(blank=True, max_length=40, null=True)),
                ('address', models.CharField(blank=True, max_length=300, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='biometric_images')),
                ('type', models.CharField(blank=True, choices=[('ENTRADA', 'ENTRADA'), ('SALIDA', 'SALIDA')], db_index=True, default='SALIDA', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WalkieTalkie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('listen_audio', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
                ('shield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Shield.shieldmodel')),
            ],
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['shield', 'member', '-route_date'], name='Shield_rout_shield__ded774_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['shield', '-route_date', '-created_at'], name='Shield_rout_shield__de7105_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['member', '-route_date', '-created_at'], name='Shield_rout_member__8da321_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['shield', 'member', 'route_date'], name='Shield_rout_shield__2c2ed0_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['route_date', '-created_at'], name='Shield_rout_route_d_b935c9_idx'),
        ),
        migrations.AddIndex(
            model_name='route',
            index=models.Index(fields=['route_completed', '-route_date'], name='Shield_rout_route_c_bb71c7_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['route', 'created_at'], name='Shield_loca_route_i_1be744_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['route', '-created_at'], name='Shield_loca_route_i_70daba_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['userprofile', '-created_at'], name='Shield_loca_userpro_6aae75_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['shield', '-created_at'], name='Shield_loca_shield__4edf01_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['route', 'userprofile', '-created_at'], name='Shield_loca_route_i_14689a_idx'),
        ),
        migrations.AddIndex(
            model_name='biometric',
            index=models.Index(fields=['userprofile', '-created_at'], name='Shield_biom_userpro_187e1e_idx'),
        ),
        migrations.AddIndex(
            model_name='biometric',
            index=models.Index(fields=['shield', '-created_at'], name='Shield_biom_shield__6c6c58_idx'),
        ),
        migrations.AddIndex(
            model_name='biometric',
            index=models.Index(fields=['type', '-created_at'], name='Shield_biom_type_87e236_idx'),
        ),
        migrations.AddIndex(
            model_name='biometric',
            index=models.Index(fields=['userprofile', 'type', '-created_at'], name='Shield_biom_userpro_494ec7_idx'),
        ),
        migrations.AddIndex(
            model_name='biometric',
            index=models.Index(fields=['shield', 'userprofile', '-created_at'], name='Shield_biom_shield__37f1a6_idx'),
        ),
    ]
