from rest_framework.serializers import ModelSerializer
from Alert import models as alert_models
from rest_framework import serializers
from django.contrib.auth.models import User
from Account.models import UserProfile
from Account import serializers as account_seri
from datetime import datetime


class AlertCategories(serializers.ModelSerializer):
    class Meta:
        model = alert_models.AlertCategories
        fields = ['id', 'name', 'image_url']


class CreateAlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = alert_models.AlertModel
        fields = ['id', 'category', 'evidence', 'thumbnail', 'status', 'description', 'address', 'lat', 'long',
                  'current_speed', 'rating', 'rating_description',
                  'phone_battery', 'alert_seen', 'video_url']


class GetAlertIdSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)


class GetAlertSerializer(serializers.ModelSerializer):
    category = serializers.SerializerMethodField()
    status_name = serializers.SerializerMethodField()
    alert_date = serializers.SerializerMethodField()
    alert_datetime = serializers.SerializerMethodField()
    updated_at = serializers.SerializerMethodField()
    rating = serializers.CharField(max_length=10, required=False, allow_null=True)
    rating_description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    userprofile = account_seri.UserProfileSerializer(read_only=True)

    def get_alert_date(self, obj):
        try:
            if hasattr(obj, 'alert_date') and obj.alert_date:
                old_date = datetime.strptime(obj.alert_date, '%m/%d/%Y')
                return old_date
            return None
        except (AttributeError, ValueError, TypeError) as e:
            print(f"Error in get_alert_date: {e}")
            return None

    def get_alert_datetime(self, obj):
        try:
            if hasattr(obj, 'alert_date') and obj.alert_date and hasattr(obj, 'alert_time') and obj.alert_time:
                old_date = datetime.strptime(obj.alert_date, '%m/%d/%Y').strftime("%Y-%m-%d")
                newdate = '{}T{}'.format(old_date, obj.alert_time)
                return newdate
            return None
        except (AttributeError, ValueError, TypeError) as e:
            print(f"Error in get_alert_datetime: {e}")
            return None

    def get_category(self, obj):
        try:
            # First check if category exists and is not None
            if hasattr(obj, 'category') and obj.category is not None:
                return obj.category.name
            return ""
        except (AttributeError, ValueError, TypeError) as e:
            print(f"Error in get_category: {e}")
            return ""

    def get_status_name(self, obj):
        """Get status name using the model's built-in method"""
        try:
            return obj.get_status_name()
        except (AttributeError, ValueError, TypeError) as e:
            print(f"Error in get_status_name: {e}")
            return "Sin estado"

    def get_updated_at(self, obj):
        try:
            if obj.updated_at:
                old_date = obj.updated_at.strftime("%Y-%m-%d")
                newdate = '{}T{}'.format(old_date, obj.updated_at.time().strftime("%H:%M:%S"))
                return newdate
            return None
        except (AttributeError, ValueError, TypeError) as e:
            print(f"Error in get_updated_at: {e}")
            return None

    class Meta:
        model = alert_models.AlertModel
        fields = ['category', 'num', 'status', 'status_name', 'alert_date', 'alert_time', 'alert_datetime',
                  'evidence_url', 'description',
                  'address', 'thumbnail_url',
                  'current_speed', 'lat', 'long',
                  'phone_battery', 'evidence_number', 'rating_description', 'rating', 'id', 'updated_at', 'alert_seen',
                  'video_url', 'userprofile']


# to update the alerts
class getallAlertSerilizer(serializers.ModelSerializer):
    category = serializers.CharField(required=False, max_length=50)
    status = serializers.CharField(required=False, max_length=50)
    evidence = serializers.FileField(required=False)
    description = serializers.CharField(required=False)
    current_speed = serializers.CharField(required=False)
    phone_battery = serializers.CharField(required=False)

    class Meta:
        model = alert_models.AlertModel
        fields = ['category', 'num', 'status', 'alert_date', 'alert_time', 'evidence', 'description', 'address',
                  'current_speed',
                  'phone_battery', 'evidence_number', 'rating_description', 'rating', 'id', 'userprofile', 'alert_seen',
                  'video_url']

    def to_representation(self, instance):
        response = super().to_representation(instance)
        response['userprofile'] = account_seri.UserProfileSerializer(instance.userprofile).data
        return response


class AlertStatuesSerializer(serializers.ModelSerializer):
    class Meta:
        model = alert_models.AlertCategories
        fields = ['id', 'name', ]


class AlertReviewsSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    rating = serializers.CharField(required=True)
    rating_description = serializers.CharField(max_length=2500, required=True)

    class Meta:
        model = alert_models.AlertCategories
        fields = ['id', 'rating', 'rating_description']


# to update the alerts
class getUnresolvedAlertSerilizer(serializers.ModelSerializer):
    category = serializers.CharField(required=False, max_length=50)
    status = serializers.CharField(required=False, max_length=50)
    evidence = serializers.FileField(required=False)
    description = serializers.CharField(required=False)
    current_speed = serializers.CharField(required=False)
    phone_battery = serializers.CharField(required=False)

    class Meta:
        model = alert_models.AlertModel
        fields = ['category', 'num', 'status', 'alert_date', 'alert_time', 'evidence', 'description', 'address',
                  'current_speed',
                  'phone_battery', 'evidence_number', 'rating_description', 'rating', 'id', 'userprofile', 'alert_seen',
                  'video_url']
