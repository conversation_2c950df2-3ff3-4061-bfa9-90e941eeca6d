from django.db import models
from tinymce import models as tinymce_models
# Create your models here.

class DataPolicie(models.Model):
    title = models.CharField(max_length=255)
    description = tinymce_models.HTMLField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.description


# TODO: TermsAndCondition is the check on the signup page
class TermsAndCondition(models.Model):
    title = models.CharField(max_length=255)
    description = tinymce_models.HTMLField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)


    def __str__(self):
        return self.description
