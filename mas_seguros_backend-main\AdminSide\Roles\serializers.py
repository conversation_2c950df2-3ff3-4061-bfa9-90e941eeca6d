from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from Account import models as account_models
from AdminSide import models as adminside_models
from time import strftime
from datetime import datetime
from django.contrib.auth.models import User


class GetUserIdSerializer(serializers.Serializer):
    id = serializers.CharField(max_length=100, required=True)


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined', 'last_login']


class AdminPermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = adminside_models.AdminPermission
        fields = ['users_access', 'shields_access', 'alerts_sos_access',
                 'payment_history_access', 'support_access', 'roles_access', 'full_access']


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    admin_permissions = AdminPermissionSerializer(read_only=True)
    last_activity = serializers.SerializerMethodField()
    # Add birth_date to serializer
    birth_date = serializers.DateField(format="%d/%m/%Y", required=False)

    class Meta:
        model = account_models.UserProfile
        fields = ['id', 'user', 'phone', 'full_name', 'identification_card', 'role',
                 'suspend', 'created_at', 'updated_at', 'image_url', 'admin_permissions', 'last_activity', 'birth_date']

    def get_last_activity(self, obj):
        if obj.user.last_login:
            return obj.user.last_login.strftime('%d/%m/%Y, %H:%M hrs')
        return 'Nunca'


class AdminCreateSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=30, required=True)
    last_name = serializers.CharField(max_length=30, required=True)
    email = serializers.EmailField(required=True)
    password = serializers.CharField(min_length=6, required=True)
    phone = serializers.CharField(max_length=20, required=True)
    identification_card = serializers.CharField(max_length=60, required=False, allow_blank=True)

    # Permission fields
    users_access = serializers.BooleanField(default=False)
    shields_access = serializers.BooleanField(default=False)
    alerts_sos_access = serializers.BooleanField(default=False)
    payment_history_access = serializers.BooleanField(default=False)
    support_access = serializers.BooleanField(default=False)
    roles_access = serializers.BooleanField(default=False)
    full_access = serializers.BooleanField(default=False)

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError("Complete los campos requeridos")

        # Basic email format validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            raise serializers.ValidationError("Formato de correo electrónico inválido")

        if User.objects.filter(email=value.lower()).exists():
            raise serializers.ValidationError("No se permite correo duplicado")
        return value.lower()

    def validate_phone(self, value):
        if not value:
            raise serializers.ValidationError("Complete los campos requeridos")

        # Basic phone validation - allow numbers, +, -, spaces, and parentheses
        import re
        phone_pattern = r'^[\+]?[0-9\s\-\(\)]{7,20}$'
        if not re.match(phone_pattern, value):
            raise serializers.ValidationError("El campo teléfono solo permite números válidos")

        if account_models.UserProfile.objects.filter(phone=value).exists():
            raise serializers.ValidationError("Ya existe un usuario con este número de teléfono.")
        return value

    def validate_first_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Complete los campos requeridos")
        return value.strip()

    def validate_last_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Complete los campos requeridos")
        return value.strip()

    def validate_password(self, value):
        if not value:
            raise serializers.ValidationError("Complete los campos requeridos")
        if len(value) < 6:
            raise serializers.ValidationError("Contraseña inválida o no coincide")
        return value

    def validate_identification_card(self, value):
        if value is None:
            return ''
        return str(value)


class AdminUpdateSerializer(serializers.Serializer):
    first_name = serializers.CharField(max_length=30, required=True)
    last_name = serializers.CharField(max_length=30, required=True)
    phone = serializers.CharField(max_length=20, required=True)
    identification_card = serializers.CharField(max_length=60, required=False, allow_blank=True)
    # Permission fields
    users_access = serializers.BooleanField(default=False)
    shields_access = serializers.BooleanField(default=False)
    alerts_sos_access = serializers.BooleanField(default=False)
    payment_history_access = serializers.BooleanField(default=False)
    support_access = serializers.BooleanField(default=False)
    roles_access = serializers.BooleanField(default=False)
    full_access = serializers.BooleanField(default=False)
    # Fix: Remove format, only use input_formats for correct DB save
    birth_date = serializers.DateField(input_formats=['%d/%m/%Y', '%Y-%m-%d'], required=False)

    def validate_phone(self, value):
        if not value:
            raise serializers.ValidationError("Complete los campos requeridos")

        # Basic phone validation
        import re
        phone_pattern = r'^[\+]?[0-9\s\-\(\)]{7,20}$'
        if not re.match(phone_pattern, value):
            raise serializers.ValidationError("El campo teléfono solo permite números válidos")

        # Allow same phone for current user during update
        request = self.context.get('request')
        if request and hasattr(request, 'user_profile'):
            existing = account_models.UserProfile.objects.filter(phone=value).exclude(id=request.user_profile.id)
            if existing.exists():
                raise serializers.ValidationError("Ya existe un usuario con este número de teléfono.")
        return value

    def validate_first_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Complete los campos requeridos")
        return value.strip()

    def validate_last_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError("Complete los campos requeridos")
        return value.strip()

    def validate_identification_card(self, value):
        if value is None:
            return ''
        return str(value)


class ChangePasswordSerializer(serializers.Serializer):
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(min_length=6, required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError("Contraseña inválida o no coincide")
        return data


class EditProfileSerializer(serializers.Serializer):
    """Legacy serializer for backward compatibility"""
    full_name = serializers.CharField(max_length=60, required=True)
    phone = serializers.CharField(max_length=20, required=True)
    identification_card = serializers.CharField(max_length=60, required=False, allow_blank=True)
    # Add birth_date field
    from time import strftime
    birth_date = serializers.DateField(format=strftime("%d/%m/%Y"), input_formats=['%d/%m/%Y', '%Y-%m-%d'], required=False)


class LegacyEditProfileSerializer(serializers.Serializer):
    """Extended legacy serializer"""
    id = serializers.CharField(max_length=100, required=True)
    full_name = serializers.CharField(required=True)
    email = serializers.CharField(required=True)
    phone = serializers.CharField(required=True)
    identification_card = serializers.CharField(required=True)
    birth_date = serializers.DateField(format=strftime("%d/%m/%Y"), input_formats=['%d/%m/%Y', ], required=True)
    image = serializers.ImageField(required=False)
