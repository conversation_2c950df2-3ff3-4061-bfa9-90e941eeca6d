# =============================================================================
# MAS SEGUROS BACKEND - COMPREHENSIVE MIGRATION RESET SCRIPT
# =============================================================================
# This script safely resets all Django migrations for the project
# Author: Augment Agent
# Date: $(Get-Date -Format "yyyy-MM-dd")
# =============================================================================

param(
    [switch]$SkipBackup,
    [switch]$Force,
    [switch]$Help
)

if ($Help) {
    Write-Host @"
MAS SEGUROS MIGRATION RESET SCRIPT
==================================

Usage: .\reset-migrations.ps1 [OPTIONS]

Options:
  -SkipBackup    Skip database backup (not recommended for production)
  -Force         Skip confirmation prompts
  -Help          Show this help message

Examples:
  .\reset-migrations.ps1                    # Normal reset with backup
  .\reset-migrations.ps1 -SkipBackup       # Reset without backup
  .\reset-migrations.ps1 -Force            # Reset without prompts

"@
    exit 0
}

# Color functions
function Write-Success { param($Message) Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }
function Write-Info { param($Message) Write-Host "[INFO] $Message" -ForegroundColor Cyan }

Write-Host @"
=============================================================================
🚀 MAS SEGUROS BACKEND - MIGRATION RESET SCRIPT
=============================================================================
"@ -ForegroundColor Magenta

# Check if virtual environment is activated
if (-not $env:VIRTUAL_ENV) {
    Write-Warning "Virtual environment not detected!"
    Write-Info "Attempting to activate virtual environment..."

    # Try to find and activate virtual environment
    $venvPaths = @(".\venv\Scripts\Activate.ps1", ".\env\Scripts\Activate.ps1", ".\.venv\Scripts\Activate.ps1")
    $venvActivated = $false

    foreach ($venvPath in $venvPaths) {
        if (Test-Path $venvPath) {
            Write-Info "Found virtual environment at: $venvPath"
            try {
                & $venvPath
                $venvActivated = $true
                Write-Success "Virtual environment activated successfully!"
                break
            } catch {
                Write-Warning "Failed to activate virtual environment at: $venvPath"
            }
        }
    }

    if (-not $venvActivated) {
        Write-Warning "Could not find or activate virtual environment!"
        Write-Info "Please activate your virtual environment manually:"
        Write-Host "   .\venv\Scripts\Activate.ps1" -ForegroundColor Yellow
        if (-not $Force) {
            $continue = Read-Host "Continue anyway? (y/N)"
            if ($continue -ne "y" -and $continue -ne "Y") {
                exit 1
            }
        }
    }
}

# Check if manage.py exists
if (-not (Test-Path "manage.py")) {
    Write-Error "manage.py not found! Please run this script from the Django project root."
    exit 1
}

# Confirmation prompt
if (-not $Force) {
    Write-Warning "This will DELETE ALL migration files and reset the database!"
    Write-Info "Make sure you have a backup of your data."
    $confirm = Read-Host "Are you sure you want to continue? (y/N)"
    if ($confirm -ne "y" -and $confirm -ne "Y") {
        Write-Info "Operation cancelled."
        exit 0
    }
}

# Create backup if not skipped
if (-not $SkipBackup) {
    Write-Info "Creating database backup..."
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "backup_$timestamp.json"

    try {
        python manage.py dumpdata --natural-foreign --natural-primary --exclude=contenttypes --exclude=auth.Permission > $backupFile
        Write-Success "Database backup created: $backupFile"
    } catch {
        Write-Warning "Backup failed, but continuing..."
    }
}

Write-Info "Step 1: Deleting migration .py files (except __init__.py)..."
$deletedFiles = 0
Get-ChildItem -Recurse -Include *.py -Path . | Where-Object {
    $_.FullName -match "\\migrations\\" -and $_.Name -ne "__init__.py" -and $_.FullName -notmatch "\\venv\\" -and $_.FullName -notmatch "site-packages"
} | ForEach-Object {
    Remove-Item -Force $_.FullName
    $deletedFiles++
}
Write-Success "Deleted $deletedFiles migration files"

Write-Info "Step 2: Deleting .pyc files in migrations..."
$deletedPyc = 0
Get-ChildItem -Recurse -Include *.pyc -Path . | Where-Object {
    $_.FullName -match "\\migrations\\" -and $_.FullName -notmatch "\\venv\\" -and $_.FullName -notmatch "site-packages"
} | ForEach-Object {
    Remove-Item -Force $_.FullName
    $deletedPyc++
}
Write-Success "Deleted $deletedPyc .pyc files"

Write-Info "Step 3: Deleting __pycache__ folders..."
$deletedCache = 0
Get-ChildItem -Recurse -Directory -Filter __pycache__ | Where-Object {
    $_.FullName -notmatch "\\venv\\" -and $_.FullName -notmatch "site-packages"
} | ForEach-Object {
    Remove-Item -Recurse -Force $_.FullName
    $deletedCache++
}
Write-Success "Deleted $deletedCache __pycache__ folders"

Write-Info "Step 4: Deleting migrations folders (except venv)..."
$deletedDirs = 0
Get-ChildItem -Recurse -Directory -Filter migrations | Where-Object {
    $_.FullName -notmatch "\\venv\\" -and $_.FullName -notmatch "site-packages"
} | ForEach-Object {
    Remove-Item -Recurse -Force $_.FullName
    $deletedDirs++
}
Write-Success "Deleted $deletedDirs migration directories"

Write-Info "Step 5: Creating fresh migrations..."
try {
    python manage.py makemigrations
    Write-Success "Fresh migrations created successfully"
} catch {
    Write-Error "Failed to create migrations!"
    exit 1
}

Write-Info "Step 6: Applying migrations..."
try {
    python manage.py migrate
    Write-Success "Migrations applied successfully"
} catch {
    Write-Error "Failed to apply migrations!"
    exit 1
}

Write-Info "Step 7: Running system checks..."
try {
    python manage.py check
    Write-Success "System check passed"
} catch {
    Write-Warning "System check failed, but migrations completed"
}

Write-Host @"
=============================================================================
🎉 MIGRATION RESET COMPLETED SUCCESSFULLY!
=============================================================================
"@ -ForegroundColor Green

Write-Info "Summary:"
Write-Host "  - Migration files deleted: $deletedFiles" -ForegroundColor White
Write-Host "  - Cache files deleted: $deletedPyc" -ForegroundColor White
Write-Host "  - Cache directories deleted: $deletedCache" -ForegroundColor White
Write-Host "  - Migration directories deleted: $deletedDirs" -ForegroundColor White
if (-not $SkipBackup) {
    Write-Host "  - Backup created: $backupFile" -ForegroundColor White
}

Write-Info "Next steps:"
Write-Host "  1. Test your application: python manage.py runserver" -ForegroundColor Yellow
Write-Host "  2. Create superuser if needed: python manage.py createsuperuser" -ForegroundColor Yellow
if (-not $SkipBackup) {
    Write-Host "  3. Load backup data if needed: python manage.py loaddata $backupFile" -ForegroundColor Yellow
}
