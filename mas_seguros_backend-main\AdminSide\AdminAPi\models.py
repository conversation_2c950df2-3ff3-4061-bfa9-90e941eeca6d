from django.db import models
from Account import models as account_models
from Alert.models import AlertModel
from django.contrib.auth.models import User
# from CompanyDashboard import models as company_models
import datetime
from django.utils.crypto import get_random_string

# from CompanyDashboard import models as company_models

# Create your models here.
def generate_promo_code():
    return get_random_string(length=8)


class PromoCode(models.Model):
    label = models.CharField(max_length=150, null=True, blank=True)
    company = models.ForeignKey("AdminSide.CompanyProfileModel", on_delete=models.CASCADE, null=True, blank=True)
    promo_code = models.CharField(max_length=100, null=True, blank=True, unique=True)
    code_id = models.Char<PERSON>ield(default=generate_promo_code, max_length=10)
    start_duration = models.DateField(default=datetime.date.today)
    end_duration = models.DateField(null=True)
    stocks = models.IntegerField(null=False)
    discount = models.IntegerField(null=True)
    Etiquette = models.Char<PERSON>ield(max_length=100, null=True)
    state = models.<PERSON><PERSON>an<PERSON>ield(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    membership_choice = (
        ("level 1", "level 1"),
        ("level 2", "level 2"),
        ("level 3", "level 3"),
        ("level 4", "level 4")
    )
    membership = models.CharField(max_length=100, choices=membership_choice, blank=True)

    @property
    def start_duration_date(self):
        return self.date.strftime("%m/%d/%Y")

    @property
    def end_duration_date(self):
        return self.date.strftime("%m/%d/%Y")

# class Alert(models.Model):
#     comment = models.TextField(null=True,blank=True)
#     alert = models.OneToOneField(AlertModel, on_delete=models.CASCADE)
#     def __str__(self):
#         return self.alert






