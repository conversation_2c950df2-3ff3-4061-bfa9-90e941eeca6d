@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
    input[type='number']::-webkit-outer-spin-button,
    input[type='number']::-webkit-inner-spin-button,
    input[type='number'] {
      -webkit-appearance: none;
      margin: 0;
      -moz-appearance: textfield !important;
  }
}


.container-padding {
 @apply px-4 sm:px-6 lg:px-8;
}


/* ===== Scrollbar CSS ===== */
* {
  scrollbar-width: thin;
  scrollbar-color: black #ffffff;
}
*::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar:horizontal {
  height: 5px;
}
*::-webkit-scrollbar-track {
  background: #ffffff;
}
*::-webkit-scrollbar-thumb {
  background-color: black;
  border-radius: 0px;
  border: 0px none #ffffff;
}


/* No Scrollbar */
.no-scrollbar::-webkit-scrollbar {
  width: 0px;
}
.no-scrollbar::-webkit-scrollbar:horizontal {
  height: 0px;
}

input[type="radio"] {
  @apply !h-5 !w-5;
}