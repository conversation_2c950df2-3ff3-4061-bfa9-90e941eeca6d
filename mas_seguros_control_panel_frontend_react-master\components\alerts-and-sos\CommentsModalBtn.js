import Modal from "@/components/utility/Modal";
import classNames from "classnames";
import React, { createElement, useState, useEffect, useMemo } from "react";
import InputGroup from "../utility/InputGroup";
import useAxios from "@/hooks/useAxios";
import { useQuery, useMutation, useQueryClient } from "react-query";
import toast from "react-hot-toast";

const CommentsModalBtn = ({ as = "button", alert = {}, className = "", ...props }) => {
  const [open, setOpen] = useState(false);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { axios } = useAxios();
  const queryClient = useQueryClient();

  // Determine alert type and ID
  const alertType = useMemo(() => {
    const type = alert?.type?.toLowerCase();
    const categoryName = alert?.category?.name?.toLowerCase();
    return type === 'sos' || categoryName === 'sos' || alert?.sender ? 'sos' : 'alert';
  }, [alert?.type, alert?.category?.name, alert?.sender]);

  const alertId = alert?.id;

  // Fetch comments for this alert/SOS
  const { data: comments = [], isLoading: commentsLoading, refetch: refetchComments } = useQuery(
    [`comments-${alertType}-${alertId}`],
    async () => {
      if (!alertId) return [];
      try {
        const response = await axios.get('/adminside/api/alert/getcommentalertsos/', {
          params: {
            id: alertId,
            type: alertType
          }
        });
        return response.data?.data || [];
      } catch (error) {
        console.error('Error fetching comments:', error);
        return [];
      }
    },
    {
      enabled: !!alertId && open,
      refetchOnWindowFocus: false,
    }
  );

  // Mutation for posting comments
  const postCommentMutation = useMutation(
    async (commentData) => {
      const response = await axios.post('/adminside/api/alert/postcommentalertsos/', commentData);
      return response.data;
    },
    {
      onSuccess: () => {
        toast.success('Comentario agregado exitosamente');
        setComment('');
        refetchComments();
        // Refetch the main alerts table data
        queryClient.invalidateQueries(['alerts-and-sos-table-data']);
      },
      onError: (error) => {
        console.error('Error posting comment:', error);
        toast.error(error.response?.data?.message || 'Error al agregar comentario');
      },
      onSettled: () => {
        setIsSubmitting(false);
      }
    }
  );

  const close = () => {
    setOpen(false);
    setComment('');
  };

  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!comment.trim()) {
      toast.error('Debe ingresar un comentario');
      return;
    }

    if (!alertId) {
      toast.error('ID de alerta no válido');
      return;
    }

    setIsSubmitting(true);
    postCommentMutation.mutate({
      id: alertId,
      type: alertType,
      comment: comment.trim()
    });
  };

  // Format date for display
  const formatDate = (dateStr) => {
    try {
      if (!dateStr) return "N/A";
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return "N/A";
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(',', ' ') + ' Hrs';
    } catch (error) {
      return "N/A";
    }
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-4xl overflow-hidden bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Comentarios</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="grid grid-cols-1 gap-5 p-5 lg:grid-cols-2">
            <div className="overflow-auto bg-accent p-2.5 text-sm lg:h-96">
              {commentsLoading ? (
                <div className="bg-white px-2.5 py-3 text-center">
                  <p>Cargando comentarios...</p>
                </div>
              ) : (
                <ul className="space-y-2">
                  {/* Display the description as the initial comment */}
                  {alert.description && (
                    <li className="bg-white px-2.5 py-3">
                      <p>{alert.description}</p>
                      <div className="mt-3 flex justify-between gap-4 text-secondary">
                        <span>{alert.userprofile?.full_name || alert.sender?.full_name || "Usuario"}</span>
                        <span>{formatDate(alert.created_at || alert.updated_at)}</span>
                      </div>
                    </li>
                  )}

                  {/* Display fetched comments */}
                  {comments.length > 0 ? (
                    comments.map((commentItem, index) => (
                      <li key={index} className="bg-white px-2.5 py-3">
                        <p>{commentItem.comment}</p>
                        <div className="mt-3 flex justify-between gap-4 text-secondary">
                          <span>{commentItem.userprofile?.full_name || "Administrador"}</span>
                          <span>{formatDate(commentItem.created_at)}</span>
                        </div>
                      </li>
                    ))
                  ) : !alert.description && (
                    <li className="bg-white px-2.5 py-3 text-center text-gray-500">
                      No hay comentarios disponibles
                    </li>
                  )}
                </ul>
              )}
            </div>
            <div className="text-sm">
              <InputGroup>
                <InputGroup.Textarea
                  className="h-60"
                  placeholder="Escribir..."
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
              </InputGroup>
              <div className="mt-3 text-right">
                <button
                  className="rounded bg-black px-4 py-1.5 text-white disabled:bg-gray-400"
                  disabled={!comment.trim() || isSubmitting}
                  onClick={handleSubmitComment}
                >
                  {isSubmitting ? 'Enviando...' : 'Crear comentario'}
                </button>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-black text-white">
              Cerrar
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        type: "button",
        onClick: () => setOpen(true),
        className: classNames(className, "font-semibold hover:underline"),
        ...props,
      })}
    </>
  );
};

export default CommentsModalBtn;
