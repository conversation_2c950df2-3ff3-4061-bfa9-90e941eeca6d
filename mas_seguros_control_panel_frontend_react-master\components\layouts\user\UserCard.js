import ProfilePicture from "@/components/ProfilePicture";
import { format } from "date-fns";
import React from "react";

const UserCard = ({ data, isSuccess }) => {
  // Debug logging
  console.log("UserCard received data:", data);
  console.log("UserCard isSuccess:", isSuccess);

  // Handle both adminside API response formats
  const userProfile = data?.userprofile || data;
  const user = userProfile?.user || {};

  console.log("UserCard userProfile:", userProfile);
  console.log("UserCard user:", user);

  // Show loading state if data is not available
  if (!isSuccess || !data) {
    return (
      <div className="items-center gap-7 bg-white px-5 py-10 sm:flex 2xl:block">
        <div className="text-center">
          <div className="mx-auto aspect-square w-24 rounded-full bg-gray-200 animate-pulse"></div>
          <div className="mt-3 h-6 bg-gray-200 rounded animate-pulse"></div>
          <div className="mt-1 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="flex-grow">
          <div className="h-6 bg-gray-200 rounded animate-pulse mb-4"></div>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const items = [
    {
      key: "ID Usuario",
      value: userProfile?.id ? `ID-${userProfile.id}` : (user?.id ? `ID-${user.id}` : 'No disponible'),
    },
    {
      key: "Membresía",
      value: data?.membership || userProfile?.membership || "No asignada",
    },
    {
      key: "Teléfono",
      value: userProfile?.phone || "No disponible",
    },
    {
      key: "Correo",
      value: user?.email || "No disponible",
    },
    {
      key: "Fecha de creación",
      value: userProfile?.created_at ? format(new Date(userProfile.created_at), "dd/MM/yy") : "No disponible",
    },
    {
      key: "Tipo de Usuario",
      value: userProfile?.user_type || "No especificado",
    },
  ];

  return (
    <div className="items-center gap-7 bg-white px-5 py-10 sm:flex 2xl:block">
      <div className="text-center">
        <ProfilePicture
          className="mx-auto aspect-square w-24 rounded-full"
          src={userProfile?.image || userProfile?.image_url}
          alt="User"
        />
        <h4 className="mt-3 text-lg font-semibold">
          {userProfile?.full_name ||
           (user?.first_name || user?.last_name ? `${user?.first_name || ''} ${user?.last_name || ''}`.trim() : '') ||
           "Usuario sin nombre"}
        </h4>
        <p className="text-secondary">
          ID {userProfile?.id || user?.id || 'No disponible'}
        </p>
        {userProfile?.suspend ? (
          <span className="mt-1.5 inline-flex items-center rounded-full bg-red-100 px-3 py-1.5 text-sm font-semibold text-red-600">
            <svg
              className="mr-1.5 h-2 w-2 text-red-600"
              fill="currentColor"
              viewBox="0 0 8 8"
            >
              <circle cx={5} cy={4} r={3} />
            </svg>
            Suspendido
          </span>
        ) : (
          <span className="mt-1.5 inline-flex items-center rounded-full bg-green-100 px-3 py-1.5 text-sm font-semibold text-green-600">
            <svg
              className="mr-1.5 h-2 w-2 text-green-600"
              fill="currentColor"
              viewBox="0 0 8 8"
            >
              <circle cx={5} cy={4} r={3} />
            </svg>
            Activo
          </span>
        )}
      </div>

      <div className="flex-grow">
        <h5 className="border-b pb-2 text-lg font-semibold">Detalles</h5>
        <div className="mt-4 grid grid-cols-1 gap-y-6 gap-x-5 text-sm sm:grid-cols-2 2xl:grid-cols-1">
          {items.map((item) => {
            return (
              <dl key={item.key}>
                <dd className="font-semibold">{item.key}</dd>
                <dd className="text-secondary">{item.value}</dd>
              </dl>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default UserCard;
