from Account import models as account_models
from rest_framework import serializers
from django.contrib.auth.models import User
from . import models as company_models
from Shield import models as shield_models
from Membership import models as membership_models
from AdminSide.AdminAPi import models as admin_models


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        lower_email = value.lower()
        if User.objects.filter(email__iexact=lower_email).exists():
            raise serializers.ValidationError("Email already registered to other account! ")
        return lower_email

    class Meta:
        model = account_models.User
        fields = (
            'id',
            'first_name',
            'last_name',
            'username',
            'email',
            'password'
        )


class UserProfileSerializerForLogin(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = '__all__'


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role',
                  'lat', 'long', 'verification_code', 'email_verified', 'enable_location', 'image_url', 'user_type',
                  'suspend']


class SuperAdminSerializer(serializers.ModelSerializer):
    userprofile = UserProfileSerializer()

    class Meta:
        model = company_models.SuperAdmin
        fields = ['userprofile', ]


class CompanySerializer(serializers.ModelSerializer):
    super_admin = SuperAdminSerializer(required=False)
    users = serializers.SerializerMethodField(required=False)
    shields = serializers.SerializerMethodField(required=False)
    admin = serializers.SerializerMethodField(required=False)

    def get_users(self, record):
        record: company_models.CompanyProfileModel
        return record.users.all().count()

    def get_shields(self, record):
        record: company_models.CompanyProfileModel
        return record.shields.all().count()

    def get_admin(self, record):
        record: company_models.CompanyProfileModel
        return record.admin.all().count()

    class Meta:
        model = company_models.CompanyProfileModel
        fields = ['id', 'name', 'admin', 'users', 'shields', 'image_url', 'company_code', 'state', 'suspended', 'super_admin',
                  'created_at', 'updated_at']


class GetCompanyIDSerializer(serializers.Serializer):
    id = serializers.CharField(max_length=10, required=True)


class ShieldsSerializer(serializers.ModelSerializer):
    membership_level = serializers.SerializerMethodField()
    admin = serializers.SerializerMethodField()

    def get_admin(self, record):
        """Return the first admin as a single object instead of array"""
        admin = record.admin.first()
        if admin:
            return UserProfileSerializer(admin).data
        return None

    def get_membership_level(self, record):
        record: shield_models.ShieldModel
        company_obj_with_id = record.companyprofilemodel_set.all().last()
        # company_obj_with_id = company_models.CompanyProfileModel.objects.filter(users__id=record.id).last()
        membership_obj = membership_models.MembershipModel.objects.filter(companyprofile=company_obj_with_id,
                                                                          conditions=membership_models.effected).last()
        if membership_obj:
            return membership_obj.membership
        return None

    class Meta:
        model = shield_models.ShieldModel
        fields = ['id', 'shield_name', 'logo_url', 'shield_code', 'admin', 'shield_type', 'members_count', 'created_at',
                  'condition', 'membership_level']


class CompanyMemberSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    membership_level = serializers.SerializerMethodField()
    # membership_lev =membership_serializers.membershipSerializer()
    number_of_shields = serializers.SerializerMethodField()

    def get_number_of_shields(self, record):
        shield_count = shield_models.ShieldModel.objects.filter(members__user=record.user).count()
        return shield_count

    def get_membership_level(self, record):
        record: account_models.UserProfile
        company_obj_with_id = company_models.CompanyProfileModel.objects.filter(users__id=record.id).last()
        membership_obj = membership_models.MembershipModel.objects.filter(companyprofile=company_obj_with_id,
                                                                          conditions=membership_models.effected).last()
        if membership_obj:
            return membership_obj.membership
        return None

    # def membership_leve(self,record):
    #     membership_levels= membership_models.MembershipModel.objects.filter(userprofile__user=record.membership)
    #     return membership_levels

    class Meta:
        model = account_models.UserProfile
        fields = ['user', 'firebase_uid', 'ui_id', 'phone', 'full_name', 'identification_card', 'birth_date', 'role',
                  'lat', 'long', 'verification_code', 'email_verified', 'enable_location', 'user_type', 'suspend',
                  'created_at', 'updated_at', 'image_url', 'number_of_shields', 'membership_level']


class MembershipSerializer(serializers.ModelSerializer):
    # membership =membership_serializers.membershipSerializer()
    # admin =
    userprofile = UserProfileSerializer()

    class Meta:
        model = membership_models.MembershipModel
        fields = ['order_id', 'membership', 'date', 'membership_end_date', 'unitary_amount', 'id', 'userprofile']


class PromoCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = admin_models.PromoCode
        fields = "__all__"
