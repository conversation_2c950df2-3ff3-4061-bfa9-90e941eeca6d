{"type": "service_account", "project_id": "mas-seguros-acc65", "private_key_id": "739c1523c6eebec5b2133d74579e27b91adc9383", "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCeoFXTTQiXfQ2H\ncD0mm2AxrZHRuMeU46kcBIE2s/UlslgLiMAst1lL6KQ/anBT8C8FGlNF3+FN<PERSON><PERSON><PERSON>\nYSVi+TFbpl4LnLbkK7gQ7VawDwIIY9nkbtvJhD2wW5gMwcwrVdiG67+8zmLKeufv\n/q3uejS2nU1qJ0dEuSxXtVaXArWFVAE6+HPPhFvIO0xafo2ETVR9bDTFgjsD87d6\nIeyw7BQByeCbh3RG1w2Yeq48culBV5FQZus/gySJ0R5yNmkfhrrO3Qxhnr1WqSOx\nTWYGqRLFreB/rqD7vebhoVtCrVSGu/et5uDWT2l+Lq5ChxR59PBlVLqFw/DTn9Ia\n3nQbsgMHAgMBAAECggEAI4sv0AoZ28AEsQ2AVk9IjbkjzIeICR++O+3yeix2yPye\nZ+87iImI885sR111UAmor48Hi8evt0/gcIE+HGBUJAIyXo2+GYCGXRyhaQ/j17dw\nEpSYoR4FqKfNeUZPloJE426d5xOj5VUG9arulFpDx4uV1ix49rg7bGfQwEO2Iuqm\nhjNDwRqM48/9dmTZXWewjLB87P8kTq2zEXzc/yQjiisn5y9E4SvtbpjBJItPz4XO\nWOBetcFu87LwTpT+F949RplDYuhpRry+eOpoCmoh6sCjxARgBT/OC/9x0f4Rpbi8\nlfPo1HhWZnGuCxzdhGbdaDg37NCtDfdTzvebA+g4GQKBgQDPtT7CbOOkTy4IfdGw\nfwi9Mq+60mgwVDsRIT0vQq4JC8WdQPGXm4CqC6SoVNWLa0LPc14HLpHYVQUOp/4t\n/hA0kM89zmve+O57JfDlGfYJA2qPkVbK0qVGk2okNtdpjlcUeJ4y46TOXC7xPXi3\nSF4tE2yNEyIDIpD8iau9f3Jw3QKBgQDDgcEevAj7a16DrTfFkbAVBx0qRgK/DH+P\n2xCpfMvdJHIEur3RFhTiLCsMEhubviuG13YP31x/LWPdslBqdovk+Y0eXJpULJGD\nb5xKc0tt8mcLU/k+66YWfFEWoZFGITt6uLxVCMSrk2FV+nVl58oWnLeo3wBhpKOg\nccbtSbezMwKBgHVBZzWRDQ2j2WPXy3lsrX1JUOURhNnLozCvZ//3D5Jxv9RfZkNk\n+MSGICUbBxZWrfe4tBsv0gujK2Czu40xQOq72RKWJlVQiCijKZPcDQXXlN+Jd8q2\nEbF+5Mue3gpr3YLj+MLXuiD471MmlbA6PVgLABu/8lJ8wAagnYAzo2JNAoGBAKW+\ncwKjVtoPBGmNiCKuiCDLjyYvhFSDPAGxLkcBzbMFGsR3rER9++Zim3v4Pf+jETOX\npjd+nOQUjv18FkCrj53nEoQS7aM+xX4wOMfFRsVHI6Su5Dc7f+rAKd6NyqMxuUMp\nKkDM1i6/G13Qn4ZtIx5Ybjt0Eta0xSNYUdUJEfuRAoGASAbsyOvy/kV2n14t/g7B\noKBBuaI7maqOyAo5ZYQGN5Q8cHJHGbrGJFfR4ojqQNBY+It4Dc28Euv4x4aTUfk2\nX5Y0DirkYl9BhMSVAVQ5vDUl2RUhAXMZChjKnk3WyqCBkzTQuyuLbVRa0xnZCU/F\njMathAU8atHlMF6+ULG5dR4=\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "106165093614802142580", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-1mt2r%40mas-seguros-acc65.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}