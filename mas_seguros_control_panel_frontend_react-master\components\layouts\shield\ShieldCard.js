import Badge from "@/components/Badge";
import ProfilePicture from "@/components/ProfilePicture";
import classNames from "classnames";
import { format } from "date-fns";
import React, { createElement } from "react";

const CardWrapper = ({ as = 'div', className = '', ...props }) => {
  return createElement(as, {
    ...props,
    className: classNames("items-center gap-7 bg-white px-5 py-10 sm:flex 2xl:block", className),
  })
}

const ShieldCard = ({ shield = {}, isSuccess }) => {
  // Format admin data to ensure we're not rendering an object directly
  const formatAdminValue = (admin) => {
    if (!admin) return 'N/A';
    if (typeof admin === 'string') return admin;
    // If admin is an object, display the full_name or a default value
    return admin.full_name || admin.name || 'Admin sin nombre';
  };

  const data = [
    {
      key: "Administradores",
      value: formatAdminValue(shield?.admin),
    },
    {
      key: "N° de Miembros",
      value: shield?.members_count || 0,
    },
    {
      key: "Fecha de creación",
      value: shield?.created_at ? format(new Date(shield.created_at), 'dd/MM/yy') : 'N/A',
    },
    {
      key: "Tipo de Usuario",
      value: shield?.shield_type || 'N/A',
    },
  ];

  if (isSuccess && shield) {
    return (
      <CardWrapper>
        <div className="text-center">
          <ProfilePicture
            className="mx-auto aspect-square w-24 rounded-full object-cover"
            src={shield?.logo ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${shield.logo}` : null}
            alt={shield?.shield_name || 'Shield'}
          />
          <h4 className="mt-3 text-lg font-semibold capitalize">{shield?.shield_name || 'N/A'}</h4>
          <p className="text-secondary">ID {shield?.id || 'N/A'}</p>
          <div className="mt-1.5">
            {shield?.suspend ? (
              <Badge.Md className="text-red-600 bg-red-100" text="Suspendido" />
            ) : (
              <Badge.Md className="text-green-600 bg-green-100" text="Activo" />
            )}
          </div>
        </div>

        <div className="flex-grow">
          <h5 className="border-b pb-2 text-lg font-semibold">Detalles</h5>
          <div className="mt-4 grid grid-cols-1 gap-y-6 gap-x-5 text-sm sm:grid-cols-2 2xl:grid-cols-1">
            {data.map((item) => {
              return (
                <dl key={item.key}>
                  <dd className="font-semibold">{item.key}</dd>
                  <dd className="text-secondary">{item.value}</dd>
                </dl>
              );
            })}
          </div>
        </div>
      </CardWrapper>
    );
  }

  return <CardWrapper />

};

export default ShieldCard;
