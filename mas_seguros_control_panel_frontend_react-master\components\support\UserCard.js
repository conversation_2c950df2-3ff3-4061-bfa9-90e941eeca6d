import React from "react";
import SectionHeading from "../SectionHeading";
import { format } from "date-fns";

const UserCard = ({ selectedTicket }) => {
  if (!selectedTicket) {
    return (
      <div className="bg-white p-5 flex items-center justify-center">
        <p>Selecciona un ticket para ver la información del usuario</p>
      </div>
    );
  }

  const { user, title, created_at, resolved } = selectedTicket;
  const userName = user?.full_name || "Usuario";
  const userId = user?.id ? `UI${user.id}` : "N/A";
  const ticketId = selectedTicket.ticket_num || `Ticket #${selectedTicket.id}`;
  const ticketStatus = resolved ? "Resuelto" : "Pendiente";
  const statusColor = resolved ? "text-success" : "text-danger";
  const creationDate = created_at ? format(new Date(created_at), "dd/MM/yy") : "N/A";
  const location = user?.lat && user?.long ? `${user.lat}, ${user.long}` : "N/A";

  return (
    <div className="bg-white p-5">
      <div className="text-center">
        <img
          src="/assets/img/sample/user-2.png"
          className="mb-2 inline-block h-24 w-24"
          alt="User"
        />
        <SectionHeading>{userName}</SectionHeading>
        <dd className="text-secondary">ID {userId}</dd>
        <dd className={`text-[15px] font-semibold ${statusColor}`}>
          Ticket {ticketStatus}
        </dd>
      </div>
      <div className="mt-5 space-y-5 text-sm">
        <dl>
          <dd className="font-semibold">Ticket</dd>
          <dd className="text-secondary">{ticketId}</dd>
        </dl>
        <dl>
          <dd className="font-semibold">Asunto</dd>
          <dd className="text-secondary">{title}</dd>
        </dl>
        <dl>
          <dd className="font-semibold">Fecha de creación</dd>
          <dd className="text-secondary">{creationDate}</dd>
        </dl>
        {location !== "N/A" && (
          <dl>
            <dd className="font-semibold">Ubicación</dd>
            <dd className="text-secondary">{location}</dd>
          </dl>
        )}
      </div>
    </div>
  );
};

export default UserCard;
