import PaymentMembershipsTable from "@/components/payment-memberships/PaymentMembershipsTable";
import FilterDropDownBtn from "@/components/utility/FilterDropDownBtn";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import InputGroup from "@/components/utility/InputGroup";
import Pagination from "@/components/Pagination";
import Admin from "@/components/layouts/Admin";
import useTableData from "@/hooks/useTableData";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { format, isValid, parse } from "date-fns";

const pageSize = 10

export default function PaymentMemberships() {
  const [selectedDate, setSelectedDate] = useState("");
  const [dateError, setDateError] = useState("");

  const {
    search,
    setSearch,
    currentTableData,
    tempFilters,
    setTempFilters,
    applyFilters,
    isLoading,
    isError,
    error,
    sort,
    setSort,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess,
    resetPage
  } = useTableData({
    dataUrl: "adminside/api/Membership/payments/",
    pageSize: pageSize,
    queryKeys: ["payment-memberships-table-data"],
    dataCallback: (resp) => {
      // Check if the response has data directly in the response
      if (resp && Array.isArray(resp.data)) {
        return resp.data;
      }

      // Check if the data is nested in a data property
      const data = resp?.data?.data ?? [];
      return Array.isArray(data) ? data : [];
    }
  })

  // Handle date search
  const handleDateSearch = () => {
    setDateError("");

    if (!selectedDate) {
      toast.error('Por favor seleccione una fecha');
      return;
    }

    // Validate date format
    const parsedDate = parse(selectedDate, 'yyyy-MM-dd', new Date());
    if (!isValid(parsedDate)) {
      setDateError("Formato de fecha inválido");
      toast.error("Formato de fecha inválido");
      return;
    }

    try {
      // Format the date for search (DD/MM/YYYY format)
      const [year, month, day] = selectedDate.split('-');
      const formattedDate = `${day}/${month}/${year}`;

      setSearch(formattedDate);
      resetPage();
      toast.success('Búsqueda por fecha aplicada correctamente');
    } catch (error) {
      console.error('Error searching by date:', error);
      toast.error('Error al aplicar la búsqueda por fecha');
    }
  };

  const clearDateSearch = () => {
    setSelectedDate('');
    setSearch('');
    setDateError("");
    resetPage();
    toast.success('Filtros de fecha limpiados');
  };

  return (
    <Admin pageTitle="Pagos Membresías" headerTitle="Pagos Membresías">
      <div className="bg-neutral">
        <div className="container-padding items-center gap-3 space-y-2 py-2.5 lg:flex lg:space-y-0">
          <div className="w-full flex-shrink-0 sm:w-auto">
            <InputGroup className=" relative">
              <div className="absolute inset-y-0 left-0 flex w-9 items-center justify-center p-1 px-1.5 pl-3 text-secondary">
                <MagnifyingGlassIcon className="aspect-square w-full" />
              </div>
              <InputGroup.Input
                value={search}
                onChange={e => {
                  // Ensure we're setting the raw value without any special handling
                  // The trimming will be handled in the useTableData hook
                  setSearch(e.target.value);
                  resetPage();
                }}
                id="search"
                type="search"
                name="search"
                className="pl-9"
                placeholder="Buscar"
              />
            </InputGroup>
          </div>

          <div className="flex flex-grow items-center gap-3">
            {/* Date Filter */}
            <div className="flex items-center gap-2 text-sm">
              <span>Fecha:</span>
              <div>
                <InputGroup>
                  <InputGroup.Input
                    type="date"
                    className="bg-accent"
                    value={selectedDate}
                    onChange={(e) => {
                      setSelectedDate(e.target.value);
                      setDateError("");
                    }}
                    placeholder="DD/MM/YYYY"
                  />
                </InputGroup>
                {dateError && (
                  <p className="text-xs text-red-500 mt-1">{dateError}</p>
                )}
              </div>
              <button
                onClick={handleDateSearch}
                type="button"
                disabled={!selectedDate}
                className="rounded bg-primary px-3 py-2 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
              >
                Buscar
              </button>
              {selectedDate && (
                <button
                  type="button"
                  onClick={clearDateSearch}
                  className="rounded bg-gray-500 px-3 py-2 font-medium text-white ring-offset-2 focus:ring-2 hover:bg-gray-600 transition-colors text-sm"
                >
                  Limpiar
                </button>
              )}
            </div>

            <FilterDropDownBtn.Primary
              filters={tempFilters}
              setFilters={setTempFilters}
              onApply={applyFilters}
              groups={[
                {
                  id: 1,
                  title: "Membership",
                  options: [
                    {
                      id: 1,
                      label: "Nivel 1",
                      name: "membership",
                      value: "Level_1",
                    },
                    {
                      id: 2,
                      label: "Nivel 2",
                      name: "membership",
                      value: "Level_2",
                    },
                    {
                      id: 3,
                      label: "Nivel 3",
                      name: "membership",
                      value: "Level_3",
                    },
                    {
                      id: 4,
                      label: "Nivel 4",
                      name: "membership",
                      value: "Level_4",
                    },
                  ],
                },
                {
                  id: 2,
                  title: "Status",
                  options: [
                    {
                      id: 1,
                      label: "Efectuado",
                      name: "conditions",
                      value: "Effected",
                    },
                    {
                      id: 2,
                      label: "Fallido",
                      name: "conditions",
                      value: "Failed",
                    },
                  ],
                },
              ]}
            />
          </div>
        </div>
      </div>

      <div className="container-padding">
        {isSuccess && (!currentTableData || currentTableData.length === 0) ? (
          <div className="flex min-h-[200px] items-center justify-center bg-white">
            <div className="text-center text-warning">
              <ExclamationTriangleIcon className="mx-auto h-9 w-9" />
              <p className="mt-5">No se encontraron resultados</p>
            </div>
          </div>
        ) : (
          <PaymentMembershipsTable
            memberships={currentTableData}
            isLoading={isLoading}
            isError={isError}
            error={error}
            sort={sort}
            setSort={setSort}
          />
        )}

        {isSuccess && allData && allData.length > 0 && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}

      </div>
    </Admin>
  );
}
