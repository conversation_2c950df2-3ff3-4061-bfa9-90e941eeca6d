# Generated by Django 5.2.3 on 2025-06-14 11:14

import Membership.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Account', '0001_initial'),
        ('AdminSide', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MembershipModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(default=Membership.models.generate_order_id, max_length=10)),
                ('membership_end_date', models.DateField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('transaction_id', models.CharField(default=Membership.models.generate_transaction_id, max_length=10)),
                ('membership', models.CharField(choices=[('Level_1', 'Level_1'), ('Level_2', 'Level_2'), ('Level_3', 'Level_3'), ('Level_4', 'Level_4')], max_length=100)),
                ('unitary_amount', models.IntegerField()),
                ('vat_amount', models.IntegerField()),
                ('total_amount', models.IntegerField()),
                ('conditions', models.CharField(choices=[('Effected', 'Effected'), ('Failed', 'Failed')], max_length=100)),
                ('payment_address', models.CharField(max_length=100)),
                ('ipaddress', models.CharField(max_length=100)),
                ('payment_method', models.CharField(max_length=100)),
                ('companyprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='AdminSide.companyprofilemodel')),
                ('userprofile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='Account.userprofile')),
            ],
        ),
    ]
