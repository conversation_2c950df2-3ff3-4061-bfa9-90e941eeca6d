from abc import ABC

from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from . import models as faq_models


class FaqCategoryRequestSerializer(serializers.Serializer):
    category_name = serializers.CharField(max_length=20, required=True)


class FaqCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = faq_models.FaqCategory
        fields = ['name', 'id']


class FaqQuestionsRequestSerializer(serializers.Serializer):
    question = serializers.CharField(max_length=200, required=True)
    answer = serializers.CharField(max_length=2000, required=True)
    category_id = serializers.Char<PERSON>ield(max_length=20, required=True)


class FaqQuestionSerializer(serializers.Serializer):
    category_id = serializers.IntegerField(required=True)


class FaqQuestionCompleteSerializer(serializers.ModelSerializer):
    category = serializers.SerializerMethodField()

    def get_category(self, record):
        return record.category.name

    class Meta:
        model = faq_models.FaqQuestion
        fields = ['question', 'answer', 'category', 'id']

# ==========================================
