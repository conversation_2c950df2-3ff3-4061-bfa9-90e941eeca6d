import sys
from rest_framework import status
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail


from mas_seguros_backend.settings import from_email


def success_response(status_code=None, data=None, msg='Operation Success!'):
    response = {
        'status_code': status_code,
        'success': True,
        'message': msg,
        'data': data
    }
    if not status_code:
        # pass
        response["status_code"] = status.HTTP_200_OK
    return response


def failure_response(status_code=None, errors=None, msg='Operation Failure'):
    response = {
        'status_code': status_code,
        'success': False,
        'message': msg,
        'errors': errors
    }
    if not status_code:
        # pass
        response["status_code"] = status.HTTP_400_BAD_REQUEST
    return response


def send_email(subject, context, user=None, path=None, new_password=None):
    """
    Send an email to the user

    Args:
        subject: Email subject
        context: Context dictionary with message details
        user: User object to send email to
        path: Optional path for templates
        new_password: Optional new password to include in the email
    """
    if new_password:
        # If we're sending a password reset email with the new password
        user_name = user.get_full_name() or user.first_name or 'Administrador'
        user_message = (
            f"Hola {user_name},\n\n"
            f"Hemos recibido una solicitud para restablecer tu contraseña del panel de administración de Mas Seguros.\n\n"
            f"Tu nueva contraseña temporal es: {new_password}\n\n"
            f"Por motivos de seguridad, te recomendamos encarecidamente:\n"
            f"1. Iniciar sesión con esta contraseña temporal\n"
            f"2. Cambiar inmediatamente tu contraseña por una de tu elección\n"
            f"3. No compartir esta información con nadie\n\n"
            f"Si no solicitaste este cambio, contacta inmediatamente al equipo de soporte.\n\n"
            f"Saludos,\n"
            f"El equipo de Mas Seguros\n"
            f"Panel de Administración"
        )
    else:
        # Default message with verification code
        user_name = user.get_full_name() or user.first_name or 'Usuario'
        user_message = f"Hola {user_name}, aquí está tu código de verificación: {user.userprofile.verification_code}"

    message = Mail(
        from_email=settings.EMAIL_HOST_USER,
        to_emails=str(user.email),
        subject=subject if subject else "Mas Seguros",
        plain_text_content=user_message
    )

    try:
        sg = SendGridAPIClient(settings.SENDGRID_API_KEY)
        response = sg.send(message)
        return response.status_code
    except Exception as e:
        print(e)
        return False

    # return send_mail(
    #     subject if subject else "Mas Seguros",
    #     message,
    #     settings.EMAIL_HOST_USER,
    #     [user.email],
    #     fail_silently=False,
    # )


def logger(message: str = "", frame=None):
    """Logs specified message.

    Args:
        message: A message to log.
        frame: A frame object from the call stack.

    See:
        https://docs.python.org/3/library/sys.html#sys._getframe
    """
    function = None
    location = None

    if frame is None:
        try:
            frame = sys._getframe()
        except:
            pass

    if frame is not None:
        try:
            previous = frame.f_back
            function = previous.f_code.co_name
            location = "%s:%s" % (
                previous.f_code.co_filename, previous.f_lineno)
        except:
            pass
    sys.stderr.write("[%s] [%s] %s\r\n" %
                     (function, location, message))
