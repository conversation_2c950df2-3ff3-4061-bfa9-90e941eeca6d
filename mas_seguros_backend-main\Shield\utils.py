import string
import random
from . import models as shield_models
from math import sin, cos, sqrt, atan2, radians


def create_shield_join_code():
    res = ''.join(random.choices(string.ascii_uppercase, k=2))
    res1 = ''.join(random.choices(string.digits, k=2))
    return '{}'.format(str(res) + str(res1))


def assign_another_super_admin(shield, request):
    try:
        shield.shield_super_admin = shield.admin.all().last()
    except:
        try:
            shield.shield_super_admin = shield_models.Hierarchie.objects.filter(shield=shield,
                                                                                hierarchy=shield_models.Fantasma).last()
        except:
            try:
                shield.shield_super_admin = shield_models.Hierarchie.objects.filter(shield=shield,
                                                                                    hierarchy=shield_models.Solitario).last()
            except:
                try:
                    shield.shield_super_admin = shield_models.Hierarchie.objects.filter(shield=shield,
                                                                                        hierarchy=shield_models.Colaborativo).last()
                except:
                    try:
                        shield.shield_super_admin = shield.members.last()
                    except:
                        pass
    shield.save()
    return True


def populate_hierarchies_tables(shield, user):
    shield_models.Hierarchie.objects.create(shield=shield, member=user, hierarchy=shield_models.Fantasma)


def populate_walkie_talkie_tables(shield, user):
    shield_models.WalkieTalkie.objects.create(shield=shield, member=user)


def check_hierarchy_exist(hierarchy):
    if hierarchy == shield_models.Colaborativo or hierarchy == shield_models.Solitario or hierarchy == shield_models.Fantasma:
        return True
    return False


def add_hierarchy_of_member(shield_obj, member_obj):
    shield_models.Hierarchie.objects.create(shield=shield_obj, member=member_obj, hierarchy=shield_models.Colaborativo)


def remove_member_from_hierarchy(shield, userprofile):
    shield_models.Hierarchie.objects.filter(shield=shield, member=userprofile).last().delete()


def shield_code_already_occupied(code):
    check_code = shield_models.ShieldModel.objects.filter(shield_joining_code=code)
    print("======contains=====", check_code)
    try:
        if len(check_code) > 1:
            code = create_shield_join_code()
            shield_code_already_occupied(code)
    except:
        return code
    return code


def check_shield_code(shield):
    code = shield_code_already_occupied(shield.shield_joining_code)
    return code


def check_user_in_shield(shield_id, user_obj):
    shield = shield_models.ShieldModel.objects.filter(id=shield_id).last()
    shield_member = shield.members.filter(user=user_obj)
    if shield_member.exists():
        print("exist")
        return True
    else:
        print("not exist")
        return False


def check_shield_exist(shield_id):
    shield = shield_models.ShieldModel.objects.filter(id=shield_id)
    if shield.exists():
        print("exist")
        return True
    else:
        print("not exist")
        return False


def calculate_distance(lat1, lon1, lat2, lon2):
    R = 6371  # Radius of the earth in km
    dLat = radians(lat2 - lat1)
    dLon = radians(lon2 - lon1)
    a = sin(dLat / 2) * sin(dLat / 2) + cos(radians(lat1)) \
        * cos(radians(lat2)) * sin(dLon / 2) * sin(dLon / 2)
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = R * c * 1000  # Distance in meters
    return distance


def check_point_of_interest_exists(shield, request):
    lat = float(request.data.get('point_of_interest_lat'))
    long = float(request.data.get('point_of_interest_long'))
    tag = request.data.get('point_of_interest_tag')
    shield: shield_models.ShieldModel
    print("these are all the locations of the shoeld===", shield.locations.all())
    check = False
    for i in shield.locations.all():
        distance = calculate_distance(float(i.poi_lat), float(i.poi_long), float(lat), float(long))
        print("this is distance==", distance)
        if distance < 10:
            check = True
            break
    if check:
        return False, "La ubicación ya existe en otro Punto de Interés"
    tag_name_exists = shield.locations.filter(poi_tag_name=tag)
    if tag_name_exists:
        return False, 'El nombre de Etiqueta ya existe en otro Punto de Interés'

    return True, "all good"


def check_point_of_interest_exists_edit(shield, request, point_of_interest):
    lat = str(request.data.get('point_of_interest_lat'))
    long = str(request.data.get('point_of_interest_long'))
    tag = request.data.get('point_of_interest_tag')
    shield: shield_models.ShieldModel
    point_of_interest: shield_models.PointsOfInterest
    print("point of interest tag name", point_of_interest.poi_tag_name)
    print("point of interest tag name type", type(point_of_interest.poi_tag_name))
    print("point of interest lat name", point_of_interest.poi_lat)
    print("point of interest lat name type", type(point_of_interest.poi_lat))
    print("point of interest long name", point_of_interest.poi_long)
    print("point of interest long name type", type(point_of_interest.poi_long))
    print("===============")

    print("point of interest tag name", tag)
    print("point of interest tag name type", type(tag))
    print("point of interest lat name", lat)
    print("point of interest lat name type", type(lat))
    print("point of interest long name", long)
    print("point of interest long name type", type(long))

    if point_of_interest.poi_tag_name == tag and point_of_interest.poi_lat == lat and point_of_interest.poi_long == long:
        print("=====0====")
        return True, "all good"
    if point_of_interest.poi_tag_name == tag and point_of_interest.poi_lat != lat and point_of_interest.poi_long != long:
        print("========1=======")
        check = False
        for i in shield.locations.all():
            distance = calculate_distance(float(i.poi_lat), float(i.poi_long), float(lat), float(long))
            print("this is distance==", distance)
            if distance < 10:
                check = True
                break
        if check:
            return False, "La ubicación ya existe en otro Punto de Interés"
    if point_of_interest.poi_tag_name != tag and point_of_interest.poi_lat == lat and point_of_interest.poi_long == long:
        print("========2=======")
        tag_name_exists = shield.locations.filter(poi_tag_name=tag)
        if tag_name_exists:
            return False, 'El nombre de Etiqueta ya existe en otro Punto de Interés'
    if point_of_interest.poi_tag_name != tag and point_of_interest.poi_lat != lat and point_of_interest.poi_long != long:
        print("========3=======")
        check = False
        for i in shield.locations.all():
            distance = calculate_distance(float(i.poi_lat), float(i.poi_long), float(lat), float(long))
            print("this is distance==", distance)
            if distance < 10:
                check = True
                break
        if check:
            return False, "La ubicación ya existe en otro Punto de Interés"
        tag_name_exists = shield.locations.filter(poi_tag_name=tag)
        if tag_name_exists:
            return False, 'El nombre de Etiqueta ya existe en otro Punto de Interés'
    return True, "all good"


def check_point_of_interest_exists_in_making_location(shield, lat, long):
    """
    Check if the given coordinates are near any POI in the shield.
    Returns the POI object if found within 50 meters, None otherwise.
    """
    shield: shield_models.ShieldModel
    
    try:
        lat_float = float(lat)
        long_float = float(long)
    except (ValueError, TypeError):
        return None
        
    point_of_interest = None
    for poi in shield.locations.all():
        try:
            poi_lat = float(poi.poi_lat)
            poi_long = float(poi.poi_long)
            distance = calculate_distance(poi_lat, poi_long, lat_float, long_float)
            
            if distance < 50:  # Within 50 meters of POI
                point_of_interest = poi
                break
        except (ValueError, TypeError) as e:
            continue
    
    return point_of_interest
