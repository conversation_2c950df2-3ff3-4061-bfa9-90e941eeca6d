import React from "react";
import { useRouter } from "next/router";
import UserLayout from "@/components/layouts/UserLayout";
import CurrentLocationCard from "@/components/users/user/CurrentLocationCard";
import LocationHistoryCard from "@/components/users/user/LocationHistoryCard";

const User = () => {
  const router = useRouter();
  const { user_id } = router.query;

  return (
    <UserLayout pageTitle="Usuarios" headerTitle="Usuarios">
      <div className="mt-4 grid grid-cols-1 gap-5 lg:grid-cols-2">
        <CurrentLocationCard userId={user_id} />
        <LocationHistoryCard userId={user_id} />
      </div>
    </UserLayout>
  );
};

export default User;
