import React from "react";
import UserLayout from "@/components/layouts/UserLayout";
import Table from "@/components/Table";
import Pagination from "@/components/Pagination";
import Link from "next/link";
import { useRouter } from "next/router";
import useAxios from "@/hooks/useAxios";
import { useQuery } from "react-query";
import { format } from "date-fns";
import Badge from "@/components/Badge";
import ProfilePicture from "@/components/ProfilePicture";

const pageSize = 10;

const Shields = () => {
  const router = useRouter();
  const { user_id } = router.query;
  const { axios } = useAxios();
  const [currentPage, setCurrentPage] = React.useState(1);

  const fetchUserShields = async () => {
    try {
      const response = await axios.get(`adminside/api/dashboard/user-shields/`, {
        params: { user_id }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching user shields:", error);
      return { data: [] };
    }
  };

  const fetchUserHierarchies = async () => {
    try {
      // This endpoint is not needed anymore as we'll get all the information from the user shields endpoint
      return { data: [] };
    } catch (error) {
      console.error("Error fetching user hierarchies:", error);
      return { data: [] };
    }
  };

  const {
    data: shieldsData,
    isLoading: isLoadingShields,
    isError: isErrorShields
  } = useQuery(
    [`user-${user_id}-shields`],
    fetchUserShields,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  const {
    data: hierarchiesData,
    isLoading: isLoadingHierarchies
  } = useQuery(
    [`user-${user_id}-hierarchies`],
    fetchUserHierarchies,
    {
      enabled: !!user_id,
      refetchOnWindowFocus: false,
    }
  );

  const shields = shieldsData?.data || [];
  const hierarchies = hierarchiesData?.data || [];

  // Get hierarchy for a specific shield and user
  const getHierarchy = (shield) => {
    // Since we're using the adminside API, we don't have hierarchy information directly
    // We'll just return "Estándar" as a default value
    return "Colaborativo";
  };

  // Pagination logic
  const indexOfLastShield = currentPage * pageSize;
  const indexOfFirstShield = indexOfLastShield - pageSize;
  const currentShields = shields.slice(indexOfFirstShield, indexOfLastShield);

  return (
    <UserLayout pageTitle="Usuarios" headerTitle="Usuarios">
      <div className="mt-5">
        {isLoadingShields ? (
          <div className="text-center py-10">Cargando escudos...</div>
        ) : isErrorShields ? (
          <div className="text-center py-10 text-red-500">Error al cargar los escudos</div>
        ) : shields.length === 0 ? (
          <div className="text-center py-10">Este usuario no pertenece actualmente a ningún escudo.</div>
        ) : (
          <>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Escudo</Table.Th>
                  <Table.Th>Administrador</Table.Th>
                  <Table.Th>Fecha Creación</Table.Th>
                  <Table.Th>Usuario en escudo</Table.Th>
                  <Table.Th>Jerarquía</Table.Th>
                  <Table.Th>N° Miembros</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {currentShields.map((shield) => (
                  <Table.Tr key={shield.id}>
                    <Table.Td>
                      <div className="flex min-w-fit items-center gap-4">
                        <ProfilePicture
                          src={shield?.logo ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${shield.logo}` : null}
                          className="block aspect-square w-11 rounded-full object-cover"
                          alt={shield.shield_name}
                        />
                        <div>
                          <p className="font-semibold capitalize">{shield.shield_name}</p>
                          <p>{shield.shield_code}</p>
                        </div>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <p className="capitalize">{shield.admin?.full_name || shield.admin?.user?.first_name || "N/A"}</p>
                      <p>ID {shield.admin?.id || shield.admin?.user?.id || "N/A"}</p>
                    </Table.Td>
                    <Table.Td>
                      {shield.created_at ? format(new Date(shield.created_at), 'dd/MM/yy') : 'N/A'}
                    </Table.Td>
                    <Table.Td>Estándar</Table.Td>
                    <Table.Td>
                      <Badge.Md
                        className="bg-warning bg-opacity-20 text-warning"
                        text={getHierarchy(shield)}
                      />
                    </Table.Td>
                    <Table.Td>
                      <div className="flex items-center gap-5">
                        <span>{shield.members_count || 0}</span>
                        <Link
                          href={`/shields/${shield.id}`}
                          className="font-semibold text-primary hover:underline"
                        >
                          Ver detalles
                        </Link>
                      </div>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
            {shields.length > pageSize && (
              <div className="mt-4">
                <Pagination
                  totalCount={shields.length}
                  currentPage={currentPage}
                  pageSize={pageSize}
                  onPageChange={setCurrentPage}
                />
              </div>
            )}
          </>
        )}
      </div>
    </UserLayout>
  );
};

export default Shields;
