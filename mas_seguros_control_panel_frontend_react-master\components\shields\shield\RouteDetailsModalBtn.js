import ProfilePicture from "@/components/ProfilePicture";
import SectionHeading from "@/components/SectionHeading";
import Table from "@/components/Table";
import Modal from "@/components/utility/Modal";
import { format } from "date-fns";
import React, { createElement, useState } from "react";
import GoogleMap from "@/components/maps/GoogleMap";

const RouteDetailsModalBtn = ({ as = "button", route = {}, ...props }) => {
  const [open, setOpen] = useState(false);

  const close = () => {
    setOpen(false);
  };

  // Debug: Log route data to console (can be removed in production)
  React.useEffect(() => {
    if (route && Object.keys(route).length > 0) {
      console.log('Route data received:', {
        route_id: route.route_id,
        route_starting_time: route.route_starting_time,
        route_ending_time: route.route_ending_time,
        starting_poi: route.starting_poi,
        ending_poi: route.ending_poi,
        location_count: route.location?.length || 0,
        sample_location: route.location?.[0]?.location
      });

      // Test time formatting
      const startTime = safeFormatDate(route.route_starting_time, 'HH:mm');
      const endTime = safeFormatDate(route.route_ending_time, 'HH:mm');
      console.log('Time formatting test:', {
        raw_start: route.route_starting_time,
        raw_end: route.route_ending_time,
        formatted_start: startTime,
        formatted_end: endTime
      });
    }
  }, [route]);

  // Helper function to safely format dates
  const safeFormatDate = (dateValue, formatString) => {
    if (!dateValue) return 'N/A';
    try {
      // Handle time-only values like "12:55:33.029316"
      if (typeof dateValue === 'string' && dateValue.match(/^\d{2}:\d{2}:\d{2}/)) {
        // For time-only values, create a date with today's date
        const today = new Date().toISOString().split('T')[0];
        const fullDateTime = `${today}T${dateValue}`;
        const date = new Date(fullDateTime);
        if (!isNaN(date.getTime())) {
          return format(date, formatString);
        }
      }

      const date = new Date(dateValue);
      if (isNaN(date.getTime())) {
        console.log('Failed to parse date:', dateValue);
        return 'N/A';
      }
      return format(date, formatString);
    } catch (error) {
      console.log('Date formatting error:', error, 'for value:', dateValue);
      return 'N/A';
    }
  };

  const handleDownloadRoute = () => {
    try {
      // Create route data for download
      const routeData = {
        route_id: route.route_id,
        member: route.member?.full_name || 'N/A',
        date: safeFormatDate(route.route_date, 'dd/MM/yyyy'),
        start_time: safeFormatDate(route.route_starting_time, 'HH:mm'),
        end_time: safeFormatDate(route.route_ending_time, 'HH:mm'),
        max_speed: route.max_speed || 'N/A',
        min_battery: route.minimum_phone_battery || 'N/A',
        starting_point: route.starting_poi || route.location?.[0]?.location || 'N/A',
        ending_point: route.ending_poi || route.location?.[route.location?.length - 1]?.location || 'N/A',
        locations: route.location || []
      };

      // Create CSV content
      let csvContent = "data:text/csv;charset=utf-8,";
      csvContent += "Información de la Ruta\n";
      csvContent += `ID de Ruta,${routeData.route_id}\n`;
      csvContent += `Miembro,${routeData.member}\n`;
      csvContent += `Fecha,${routeData.date}\n`;
      csvContent += `Hora de Inicio,${routeData.start_time}\n`;
      csvContent += `Hora de Fin,${routeData.end_time}\n`;
      csvContent += `Velocidad Máxima,${routeData.max_speed} km/h\n`;
      csvContent += `Batería Mínima,${routeData.min_battery}%\n`;
      csvContent += `Punto de Origen,${routeData.starting_point}\n`;
      csvContent += `Punto de Destino,${routeData.ending_point}\n\n`;

      csvContent += "Detalle de Ubicaciones\n";
      csvContent += "Hora,Fecha,Ubicación,Latitud,Longitud\n";

      routeData.locations.forEach(location => {
        const time = safeFormatDate(location.created_at, 'HH:mm');
        const date = safeFormatDate(location.created_at, 'dd/MM/yyyy');
        const locationText = (location.location || 'N/A').replace(/,/g, ';'); // Replace commas to avoid CSV issues
        csvContent += `${time},${date},"${locationText}",${location.lat || 'N/A'},${location.long || 'N/A'}\n`;
      });

      // Create and download file
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `ruta_${route.route_id}_${routeData.date.replace(/\//g, '-')}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('Error downloading route:', error);
      alert('Error al descargar la ruta. Por favor, inténtalo de nuevo.');
    }
  };

  return (
    <>
      <Modal
        open={open}
        close={close}
        className="w-full max-w-screen-lg overflow-hidden rounded bg-white shadow-xl"
      >
        <Modal.Wrapper>
          <Modal.Header className="bg-accent">
            <h2 className="text-lg font-medium">Ruta #{route.route_id}</h2>
            <Modal.XBtn onClick={close} />
          </Modal.Header>
          <Modal.Body className="grid grid-cols-1 gap-4 py-6 lg:grid-cols-2">
            <div className="space-y-5">
              <div className="flex gap-4 text-sm">
                <ProfilePicture
                  src={route.member?.image ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${route.member?.image}` : null}
                  className="inline-block h-11 w-11 rounded-full object-cover"
                />
                <div>
                  <dd className="capitalize">{route.member?.full_name}</dd>
                  <dd>ID: {route.member?.user?.id}</dd>
                </div>
              </div>
              <div className="aspect-video w-full bg-accent">
                {route.location && route.location.length > 0 ? (
                  <GoogleMap
                    lat={route.location[0]?.lat || route.starting_lat}
                    lng={route.location[0]?.long || route.starting_long}
                    zoom={13}
                    className="w-full h-full"
                    showMarker={false}
                    showRoute={true}
                    routePoints={route.location?.map((loc) => ({
                      lat: parseFloat(loc.lat),
                      lng: parseFloat(loc.long),
                      location: loc.location || 'Ubicación'
                    })).filter(point => point.lat && point.lng && !isNaN(point.lat) && !isNaN(point.lng))}
                    userImage={route.member?.image ? `${process.env.NEXT_PUBLIC_BACKEND_URL}${route.member?.image}` : null}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <div className="text-4xl mb-2">🗺️</div>
                    <p className="text-center text-sm">No hay datos de ubicación disponibles para mostrar la ruta</p>
                  </div>
                )}
              </div>
              <div>
                <SectionHeading>
                  {(() => {
                    const startTime = safeFormatDate(route.route_starting_time, 'HH:mm');
                    const endTime = safeFormatDate(route.route_ending_time, 'HH:mm');

                    if (startTime !== 'N/A' && endTime !== 'N/A') {
                      return `${startTime} hrs - ${endTime} hrs`;
                    } else if (startTime !== 'N/A') {
                      return `${startTime} hrs - En curso`;
                    } else {
                      return 'Horario no disponible';
                    }
                  })()}
                </SectionHeading>
                <dd className="mt-1">
                  {safeFormatDate(route.route_date, 'dd/MM/yyyy') !== 'N/A' ?
                   safeFormatDate(route.route_date, 'dd/MM/yyyy') :
                   safeFormatDate(route.created_at, 'dd/MM/yyyy') !== 'N/A' ?
                   safeFormatDate(route.created_at, 'dd/MM/yyyy') : 'Fecha no disponible'}
                </dd>
              </div>

              <div>
                <div className="relative flex items-center">
                  <div className="w-4">
                    <div className="absolute inset-y-0 flex items-center">
                      <span className="z-[1] inline-block h-2 w-2 rounded-full bg-black " />
                      <span
                        className="absolute top-4 left-[3px] h-[calc(100%+4px)] w-0.5 bg-gray-300"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                  <div className="py-2">
                    <div className="font-medium text-black">
                      {route.starting_poi || 'No disponible'}
                    </div>
                  </div>
                </div>
                <div className="relative flex items-center">
                  <div className="w-4">
                    <div className="absolute inset-y-0 flex items-center">
                      <span className="z-[1] inline-block h-2 w-2 rounded-full bg-blue-600 " />
                    </div>
                  </div>
                  <div className="py-2">
                    <div className="font-medium text-black">
                      {route.ending_poi || 'No disponible'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-x-5 text-sm">
                  <dd className="font-semibold">Velocidad máx</dd>
                  <dd className="font-semibold">Batería mínima</dd>
                  <dd>{route.max_speed || 'N/A'} km/h</dd>
                  <dd>{route.minimum_phone_battery || 'N/A'}%</dd>
                </div>
              </div>
            </div>

            <Table wrapperClassName="bg-accent px-4 h-full lg:max-h-full h-fit lg:h-auto lg:overflow-auto">
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Horario</Table.Th>
                  <Table.Th>Ubicación</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {route.location?.map((location) => (
                  <Table.Tr key={location.id}>
                    <Table.Td>
                      <dd className="font-medium">{safeFormatDate(location.created_at, 'HH:mm')} hrs</dd>
                      <dd className="text-xs text-gray-500">{safeFormatDate(location.created_at, 'dd/MM/yyyy')}</dd>
                    </Table.Td>
                    <Table.Td className="!whitespace-normal">
                      <div className="text-sm">
                        {location.location || 'Ubicación no disponible'}
                      </div>
                    </Table.Td>
                  </Table.Tr>
                ))}
                {(!route.location || route.location.length === 0) && (
                  <Table.Tr>
                    <Table.Td colSpan={2} className="text-center py-4 text-gray-500">
                      No hay datos de ubicación disponibles para esta ruta
                    </Table.Td>
                  </Table.Tr>
                )}
              </Table.Tbody>
            </Table>
          </Modal.Body>
          <Modal.Footer className="bg-accent">
            <Modal.FooterBtn onClick={close} className="bg-white text-black">
              Cancelar
            </Modal.FooterBtn>
            <Modal.FooterBtn onClick={handleDownloadRoute} className="bg-black text-white">
              Descargar Ruta
            </Modal.FooterBtn>
          </Modal.Footer>
        </Modal.Wrapper>
      </Modal>
      {createElement(as, {
        ...props,
        onClick: () => setOpen(true),
      })}
    </>
  );
};

export default RouteDetailsModalBtn;
