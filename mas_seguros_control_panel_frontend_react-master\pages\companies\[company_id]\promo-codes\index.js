import React from "react";
import Table from "@/components/Table";
import CompanyLayout from "@/components/layouts/CompanyLayout";
import useTableData from "@/hooks/useTableData";
import { useRouter } from "next/router";
import Pagination from "@/components/Pagination";
const pageSize = 10;

export default function PromoCodes() {
  const router = useRouter();
  const { company_id } = router.query;

  const {
    currentTableData,
    isLoading,
    isError,
    error,
    allData,
    currentPage,
    setCurrentPage,
    isSuccess,
  } = useTableData({
    baseURL: process.env.NEXT_PUBLIC_BACKEND_URL_2,
    noAuth: false,
    dataCallback: (response) => {
      // The API returns: { data: { status_code, success, message, data: [...] } }
      // We need to access response.data.data for the actual promo codes array
      if (response?.data?.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else if (response?.data && Array.isArray(response.data)) {
        return response.data;
      }
      return [];
    },
    dataUrl: `adminside/api/company/company-promocodes/?id=${company_id}`,
    pageSize: pageSize,
    queryKeys: [`company-${company_id}-promocodes-table-data`],
    enabled: !!company_id,
  })



  return (
    <CompanyLayout pageTitle="Empresas" headerTitle="Empresas">
      <div className="mt-5">
        <Table
          dataCount={currentTableData.length}
          isLoading={isLoading}
          isError={isError}
          error={error}
        >

          <Table.Thead>
            <Table.Tr>
              <Table.Th>ID de código</Table.Th>
              <Table.Th>Código de promo</Table.Th>
              <Table.Th>Membresía</Table.Th>
              <Table.Th>% de descuento</Table.Th>
              <Table.Th>Stock total</Table.Th>
              <Table.Th>Etiqueta</Table.Th>
              <Table.Th>Estado</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {isSuccess && currentTableData.map((promoCode) => (
              <Table.Tr key={promoCode.id}>
                <Table.Td>{promoCode.code_id || 'N/A'}</Table.Td>
                <Table.Td>{promoCode.promo_code || 'N/A'}</Table.Td>
                <Table.Td>{promoCode.membership || 'N/A'}</Table.Td>
                <Table.Td>{promoCode.discount ? `${promoCode.discount}%` : 'N/A'}</Table.Td>
                <Table.Td>{promoCode.stocks || 'N/A'}</Table.Td>
                <Table.Td>{promoCode.label || promoCode.Etiquette || 'N/A'}</Table.Td>
                <Table.Td>
                  <span className={`inline-flex items-center rounded-full px-3 py-1.5 text-sm font-semibold ${
                    promoCode.state
                      ? 'bg-green-100 text-green-600'
                      : 'bg-red-100 text-red-600'
                  }`}>
                    <svg
                      className={`mr-1.5 h-2 w-2 ${
                        promoCode.state ? 'text-green-600' : 'text-red-600'
                      }`}
                      fill="currentColor"
                      viewBox="0 0 8 8"
                    >
                      <circle cx={5} cy={4} r={3} />
                    </svg>
                    {promoCode.state ? 'Activo' : 'Inactivo'}
                  </span>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
        {isSuccess && (
          <Pagination
            className="mt-3.5"
            totalCount={allData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
          />
        )}
      </div>
    </CompanyLayout>
  );
}
