from django.urls import path, reverse

from . import views as dashboard_views

urlpatterns = [
    path("users/", dashboard_views.ListUsers.as_view()),
    path("suspend_users/", dashboard_views.SuspendUsers.as_view()),
    path("user-locations/", dashboard_views.UserLocations.as_view()),
    path('user-shields/', dashboard_views.UserShields.as_view()),
    path("registered-users/", dashboard_views.RegisteredUsers.as_view()),
    path("purchased-memberships-according-months/", dashboard_views.PurchasedMemberships.as_view()),
    path("created-shields-according-months/", dashboard_views.ShieldsCreated.as_view()),
    path("generated-alerts-with-sos-according-months/", dashboard_views.AlertsGeneratedWithSOS.as_view()),
    path("generated-alerts-with-expiry-according-months/", dashboard_views.AlertsGeneratedWithExpiry.as_view()),
    path("registered-users-per-month/", dashboard_views.UserRegisteredPerMonth.as_view()),
    path("created-shields-per-month/", dashboard_views.SheildsCreatedPerMonth.as_view()),
    path("created-tickets-according-months/", dashboard_views.TicketsCreated.as_view()),
    path("created-promo-code-according-months/", dashboard_views.PromoCode.as_view()),
    path("user-biometric-report-download-single-user/", dashboard_views.UserBiometricReportDownload.as_view()),
    path("download-excel-file-for-dashboard/", dashboard_views.DownloadExcelFileDashboard.as_view()),
    path("download-pdf-file-for-dashboard/", dashboard_views.DownloadPdfFileDashboard.as_view()),

    # path("usersregisteredpermonthgraph/", dashboard_views.UsersRegisteredPerMonthGraph.as_view()),

]
